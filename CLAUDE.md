# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Telegram bot for managing and analyzing casino game records. It supports data entry, querying, statistical analysis, and automatic report generation. The bot can parse natural language input, store data in Excel files and Google Sheets, and generate various reports and charts.

## Architecture

The application follows a modular architecture with these key components:

1. **Main Application** (`Group_record.py`, `main_with_monitoring.py`): Core Telegram bot implementation with message handling and command processing
2. **Data Models** (`data_models.py`): Core data structures for game records, rebate rules, and salary rules
3. **Message Parsing** (`Parser.py`): Natural language message parsing with support for multiple date formats
4. **Storage Layer** (`storage.py`, `storage_service.py`): Data persistence with Excel and Google Sheets integration
5. **Command Processing** (`commander.py`): Command handlers for various bot functions
6. **Configuration Management** (`config.py`, `config_cache.py`): Application configuration and caching
7. **Job Scheduling** (`job.py`): Scheduled tasks for reports and data synchronization
8. **Salary Calculation** (`salary.py`): Automatic salary computation based on game results
9. **Charts and Reports** (`charts.py`, `report.py`): Data visualization and report generation
10. **Alias Management** (`alias_manager.py`): Support for aliases in person names, venues, and games
11. **Error Handling** (`error_strategy.py`, `exceptions.py`): Comprehensive error handling with retry mechanisms
12. **Message Caching** (`message_cache.py`): Cache for messages that fail to sync to Google Sheets
13. **Monitoring** (`monitoring.py`, `health_check.py`): Application health monitoring and metrics
14. **Security** (`security_utils.py`, `input_validator.py`): Input validation and security utilities

## Common Development Tasks

### Running the Application
- Development: `python Group_record.py`
- Production with monitoring: `python main_with_monitoring.py`
- Docker: `docker-compose up`

### Testing
- Run all tests: `python run_tests.py --all`
- Run unit tests: `python run_tests.py --unit`
- Run integration tests: `python run_tests.py --integration`
- Run with coverage: `python run_tests.py --coverage`
- Run specific test: `python run_tests.py --test tests/unit/test_example.py`

### Linting and Formatting
- Check dependencies: `python run_tests.py --check-deps`
- Install test dependencies: `python run_tests.py --install-deps`
- Run linting: `python run_tests.py --lint`
- Format code: `python run_tests.py --format`

### Configuration
- Environment variables are loaded from `.env` file
- Configuration is managed in `config.py`
- Alias configuration is in `aliases.json`
- Rebate configuration is in `rebate_config.json`
- Salary configuration is in `salary_config.json`

### Deployment
- Windows: Use `deploy.bat` script
- Docker: Use `docker-compose.yml`
- Manual deployment requires Python 3.8+, pip dependencies, and proper environment setup

## Key Features

1. **Natural Language Processing**: Parse game records from natural language text
2. **Data Storage**: Store data in both local Excel files and Google Sheets
3. **Automatic Calculations**: Compute rebate amounts and salaries automatically
4. **Smart Matching**: Intelligent record matching for corrections
5. **Caching**: Cache messages that fail to sync to Google Sheets with automatic retry
6. **Scheduled Reports**: Generate daily, weekly, and monthly reports automatically
7. **Charts**: Generate various charts for data visualization
8. **Alias System**: Support for aliases in person names, venues, and games
9. **Configuration Management**: Flexible configuration with caching
10. **Monitoring**: Health checks and performance metrics

## Important Implementation Details

1. **Error Handling**: Uses a strategy pattern with decorators for consistent error handling
2. **Async Operations**: Uses asyncio for non-blocking operations
3. **Caching**: Implements both data caching and configuration caching
4. **Security**: Input validation and sanitization for all user inputs
5. **Retry Logic**: Automatic retry for failed Google Sheets operations
6. **Logging**: Comprehensive logging with rotation
7. **Testing**: Comprehensive test suite with unit and integration tests