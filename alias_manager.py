import difflib
import json
import os
import logging
from typing import Dict, Any, Optional, List, Tuple

from telegram import Update
from telegram.ext import ContextTypes

from config import Config
from input_validator import SecurityValidator, SecureFileManager, SecurityError

logger = logging.getLogger(__name__)

ALIAS_FILE = Config.ALIAS_FILE

# 安全加载别名数据
def _load_alias_data() -> Dict[str, Any]:
    """安全加载别名数据"""
    try:
        return SecureFileManager.safe_load_json(ALIAS_FILE)
    except FileNotFoundError:
        logger.warning(f"别名文件不存在，创建默认文件: {ALIAS_FILE}")
        default_data = {
            "fields": {},
            "games": {},
            "persons": {},
            "venues": {}
        }
        SecureFileManager.safe_save_json(default_data, ALIAS_FILE, backup=False)
        return default_data
    except SecurityError as e:
        logger.error(f"别名文件安全验证失败: {e}")
        raise
    except Exception as e:
        logger.error(f"加载别名文件失败: {e}")
        # 返回默认数据以保证系统正常运行
        return {
            "fields": {},
            "games": {},
            "persons": {},
            "venues": {}
        }

# 初始化别名数据
alias_data = _load_alias_data()
FIELD_ALIASES = alias_data.get("fields", {})
GAME_ALIASES = alias_data.get("games", {})
PERSON_ALIASES = alias_data.get("persons", {})
LOCATION_ALIASES = alias_data.get("venues", {})


def resolve_with_aliases(value: str, alias_dict: dict, fallback=None) -> str | None:
    """
    尝试解析 value 到 alias_dict 中的标准名称。
    - 优先匹配标准名或别名（大小写不敏感）
    - 否则尝试模糊匹配（difflib）
    - 如果都失败，则返回清洗后的 value_clean 本身
    """
    if not value:
        return fallback

    # 输入清理和验证
    value_clean = SecurityValidator.sanitize_input(value, max_length=100).strip().lower()
    if not value_clean:
        return fallback
    
    mapping = {std.lower(): std for std in alias_dict}

    for std, aliases in alias_dict.items():
        for alias in aliases:
            # 清理别名数据
            clean_alias = SecurityValidator.sanitize_input(alias, max_length=100).strip().lower()
            if clean_alias:
                mapping[clean_alias] = std

    if value_clean in mapping:
        return mapping[value_clean]

    close = difflib.get_close_matches(value_clean, mapping.keys(), n=1, cutoff=0.7)
    if close:
        return mapping.get(close[0], fallback)

    return value_clean


def reload_aliases():
    """安全重新加载别名数据"""
    try:
        return SecureFileManager.safe_load_json(ALIAS_FILE)
    except Exception as e:
        logger.error(f"重新加载别名文件失败: {e}")
        # 返回当前内存中的数据作为后备
        return {
            "fields": FIELD_ALIASES,
            "games": GAME_ALIASES,
            "persons": PERSON_ALIASES,
            "venues": LOCATION_ALIASES
        }


def add_alias(category: str, standard_name: str, new_alias: str) -> bool:
    """安全添加别名"""
    try:
        # 输入验证和清理
        category = SecurityValidator.sanitize_input(category, max_length=50)
        standard_name = SecurityValidator.sanitize_input(standard_name, max_length=100)
        new_alias = SecurityValidator.sanitize_input(new_alias, max_length=100)
        
        if not all([category, standard_name, new_alias]):
            logger.warning("添加别名失败: 输入参数为空")
            return False
        
        # 验证字段类型
        valid_categories = ["fields", "games", "persons", "venues"]
        if category not in valid_categories:
            logger.warning(f"添加别名失败: 无效的类别 {category}")
            return False
        
        # 验证字段格式
        field_type = {"persons": "人员", "venues": "场子", "games": "游戏"}.get(category, "备注")
        
        is_valid, error = SecurityValidator.validate_field(field_type, standard_name)
        if not is_valid:
            logger.warning(f"添加别名失败: 标准名格式错误 - {error}")
            return False
            
        is_valid, error = SecurityValidator.validate_field(field_type, new_alias)
        if not is_valid:
            logger.warning(f"添加别名失败: 别名格式错误 - {error}")
            return False
        
        # 重新加载数据
        data = reload_aliases()
        if category not in data:
            logger.warning(f"添加别名失败: 类别不存在 {category}")
            return False
            
        category_dict = data[category]
        if standard_name not in category_dict:
            category_dict[standard_name] = []
            
        if new_alias not in category_dict[standard_name]:
            category_dict[standard_name].append(new_alias)
            
        # 安全保存文件
        SecureFileManager.safe_save_json(data, ALIAS_FILE, backup=True)
        
        # 更新内存中的数据
        global FIELD_ALIASES, GAME_ALIASES, PERSON_ALIASES, LOCATION_ALIASES
        FIELD_ALIASES = data.get("fields", {})
        GAME_ALIASES = data.get("games", {})
        PERSON_ALIASES = data.get("persons", {})
        LOCATION_ALIASES = data.get("venues", {})
        
        logger.info(f"成功添加别名: {category}/{standard_name} <- {new_alias}")
        return True
        
    except Exception as e:
        logger.error(f"添加别名失败: {e}")
        return False


# ==================== 统一字段访问API ====================

def get_all_field_names() -> List[str]:
    """
    获取所有字段名称列表

    Returns:
        List[str]: 所有字段名称的列表，从aliases.json中的fields部分获取
    """
    try:
        data = reload_aliases()
        field_names = list(data.get("fields", {}).keys())
        logger.debug(f"获取所有字段名称: {field_names}")
        return field_names
    except Exception as e:
        logger.error(f"获取字段名称失败: {e}")
        # Fallback到硬编码列表
        fallback_fields = ["工作日期", "人员", "场子", "游戏", "卡号", "起始本金", "点码", "工资", "输返", "盈利", "备注"]
        logger.warning(f"使用fallback字段列表: {fallback_fields}")
        return fallback_fields


def get_numeric_field_names() -> List[str]:
    """
    获取所有数字字段名称列表

    Returns:
        List[str]: 数字字段名称的列表
    """
    try:
        all_fields = get_all_field_names()
        numeric_fields = [field for field in all_fields if is_numeric_field(field)]
        logger.debug(f"获取数字字段名称: {numeric_fields}")
        return numeric_fields
    except Exception as e:
        logger.error(f"获取数字字段名称失败: {e}")
        # Fallback到硬编码列表
        fallback_numeric = ["起始本金", "点码", "工资", "输返", "盈利"]
        logger.warning(f"使用fallback数字字段列表: {fallback_numeric}")
        return fallback_numeric


def get_required_field_names() -> List[str]:
    """
    获取所有必填字段名称列表

    Returns:
        List[str]: 必填字段名称的列表（排除备注字段）
    """
    try:
        all_fields = get_all_field_names()
        required_fields = [field for field in all_fields if field != "备注"]
        logger.debug(f"获取必填字段名称: {required_fields}")
        return required_fields
    except Exception as e:
        logger.error(f"获取必填字段名称失败: {e}")
        # Fallback到硬编码列表
        fallback_required = ["工作日期", "人员", "场子", "游戏", "卡号", "起始本金", "点码", "工资", "输返", "盈利"]
        logger.warning(f"使用fallback必填字段列表: {fallback_required}")
        return fallback_required


def is_numeric_field(field_name: str) -> bool:
    """
    判断字段是否为数字字段

    Args:
        field_name (str): 字段名称

    Returns:
        bool: 如果是数字字段返回True，否则返回False
    """
    if not field_name:
        return False

    # 数字字段关键词
    numeric_keywords = ["本金", "点码", "工资", "输反", "盈利", "卡号"]

    try:
        # 清理字段名并检查是否包含数字关键词
        clean_field = SecurityValidator.sanitize_input(field_name, max_length=100).strip()
        is_numeric = any(keyword in clean_field for keyword in numeric_keywords)
        logger.debug(f"字段 '{field_name}' 数字判断: {is_numeric}")
        return is_numeric
    except Exception as e:
        logger.error(f"判断字段类型失败: {e}")
        return False


def get_field_aliases(field_name: str) -> List[str]:
    """
    获取指定字段的所有别名

    Args:
        field_name (str): 字段名称

    Returns:
        List[str]: 该字段的别名列表，如果字段不存在则返回空列表
    """
    try:
        data = reload_aliases()
        field_aliases = data.get("fields", {})
        aliases = field_aliases.get(field_name, [])
        logger.debug(f"获取字段 '{field_name}' 的别名: {aliases}")
        return aliases if isinstance(aliases, list) else []
    except Exception as e:
        logger.error(f"获取字段别名失败: {e}")
        return []


async def reload_aliases_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """重新加载别名词典 - 需要管理员权限"""
    try:
        # 用户授权检查
        is_authorized, error_msg = SecurityValidator.check_user_authorization(update, required_level="admin")
        if not is_authorized:
            await update.message.reply_text(f"❌ 权限不足: {error_msg}")
            return
        
        # 重新加载别名
        reload_aliases()
        
        # 更新内存中的数据
        global alias_data, FIELD_ALIASES, GAME_ALIASES, PERSON_ALIASES, LOCATION_ALIASES
        alias_data = _load_alias_data()
        FIELD_ALIASES = alias_data.get("fields", {})
        GAME_ALIASES = alias_data.get("games", {})
        PERSON_ALIASES = alias_data.get("persons", {})
        LOCATION_ALIASES = alias_data.get("venues", {})
        
        await update.message.reply_text("✅ 别名词典已重新加载")
        logger.info(f"管理员 {update.effective_user.id} 重新加载了别名词典")
        
    except Exception as e:
        error_msg = f"❌ 重新加载别名词典失败: {str(e)}"
        await update.message.reply_text(error_msg)
        logger.error(f"重新加载别名词典失败: {e}")


async def add_aliases_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """添加别名 - 需要用户权限"""
    try:
        # 用户授权检查
        is_authorized, error_msg = SecurityValidator.check_user_authorization(update, required_level="user")
        if not is_authorized:
            await update.message.reply_text(f"❌ 权限不足: {error_msg}")
            return
        
        # 参数验证
        if len(context.args) != 3:
            await update.message.reply_text(
                "❗ 格式错误\n\n"
                "📋 正确格式：`/add_aliases 类别 标准名 别名`\n"
                "💡 示例：`/add_aliases persons 小明 xm`\n\n"
                "📂 支持的类别：\n"
                "• `fields` - 字段别名\n"
                "• `games` - 游戏别名\n" 
                "• `persons` - 人员别名\n"
                "• `venues` - 场子别名",
                parse_mode='Markdown'
            )
            return
        
        category, std, alias = context.args
        
        # 输入验证
        category = SecurityValidator.sanitize_input(category)
        std = SecurityValidator.sanitize_input(std)
        alias = SecurityValidator.sanitize_input(alias)
        
        if not all([category, std, alias]):
            await update.message.reply_text("❌ 参数不能为空或包含非法字符")
            return
        
        # 添加别名
        success = add_alias(category, std, alias)
        if success:
            await update.message.reply_text(f"✅ 添加成功：{category} / {std} ← {alias}")
            logger.info(f"用户 {update.effective_user.id} 添加别名: {category}/{std} <- {alias}")
        else:
            await update.message.reply_text(
                f"❌ 添加失败\n\n"
                f"📂 支持的类别：\n"
                f"• `fields` - 字段别名\n"
                f"• `games` - 游戏别名\n"
                f"• `persons` - 人员别名\n"
                f"• `venues` - 场子别名\n\n"
                f"⚠️ 请检查类别名称和输入格式是否正确",
                parse_mode='Markdown'
            )
            
    except Exception as e:
        error_msg = f"❌ 添加别名失败: {str(e)}"
        await update.message.reply_text(error_msg)
        logger.error(f"添加别名失败: {e}")


def validate_aliases_file() -> Tuple[bool, str]:
    """验证aliases.json文件的完整性和格式正确性

    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    try:
        if not os.path.exists(ALIAS_FILE):
            return False, f"别名文件不存在: {ALIAS_FILE}"

        data = SecureFileManager.safe_load_json(ALIAS_FILE)
        if not data:
            return False, "别名文件为空或格式错误"

        required_sections = ["fields", "games", "persons", "venues"]
        for section in required_sections:
            if section not in data:
                return False, f"缺少必需的section: {section}"
            if not isinstance(data[section], dict):
                return False, f"Section {section} 格式错误，应为字典"

        # 检查必需的字段
        required_fields = ["工作日期", "人员", "场子", "游戏", "卡号", "起始本金", "点码", "工资", "输反", "盈利", "备注"]
        fields_section = data["fields"]
        missing_fields = [field for field in required_fields if field not in fields_section]
        if missing_fields:
            return False, f"缺少必需的字段: {missing_fields}"

        logger.info("别名文件验证通过")
        return True, "验证通过"

    except Exception as e:
        error_msg = f"验证别名文件时出错: {e}"
        logger.error(error_msg)
        return False, error_msg


def create_aliases_backup() -> bool:
    """创建aliases.json的备份文件

    Returns:
        bool: 是否备份成功
    """
    try:
        if not os.path.exists(ALIAS_FILE):
            logger.warning("原始别名文件不存在，无法创建备份")
            return False

        from datetime import datetime
        backup_file = f"{ALIAS_FILE}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(ALIAS_FILE, backup_file)
        logger.info(f"别名文件备份成功: {backup_file}")
        return True

    except Exception as e:
        logger.error(f"创建别名文件备份失败: {e}")
        return False


def safe_get_field_with_fallback(field_type: str, fallback_list: List[str]) -> List[str]:
    """安全获取字段列表，带fallback机制

    Args:
        field_type: 字段类型 ("all" 或 "numeric")
        fallback_list: fallback列表

    Returns:
        List[str]: 字段列表
    """
    try:
        # 先验证文件完整性
        is_valid, error_msg = validate_aliases_file()
        if not is_valid:
            logger.warning(f"别名文件验证失败: {error_msg}，使用fallback")
            return fallback_list

        if field_type == "all":
            return get_all_field_names()
        elif field_type == "numeric":
            return get_numeric_field_names()
        else:
            logger.error(f"未知的字段类型: {field_type}")
            return fallback_list

    except Exception as e:
        logger.error(f"安全获取字段失败: {e}，使用fallback")
        return fallback_list
