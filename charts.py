import matplotlib
matplotlib.use('Agg')
from collections import defaultdict
from datetime import timedelta

from matplotlib import pyplot as plt
from config import Config
from storage import load_excel_data
from utils import group_sum, draw_bar_chart, send_chart_image, group_by_day_and_person, draw_trend_chart, \
    calculate_total_by_venue

TIME_ZONE = Config.TIME_ZONE  # 使用 zoneinfo 模块处理时区
EXCEL_FILE = Config.EXCEL_FILE
GROUP_CHAT_ID = Config.GROUP_CHAT_ID
ENV = Config.ENV

plt.rcParams['font.family'] = ['SimHei', 'Segoe UI Emoji', 'sans-serif']  # 设置字体，确保支持中文和Emoji
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号


async def send_person_profit_bar_chart(context, start_time, end_time):
    data = load_excel_data(start_time, end_time)
    person_profit = group_sum(data, "person")

    if not person_profit:
        await context.bot.send_message(chat_id=GROUP_CHAT_ID, text="该时间段内无有效人员盈亏数据。")
        return

    persons = list(person_profit.keys())
    profits = list(person_profit.values())
    labels = [f"{p:.2f}" for p in profits]

    title = "👤📊 人员盈亏柱状图"
    title = f"{title}\n（{start_time.strftime('%Y-%m-%d')}～{(end_time - timedelta(days=1)).strftime('%Y-%m-%d')}）"
    buf = draw_bar_chart(persons, profits, labels, title=title, xlabel="人员", ylabel="盈亏")
    await send_chart_image(context, buf, caption="✅ <b>人员盈亏柱状图</b>")


async def send_venue_profit_bar_chart(context, start_time, end_time):
    data = load_excel_data(start_time=start_time, end_time=end_time, full_load=False)
    venue_profit = calculate_total_by_venue(data)

    if not venue_profit:
        await context.bot.send_message(chat_id=GROUP_CHAT_ID, text="该时间段内无有效场地盈亏数据。")
        return

    venues = list(venue_profit.keys())
    profits = list(venue_profit.values())
    labels = [f"{p:.2f}" for p in profits]

    title = "🏛️📊 场子盈亏柱状图"
    title = f"{title}\n（{start_time.strftime('%Y-%m-%d')}～{(end_time - timedelta(days=1)).strftime('%Y-%m-%d')}）"
    buf = draw_bar_chart(venues, profits, labels, title=title, xlabel="场地", ylabel="盈亏")
    await send_chart_image(context, buf, caption="✅ <b>场地盈亏柱状图</b>")


async def send_profit_trend_chart(context, start_time, end_time):
    data = load_excel_data(start_time=start_time, end_time=end_time, full_load=False)
    if ENV == "dev":
        print(f"调试模式：加载数据，起始时间={start_time}, 结束时间={end_time}, 数据条数={len(data)}")
    day_person_profit, day_person_rebate = group_by_day_and_person(data)

    if not day_person_profit:
        await context.bot.send_message(chat_id=GROUP_CHAT_ID, text="该时间段内无盈亏趋势数据。")
        return

    buf = draw_trend_chart(day_person_profit, day_person_rebate)
    if buf is None:
        await context.bot.send_message(chat_id=GROUP_CHAT_ID, text="无法生成盈亏趋势图，数据不足。")
        return

    # 计算总盈亏和每日平均盈亏
    total_profit = sum(
        sum(person_profit.values()) for person_profit in day_person_profit.values()
    )
    avg_daily_profit = total_profit / len(day_person_profit)
    # 计算总输返和平均每日输返
    total_rebate = sum(
        sum(person_rebate.values()) for person_rebate in day_person_rebate.values()
    )
    avg_daily_rebate = total_rebate / len(day_person_rebate)

    # 计算每日总盈亏
    daily_total_profit = {
        day: sum(person_profit.values())
        for day, person_profit in day_person_profit.items()
    }

    # 计算每日总输返
    # daily_total_rebate = {
    #     day: sum(person_rebate.values())
    #     for day, person_rebate in day_person_rebate.items()
    # }

    # 找出最佳日和最差日
    max_day, max_profit = max(daily_total_profit.items(), key=lambda x: x[1])
    min_day, min_profit = min(daily_total_profit.items(), key=lambda x: x[1])

    # 构造图表说明文字
    caption = (
        f"📊 <b>盈亏趋势摘要</b>\n"
        f"• 统计周期：{start_time.strftime('%Y-%m-%d')} 至 {(end_time - timedelta(days=1)).strftime('%Y-%m-%d')}\n"
        f"• 总盈亏：{total_profit}\n"
        f"• 平均每日盈亏：{avg_daily_profit:.0f}\n"
        f"• 总输返：{total_rebate}\n"
        f"• 平均每日输返：{avg_daily_rebate:.0f}\n"
        f"• 吹牛日：{max_day.strftime('%Y-%m-%d')}（{max_profit:+.0f}）\n"
        f"• 打脸日：{min_day.strftime('%Y-%m-%d')}（{min_profit:+.0f}）\n"
    )

    # 汇总各成员盈亏
    total_by_person = defaultdict(int)
    for person_profit in day_person_profit.values():
        for person, profit in person_profit.items():
            total_by_person[person] += profit

    # 汇总各成员输返
    total_by_person_rebate = defaultdict(int)
    for person_rebate in day_person_rebate.values():
        for person, rebate in person_rebate.items():
            total_by_person_rebate[person] += rebate

    # 合并并格式化
    all_persons = set(total_by_person) | set(total_by_person_rebate)
    combined = []
    for person in all_persons:
        profit = total_by_person.get(person, 0)
        rebate = total_by_person_rebate.get(person, 0)
        combined.append((person, profit, rebate))


    caption += "\n📊 <b>各成员盈亏 | 输返：</b>\n"
    for person, profit, rebate in sorted(combined, key=lambda x: -x[1]):
        if profit > rebate:
            emoji = "😎🤑"
            profit_str = f"<b>+{profit}</b>"
        elif profit < rebate:
            emoji = "😅"
            profit_str = f"<b>{profit}</b>"  # 亏损不加正号
        else:
            emoji = "😐"
            profit_str = f"<b>{profit}</b>"

        caption += f"• {person}: {profit_str} | {rebate} {emoji}\n"
    # 团队总输返
    total_rebate = sum(total_by_person_rebate.values())
    if total_profit > total_rebate:
        total_profit_str = f"<b>+{total_profit}</b>"
        emoji = "😎😎😎💰💰💰"
    else:
        total_profit_str = f"<b>{total_profit}</b>"
        emoji = "😞😞😞😔😔💸💸💸"
    caption += f"\n• 团队总计：<b>{total_profit_str} | {total_rebate} {emoji}</b>\n"
    # 发送图片
    await send_chart_image(context, buf, caption=caption)
