#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test parameter passing in Google Sheets operations
"""

import sys
import os
import asyncio
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_execute_operation_parameter_passing():
    """Test execute_operation parameter passing mechanism"""
    try:
        print("Testing execute_operation parameter passing...")
        
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool
        
        # Create manager
        pool = GoogleSheetsConnectionPool()
        manager = GoogleSheetsManager(pool)
        
        # Test function that only needs client parameter
        def test_function_client_only(client):
            print(f"test_function_client_only called with client: {type(client)}")
            return {"status": "success", "client_type": str(type(client))}
        
        # Test function that needs client and additional parameters
        def test_function_with_args(client, arg1, arg2, kwarg1=None):
            print(f"test_function_with_args called with:")
            print(f"  client: {type(client)}")
            print(f"  arg1: {arg1}")
            print(f"  arg2: {arg2}")
            print(f"  kwarg1: {kwarg1}")
            return {"status": "success", "args": [arg1, arg2], "kwargs": {"kwarg1": kwarg1}}
        
        async def run_tests():
            try:
                # Test 1: Function with only client parameter
                print("\n--- Test 1: Function with only client parameter ---")
                result1 = await manager.execute_operation(test_function_client_only)
                print(f"Result 1: {result1}")
                
                # Test 2: Function with client and additional parameters
                print("\n--- Test 2: Function with client and additional parameters ---")
                result2 = await manager.execute_operation(
                    test_function_with_args, 
                    "arg1_value", 
                    "arg2_value", 
                    kwarg1="kwarg1_value"
                )
                print(f"Result 2: {result2}")
                
                return True
                
            except Exception as e:
                print(f"Test execution failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # Run the tests
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success = loop.run_until_complete(run_tests())
            return success
        finally:
            loop.close()
        
    except Exception as e:
        print(f"Parameter passing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_report_functions():
    """Test monthly report functions parameter passing"""
    try:
        print("\nTesting monthly report functions parameter passing...")
        
        from monthlyjob import MonthlyReportManager
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool
        
        # Create components
        pool = GoogleSheetsConnectionPool()
        sheets_manager = GoogleSheetsManager(pool)
        report_manager = MonthlyReportManager(sheets_manager)
        
        print(f"MonthlyReportManager created successfully")
        
        # Test the internal functions directly
        def test_check_and_create_headers(client):
            print(f"_check_and_create_headers would be called with client: {type(client)}")
            # Simulate the function logic without actually calling Google Sheets
            return True
        
        def test_update_report_operation(client):
            print(f"_update_report_operation would be called with client: {type(client)}")
            # Simulate the function logic without actually calling Google Sheets
            return True
        
        async def run_report_tests():
            try:
                # Test the parameter passing for both functions
                print("\n--- Testing _check_and_create_headers parameter passing ---")
                result1 = await sheets_manager.execute_operation(test_check_and_create_headers)
                print(f"Result: {result1}")
                
                print("\n--- Testing _update_report_operation parameter passing ---")
                result2 = await sheets_manager.execute_operation(test_update_report_operation)
                print(f"Result: {result2}")
                
                return True
                
            except Exception as e:
                print(f"Report test execution failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # Run the tests
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success = loop.run_until_complete(run_report_tests())
            return success
        finally:
            loop.close()
        
    except Exception as e:
        print(f"Monthly report functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_monthly_job():
    """Test actual monthly job execution"""
    try:
        print("\nTesting actual monthly job execution...")
        
        from monthlyjob import manual_complete_monthly_job
        
        # Execute monthly job for August 2025
        result = manual_complete_monthly_job(2025, 8)
        
        print(f"Monthly job execution result:")
        print(f"  Success: {result.get('success', False)}")
        
        errors = result.get('errors', [])
        print(f"  Errors count: {len(errors)}")
        
        if errors:
            print(f"  Error details:")
            parameter_errors = 0
            for i, error in enumerate(errors[:3], 1):  # Show first 3 errors
                print(f"    {i}. {error}")
                
                # Check for parameter-related errors
                if any(keyword in str(error).lower() for keyword in [
                    "takes", "positional", "argument", "parameter", "missing"
                ]):
                    parameter_errors += 1
            
            if parameter_errors > 0:
                print(f"  ❌ WARNING: Found {parameter_errors} parameter-related errors!")
                return False
            else:
                print(f"  ✅ SUCCESS: No parameter-related errors detected")
                return True
        else:
            print(f"  ✅ SUCCESS: No errors at all")
            return True
        
    except Exception as e:
        print(f"Actual monthly job test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Testing Parameter Passing in Google Sheets Operations")
    print("=" * 70)
    
    # Test 1: execute_operation parameter passing
    success1 = test_execute_operation_parameter_passing()
    
    # Test 2: Monthly report functions
    success2 = test_monthly_report_functions()
    
    # Test 3: Actual monthly job
    success3 = test_actual_monthly_job()
    
    print("\nTest Summary:")
    print(f"execute_operation parameter passing: {'PASS' if success1 else 'FAIL'}")
    print(f"Monthly report functions: {'PASS' if success2 else 'FAIL'}")
    print(f"Actual monthly job: {'PASS' if success3 else 'FAIL'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! Parameter passing is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
