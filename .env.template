# Telegram Bot Configuration
BOT_TOKEN=your_bot_token_here
CHAT_ID=your_chat_id_here

# Environment
ENV=production
DEBUG=false

# Logging Configuration
LOG_LEVEL=INFO
CONSOLE_LOG_LEVEL=INFO
FILE_LOG_LEVEL=DEBUG
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5

# Google Sheets Configuration
GOOGLE_SHEET_NAME=your_sheet_name_here
REBATE_CONFIG_SHEET_NAME=rebate_ratio_config
SALARY_CONFIG_SHEET_NAME=salary配置
CREDENTIALS_FILE=mysheetapp.json

# File Paths
EXCEL_FILE=data/records.xlsx
REBATE_FILE=data/rebate_config.json
SALARY_FILE=data/salary_config.json

# Time Zone
TIME_ZONE=Asia/Shanghai

# Monitoring (Optional)
ENABLE_MONITORING=true
METRICS_PORT=8080
HEALTH_CHECK_PORT=8081

# Telegram Logging (Optional - for critical errors)
LOG_BOT_TOKEN=
LOG_CHAT_ID=

# Database (Future use)
# DATABASE_URL=sqlite:///data/telegram_bot.db

# Security
SECRET_KEY=your_secret_key_here
ALLOWED_USERS=user1,user2,user3
ADMIN_USERS=admin1,admin2

# Performance
MAX_WORKERS=4
CONNECTION_POOL_SIZE=5
REQUEST_TIMEOUT=30

# Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL=3600
BACKUP_RETENTION_DAYS=30
