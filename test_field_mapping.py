#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
sys.stdout.reconfigure(encoding='utf-8')

def test_field_parsing():
    """测试字段解析和映射"""
    from Parser import parse_message

    test_msg = '''日期：9月26日
人员：吴风
场子： ChamadaP
游戏：俄罗斯
卡号:  1277182
本金：1400
点码:  3475
工资：0
输反：0
赢亏:  2075
备注：中了个炸弹。盈利里包含9月18日未记录的输返75'''

    print("=== 字段映射测试 ===")
    print("输入消息:")
    print(test_msg)
    print("\n解析结果:")

    try:
        fields, missing = parse_message(test_msg, strict=False)

        for key, value in fields.items():
            if value is not None and value != '':
                print(f"  {key}: {value} (类型: {type(value)})")

        print(f"\n缺失字段: {missing}")

        # 重点检查之前有问题的字段
        print("\n=== 关键字段验证 ===")
        print(f"起始本金: {fields.get('起始本金')}")
        print(f"盈利: {fields.get('盈利')}")
        print(f"输反: {fields.get('输反')}")

        # 验证字段值正确性
        if fields.get('起始本金') == 1400:
            print("✅ 起始本金字段解析正确")
        else:
            print(f"❌ 起始本金字段解析错误: 期望1400, 实际{fields.get('起始本金')}")

        if fields.get('盈利') == 2075:
            print("✅ 盈利字段解析正确")
        else:
            print(f"❌ 盈利字段解析错误: 期望2075, 实际{fields.get('盈利')}")

        return fields

    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def simulate_salary_calculation():
    """模拟工资计算过程"""
    print("\n=== 模拟工资计算过程 ===")

    # 模拟之前的问题
    test_parsed = {
        '工作日期': '9月26日',
        '人员': '吴风',
        '场子': 'ChamadaP',
        '游戏': '俄罗斯',
        '卡号': 1277182,
        '起始本金': 1400,
        '点码': 3475,
        '工资': 0,
        '输反': 0,
        '盈利': 2075,
        '备注': '中了个炸弹。盈利里包含9月18日未记录的输返75'
    }

    # 测试修复前的代码（使用"赢亏"）
    profit_old = test_parsed.get("赢亏", 0)
    print(f"使用旧字段名'赢亏': {profit_old}")

    # 测试修复后的代码（使用"盈利"）
    profit_new = test_parsed.get("盈利", 0)
    print(f"使用新字段名'盈利': {profit_new}")

    if profit_new == 2075:
        print("✅ 字段映射修复成功！")
    else:
        print("❌ 字段映射仍有问题")

if __name__ == "__main__":
    fields = test_field_parsing()
    simulate_salary_calculation()

    print("\n=== 测试总结 ===")
    if fields and fields.get('盈利') == 2075:
        print("✅ 所有测试通过，字段映射问题已修复！")
    else:
        print("❌ 仍有问题需要解决")
