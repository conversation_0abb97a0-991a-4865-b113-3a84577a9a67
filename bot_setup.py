#!/usr/bin/env python3
"""
Telegram Bot 设置模块
包含设置 Telegram Bot 应用程序、注册命令处理器和设置定时任务的共享代码
"""

import logging
from datetime import time as dt_time
from telegram import BotCommand
from telegram.ext import <PERSON><PERSON><PERSON>er, CommandHandler, MessageHandler, filters, JobQueue
from telegram.request import HTTPXRequest

from config import Config
from commander import (
    help_command_handler, help_simple_command_handler, venue_report_command_handler,
    export_data_command_handler, daily_command_handler, trend_command_handler,
    confirm_correction_command_handler, set_rebate_ratio_command_handler, add_venue_command_handler,
    load_rebate_config_command_handler, set_salary_command_handler, load_salary_config_command_handler,
    cache_status_command_handler, retry_sync_command_handler, config_cache_status_command_handler,
    refresh_config_cache_command_handler, person_report_command_handler, monthly_command_handler,
    monthly_salary_check_command_handler, complete_monthly_job_command_handler
)
from storage import search_records
from Group_record import (
    handle_group_message, error_handler, send_daily_report_job, send_weekly_report_job, 
    send_monthly_report_job, send_profit_trend_chart_job, send_venue_report_job, 
    send_person_report_job, retry_sync_job, refresh_config_cache_job
)

logger = logging.getLogger(__name__)

def create_bot_commands():
    """创建并返回 Bot 命令列表"""
    return [
        BotCommand("help", "显示详细帮助信息"),
        BotCommand("help_simple", "显示简化帮助信息"),
        BotCommand("daily_report", "生成昨日盈亏汇总"),
        BotCommand("monthly_report", "生成每周盈亏汇总"),
        BotCommand("trend_chart", "生成盈亏趋势图"),
        BotCommand("venue_report", "生成场子盈亏柱状图"),
        BotCommand("person_report", "生成个人盈亏柱状图"),
        BotCommand("find", "查找记录"),
        BotCommand("set_rebate", "设置输返比例"),
        BotCommand("add_venue", "添加新venue"),
        BotCommand("load_rebate_config", "从Google Sheet加载rebate配置"),
        BotCommand("set_salary", "设置工资规则"),
        BotCommand("load_salary_config", "从Google Sheet加载salary配置"),
        BotCommand("cache_status", "查看消息缓存状态"),
        BotCommand("retry_sync", "手动重试同步缓存消息"),
        BotCommand("config_cache_status", "查看配置缓存状态"),
        BotCommand("refresh_config_cache", "手动刷新配置缓存"),
        BotCommand("export_file", "导出数据"),
        BotCommand("monthly_salary_check", "执行月度工资检查"),
        BotCommand("complete_monthly_job", "执行完整月度工作流"),
    ]

def setup_bot_commands(app):
    """设置 Bot 命令"""
    return app.bot.set_my_commands(create_bot_commands())

def register_handlers(app):
    """注册命令和消息处理器"""
    # 注册命令处理器
    app.add_handler(CommandHandler("help", help_command_handler))
    app.add_handler(CommandHandler("help_simple", help_simple_command_handler))
    app.add_handler(CommandHandler("venue_report", venue_report_command_handler))
    app.add_handler(CommandHandler("export_file", export_data_command_handler))
    app.add_handler(CommandHandler("find", search_records))
    app.add_handler(CommandHandler("daily_report", daily_command_handler))
    app.add_handler(CommandHandler("trend_chart", trend_command_handler))
    app.add_handler(CommandHandler("confirm", confirm_correction_command_handler))
    app.add_handler(CommandHandler("set_rebate", set_rebate_ratio_command_handler))
    app.add_handler(CommandHandler("add_venue", add_venue_command_handler))
    app.add_handler(CommandHandler("load_rebate_config", load_rebate_config_command_handler))
    app.add_handler(CommandHandler("set_salary", set_salary_command_handler))
    app.add_handler(CommandHandler("load_salary_config", load_salary_config_command_handler))
    app.add_handler(CommandHandler("cache_status", cache_status_command_handler))
    app.add_handler(CommandHandler("retry_sync", retry_sync_command_handler))
    app.add_handler(CommandHandler("config_cache_status", config_cache_status_command_handler))
    app.add_handler(CommandHandler("refresh_config_cache", refresh_config_cache_command_handler))
    app.add_handler(CommandHandler("person_report", person_report_command_handler))
    app.add_handler(CommandHandler("monthly_report", monthly_command_handler))

    # app.add_handler(CommandHandler("weekly", weekly_command_handler))

    app.add_handler(CommandHandler("monthly_salary_check", monthly_salary_check_command_handler))
    app.add_handler(CommandHandler("complete_monthly_job", complete_monthly_job_command_handler))

    # 注册消息处理器
    app.add_handler(MessageHandler(filters.TEXT & filters.ChatType.GROUPS, handle_group_message))

    # 注册错误处理器
    app.add_error_handler(error_handler)

def setup_jobs(job_queue: JobQueue):
    """设置定时任务"""
    if job_queue is None:
        logger.warning("JobQueue 不可用，无法设置定时任务")
        return

    # 设置每日任务
    job_queue.run_daily(send_daily_report_job, time=dt_time(hour=8, minute=4))  # 每日 12:05（UTC+4）
    job_queue.run_daily(send_weekly_report_job, time=dt_time(hour=8, minute=6))  # 每日 UTC+4 12:10
    job_queue.run_daily(send_monthly_report_job, time=dt_time(hour=8, minute=8))
    job_queue.run_daily(send_profit_trend_chart_job, time=dt_time(hour=8, minute=12))  # UTC 时间，对应 UTC+4 为中午12:15

    # 设置重试同步任务
    job_queue.run_repeating(retry_sync_job, interval=1800, first=30)  # 每30分钟检查一次，30秒后开始
    job_queue.run_daily(retry_sync_job, time=dt_time(hour=7, minute=0))  # 每天早上11点（UTC+4）重试

    # 设置配置缓存刷新任务
    job_queue.run_daily(refresh_config_cache_job, time=dt_time(hour=8, minute=45))  # 每天中午12:45（UTC+4）刷新配置缓存

    # 设置每月任务
    job_queue.run_monthly(send_venue_report_job, when=dt_time(hour=8, minute=15), day=1)
    job_queue.run_monthly(send_person_report_job, when=dt_time(hour=8, minute=20), day=1)

    # 添加月度工资检查任务
    try:
        from monthlyjob import monthly_salary_check_job
        job_queue.run_monthly(
            callback=monthly_salary_check_job,
            when=dt_time(hour=8, minute=35),
            day=1,
            data={'chat_id': Config.GROUP_CHAT_ID if hasattr(Config, 'GROUP_CHAT_ID') else None}
        )
        logger.info(f"月度工资检查任务已配置: 每月1日 08:35 UTC")
    except ImportError:
        logger.warning("无法导入月度工资检查任务，跳过设置")

    # 添加完整月度工作流任务
    try:
        from monthlyjob import complete_monthly_job, get_complete_monthly_job_schedule_time
        complete_job_schedule_time = get_complete_monthly_job_schedule_time()
        job_queue.run_monthly(
            callback=complete_monthly_job,
            when=complete_job_schedule_time,
            day=1,
            data={'chat_id': Config.GROUP_CHAT_ID if hasattr(Config, 'GROUP_CHAT_ID') else None}
        )
        logger.info(f"完整月度工作流任务已配置: 每月1日 {complete_job_schedule_time.strftime('%H:%M')} UTC")
    except ImportError:
        logger.warning("无法导入完整月度工作流任务，跳过设置")
    
def create_application():
    """创建并配置 Telegram 应用程序"""
    # 创建 HTTPXRequest
    request = HTTPXRequest()

    # 创建应用程序
    app = ApplicationBuilder().token(Config.BOT_TOKEN or "").request(request).build()

    # 设置 Bot 命令
    setup_bot_commands(app)

    # 注册处理器
    register_handlers(app)

    # 设置定时任务
    setup_jobs(app.job_queue)

    return app
