import io
from collections import defaultdict
from datetime import timedelta, datetime
import matplotlib
matplotlib.use('Agg')
import io
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import matplotlib.colors as mcolors

from config import Config

GROUP_CHAT_ID = Config.GROUP_CHAT_ID
TIME_ZONE = Config.TIME_ZONE


async def send_error(bot, text):
    await bot.send_message(chat_id=GROUP_CHAT_ID, text=f"❗ {text}")


def get_report_period(start=None, end=None, period="monthly"):
    now = datetime.now(TIME_ZONE)
    title = ""
    if not start or not end:
        if period == "daily":
            start = (now - timedelta(days=1)).replace(hour=12, minute=0, second=0, microsecond=0)
            end = now.replace(hour=11, minute=59, second=59, microsecond=999999)
            title = "日报"
        elif period == "weekly":
            start_of_week = (now - timedelta(days=now.weekday())).replace(hour=12, minute=0, second=0, microsecond=0)
            if now.weekday() == 0:
                start = start_of_week - timedelta(days=7)
                end = start_of_week - timedelta(microseconds=1)
                title = "上周报告"
            else:
                start = start_of_week
                end = now.replace(hour=12, minute=0)
                title = "本周进度"
        elif period == "monthly":
            start = now.replace(day=1, hour=12, minute=0, second=0, microsecond=0)
            end = now
            if now.day == 1:
                prev_month = start - timedelta(days=1)
                start = prev_month.replace(day=1, hour=12, minute=0, second=0, microsecond=0)
            title = "月报"

    elif period == "specific":
        if start and end:
            start = start.replace(hour=12, minute=0, second=0, microsecond=0)
            end = (end + timedelta(days=1)).replace(hour=11, minute=59, second=59, microsecond=999999)
            title = f"从 {start.strftime('%Y-%m-%d')} 到 {end.strftime('%Y-%m-%d')} 的报告"
        else:
            return None, None, None
    else:
        return None, None, None
    return start, end, title


def calculate_total_by_venue(data):
    result = defaultdict(float)
    for record in data:
        venue = record["venue"]
        profit = record["profit"]
        result[venue] += profit
    return dict(result)


def calculate_total_by_person(data):
    result = defaultdict(float)
    for record in data:
        person = record["person"]
        profit = record["profit"]
        result[person] += profit
    return dict(result)


def calculate_person_win_rate(data):
    stats = defaultdict(lambda: {"wins": 0, "losses": 0})

    for record in data:
        person = record["person"]
        profit = record["profit"]
        if profit > 0:
            stats[person]["wins"] += 1
        elif profit < 0:
            stats[person]["losses"] += 1

    win_rates = {}
    for person, stat in stats.items():
        total = stat["wins"] + stat["losses"]
        if total > 0:
            win_rate = stat["wins"] / total
            win_rates[person] = round(win_rate, 2)

    return win_rates


def group_sum(rows, key):
    result = defaultdict(float)
    for row in rows:
        result[row[key]] += row["profit"]
    return result


def group_by_day_and_person(rows):
    day_person_profit = defaultdict(lambda: defaultdict(float))
    day_person_rebate = defaultdict(lambda: defaultdict(float))

    for row in rows:
        msg_time = row["msg_time"]  # 应该是 datetime 对象
        person = row["person"]
        profit = row["profit"]
        rebate = row.get("rebate", 0.0)

        # 构造当天中午12点的时间点
        noon_time = msg_time.replace(hour=12, minute=0, second=0, microsecond=0)

        # 如果消息时间在当天中午之前，则归入前一天
        if msg_time < noon_time:
            date_key = (msg_time - timedelta(days=1)).date()
        else:
            date_key = msg_time.date()

        # 以 date_key 和 person 为键进行归类
        day_person_profit[date_key][person] += profit
        day_person_rebate[date_key][person] += rebate

    return day_person_profit, day_person_rebate


def draw_bar_chart(labels, values, sublabels=None, title="", xlabel="", ylabel=""):
    plt.figure(figsize=(12, 6))
    bars = plt.bar(labels, values, color=['green' if v >= 0 else 'red' for v in values])
    plt.title(title)
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.grid(axis='y')

    if sublabels:
        for bar, label in zip(bars, sublabels):
            plt.text(bar.get_x() + bar.get_width() / 2,
                     bar.get_height(), label,
                     ha='center', va='bottom' if bar.get_height() >= 0 else 'top', fontsize=9)

    buf = io.BytesIO()
    plt.tight_layout()
    plt.savefig(buf, format='png')
    buf.seek(0)
    plt.close()
    return buf


def draw_trend_chart(day_person_profit, day_person_rebate):
    all_dates = sorted(day_person_profit.keys())
    people = sorted({person for day in day_person_profit.values() for person in day})

    # 生成颜色列表，每人一种颜色
    cmap = cm.get_cmap('tab10', len(people))
    color_map = {person: cmap(i) for i, person in enumerate(people)}

    # 初始化累计盈亏与累计输返
    profit_cumulative = {person: [] for person in people}
    rebate_cumulative = {person: [] for person in people}
    profit_totals = defaultdict(float)
    rebate_totals = defaultdict(float)

    total_profit_per_day = []

    for day in all_dates:
        day_total_profit = 0
        for person in people:
            profit = day_person_profit[day].get(person, 0)
            rebate = day_person_rebate.get(day, {}).get(person, 0)

            profit_totals[person] += profit
            rebate_totals[person] += rebate
            day_total_profit += profit

            profit_cumulative[person].append(profit_totals[person])
            rebate_cumulative[person].append(rebate_totals[person])

        total_profit_per_day.append(day_total_profit)

    # 计算累计总盈亏
    cumulative_total_profit = []
    running_total = 0
    for profit in total_profit_per_day:
        running_total += profit
        cumulative_total_profit.append(running_total)

    # 平均盈亏线
    avg_profit_per_day = [p / len(people) for p in total_profit_per_day]
    avg_profit = sum(total_profit_per_day) / len(total_profit_per_day) if total_profit_per_day else 0

    # 绘图
    plt.figure(figsize=(14, 8))

    # ✅ 堆叠柱状图：累计输返
    bottom = [0] * len(all_dates)
    for person in people:
        cumulative_rebates = rebate_cumulative[person]
        color = color_map[person]
        plt.bar(all_dates, cumulative_rebates, bottom=bottom, color=color, alpha=0.3, label=f"{person} 累计输返（柱状）")
        bottom = [b + r for b, r in zip(bottom, cumulative_rebates)]

    # 各成员盈亏 & 累计输返 折线图
    for person in people:
        color = color_map[person]
        plt.plot(all_dates, profit_cumulative[person], label=f"{person} 盈亏", color=color, linestyle='-')
        # plt.plot(all_dates, rebate_cumulative[person], label=f"{person} 输返累计", color=color, linestyle='--', alpha=0.7)

    # 每日总盈亏（黑色虚线）
    plt.plot(all_dates, total_profit_per_day, label="每日总盈亏", linestyle="--", linewidth=2, color="black")

    # 累计总盈亏（深红色实线）
    plt.plot(all_dates, cumulative_total_profit, label="累计总盈亏", linestyle="-", linewidth=2.5, color="darkred")

    # 团队累计输返（紫色虚线）
    # team_cumulative_rebate = [sum(rebate_cumulative[person][i] for person in people) for i in range(len(all_dates))]
    # plt.plot(all_dates, team_cumulative_rebate, label="团队累计输返", linestyle="--", linewidth=2.5, color="purple")

    # 平均盈亏（蓝色点线）
    # plt.plot(all_dates, avg_profit_per_day, label=f"每日平均盈亏", linestyle=":", linewidth=2, color='deepskyblue')

    # 总体平均盈亏线
    plt.axhline(y=avg_profit, color='green', linestyle=':', linewidth=2, label=f"总体平均盈亏：{avg_profit:.0f}")

    # 图形设置
    plt.title(f"📈💰📊 盈亏趋势图（含累计输返堆叠柱状）")
    plt.xlabel("日期（统计周期起始）")
    plt.ylabel("金额")
    plt.xticks(rotation=45)
    plt.grid(True)
    plt.legend(loc='best', fontsize=9)
    plt.tight_layout()

    # 输出图像
    buf = io.BytesIO()
    plt.savefig(buf, format='png')
    buf.seek(0)
    plt.close()
    return buf


async def send_chart_image(context, buf, caption):
    await context.bot.send_photo(
        chat_id=GROUP_CHAT_ID,
        photo=buf,
        caption=caption,
        parse_mode="HTML"
    )
    buf.close()
