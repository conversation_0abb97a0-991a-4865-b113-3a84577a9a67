#!/usr/bin/env python3
"""
Simple test for message caching functionality without pytest
"""

import os
import json
import tempfile
from datetime import datetime
from message_cache import MessageCache


def test_message_cache():
    """Test basic message caching functionality"""
    print("🧪 Testing message caching functionality...")
    
    # Create temporary files for testing
    temp_dir = tempfile.mkdtemp()
    cache_file = os.path.join(temp_dir, "test_cache.txt")
    retry_log_file = os.path.join(temp_dir, "test_retry_log.txt")
    
    try:
        # Create test cache instance
        cache = MessageCache()
        cache.cache_file = cache_file
        cache.retry_log_file = retry_log_file
        
        # Test 1: Add message to cache
        print("📝 Test 1: Adding message to cache...")
        parsed_data = {
            "人员": "测试用户",
            "场子": "测试场所",
            "本金": 1000,
            "输反": 100,
            "赢亏": -100
        }
        msg_time = "2025-08-08 21:38:59"
        original_message = "测试用户 测试场所 本金1000 输反100 输100"
        
        cache.add_message(parsed_data, msg_time, original_message)
        
        # Verify cache file exists
        assert os.path.exists(cache_file), "Cache file should exist"
        print("✅ Cache file created successfully")
        
        # Verify cache content
        with open(cache_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            assert len(lines) == 1, "Should have exactly one cached message"
            
            cached_entry = json.loads(lines[0].strip())
            assert cached_entry["msg_time"] == msg_time, "Message time should match"
            assert cached_entry["parsed_data"] == parsed_data, "Parsed data should match"
            assert cached_entry["retry_count"] == 0, "Initial retry count should be 0"
        print("✅ Message cached correctly with proper data")
        
        # Test 2: Get pending messages
        print("📋 Test 2: Getting pending messages...")
        pending = cache.get_pending_messages()
        assert len(pending) == 1, "Should have one pending message"
        assert pending[0]["parsed_data"] == parsed_data, "Pending message data should match"
        print("✅ Pending messages retrieved correctly")
        
        # Test 3: Update retry info
        print("🔄 Test 3: Updating retry information...")
        original_entry = pending[0]
        cache.update_retry_info(original_entry)
        
        updated_messages = cache._load_cache()
        assert len(updated_messages) == 1, "Should still have one message"
        assert updated_messages[0]["retry_count"] == 1, "Retry count should be incremented"
        assert updated_messages[0]["last_retry"] is not None, "Last retry time should be set"
        print("✅ Retry information updated correctly")
        
        # Test 4: Get cache statistics
        print("📊 Test 4: Getting cache statistics...")
        stats = cache.get_cache_stats()
        assert stats["total_cached"] == 1, "Should show 1 cached message"
        assert stats["pending_retry"] >= 0, "Should have valid pending retry count"
        print(f"✅ Cache stats: {stats['total_cached']} total, {stats['pending_retry']} pending")
        
        # Test 5: Remove message
        print("🗑️ Test 5: Removing message from cache...")
        cache.remove_message(updated_messages[0])
        
        remaining_messages = cache._load_cache()
        assert len(remaining_messages) == 0, "Should have no messages after removal"
        print("✅ Message removed successfully")
        
        # Test 6: Cache stats after removal
        print("📊 Test 6: Cache statistics after removal...")
        final_stats = cache.get_cache_stats()
        assert final_stats["total_cached"] == 0, "Should show 0 cached messages"
        print(f"✅ Final cache stats: {final_stats['total_cached']} total")
        
        print("\n🎉 All message caching tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        if os.path.exists(cache_file):
            os.remove(cache_file)
        if os.path.exists(retry_log_file):
            os.remove(retry_log_file)
        os.rmdir(temp_dir)


def test_network_error_detection():
    """Test network error detection in storage service"""
    print("\n🌐 Testing network error detection...")
    
    try:
        from storage_service import StorageService
        from error_handling import NetworkError, GoogleAPIError
        
        # Test different error types
        test_errors = [
            ("httpcore.ReadError", True),
            ("Connection timeout", True),
            ("Network unreachable", True),
            ("ReadTimeoutError", True),
            ("ConnectTimeoutError", True),
            ("Invalid API key", False),
            ("Permission denied", False),
            ("Quota exceeded", False)
        ]
        
        print("🔍 Testing error classification...")
        for error_msg, should_be_network in test_errors:
            error_msg_lower = error_msg.lower()
            network_keywords = ['timeout', 'connection', 'network', 'readerror', 'httpcore', 'connecttimeout', 'readtimeout']
            is_network_error = any(keyword in error_msg_lower for keyword in network_keywords)
            
            if is_network_error == should_be_network:
                print(f"✅ '{error_msg}' correctly classified as {'network' if should_be_network else 'API'} error")
            else:
                print(f"❌ '{error_msg}' incorrectly classified")
                return False
        
        print("✅ Network error detection working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Network error detection test failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Starting message caching tests...\n")
    
    success1 = test_message_cache()
    success2 = test_network_error_detection()
    
    if success1 and success2:
        print("\n🎊 All tests passed! Message caching system is working correctly.")
        exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        exit(1)
