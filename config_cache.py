#!/usr/bin/env python3
"""
配置缓存管理模块
管理 Rebate 和 Salary 配置的内存缓存
"""

import json
import os
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
import logging

from storage import (
    load_rebate_from_google_sheet, save_rebate_config, load_rebate_config,
    load_salary_from_google_sheet, save_salary_config, load_salary_config
)

logger = logging.getLogger(__name__)

class ConfigCache:
    """配置缓存管理器"""
    
    def __init__(self):
        # 内存缓存
        self._rebate_cache: Optional[Dict[str, Any]] = None
        self._salary_cache: Optional[Dict[str, Any]] = None
        
        # 缓存状态
        self._rebate_last_updated: Optional[datetime] = None
        self._salary_last_updated: Optional[datetime] = None
        
        # 缓存统计
        self._rebate_access_count = 0
        self._salary_access_count = 0
        
        # 初始化标志
        self._initialized = False
    
    async def initialize(self) -> Dict[str, Any]:
        """初始化配置缓存"""
        try:
            print("🔄 初始化配置缓存...")
            
            # 加载 Rebate 配置
            rebate_success = await self._load_rebate_cache()
            
            # 加载 Salary 配置
            salary_success = await self._load_salary_cache()
            
            self._initialized = True
            
            result = {
                'success': rebate_success and salary_success,
                'rebate_loaded': rebate_success,
                'salary_loaded': salary_success,
                'rebate_count': len(self._rebate_cache) if self._rebate_cache else 0,
                'salary_rules': self._count_salary_rules(),
                'memory_usage': self._estimate_memory_usage()
            }
            
            if result['success']:
                print(f"✅ 配置缓存初始化成功")
                print(f"📊 Rebate配置: {result['rebate_count']} 项")
                print(f"📊 Salary规则: {result['salary_rules']} 条")
                print(f"💾 预估内存占用: {result['memory_usage']:.2f}KB")
            else:
                print("⚠️ 配置缓存初始化部分失败")
            
            return result
            
        except Exception as e:
            logger.error(f"配置缓存初始化失败: {e}")
            self._initialized = False
            return {
                'success': False,
                'error': str(e),
                'rebate_loaded': False,
                'salary_loaded': False
            }
    
    async def _load_rebate_cache(self) -> bool:
        """加载 Rebate 配置到缓存"""
        try:
            # 优先从 Google Sheet 加载
            try:
                rebate_config = load_rebate_from_google_sheet()
                save_rebate_config(rebate_config)
                print("✅ 从 Google Sheet 加载 Rebate 配置成功")
            except Exception as e:
                logger.warning(f"从 Google Sheet 加载 Rebate 配置失败: {e}")
                # 回退到本地文件
                rebate_config = load_rebate_config()
                print("⚠️ 从本地文件加载 Rebate 配置")
            
            self._rebate_cache = rebate_config
            self._rebate_last_updated = datetime.now()
            return True
            
        except Exception as e:
            logger.error(f"加载 Rebate 配置失败: {e}")
            return False
    
    async def _load_salary_cache(self) -> bool:
        """加载 Salary 配置到缓存"""
        try:
            # 优先从 Google Sheet 加载
            try:
                salary_config = load_salary_from_google_sheet()
                save_salary_config(salary_config)
                print("✅ 从 Google Sheet 加载 Salary 配置成功")
            except Exception as e:
                logger.warning(f"从 Google Sheet 加载 Salary 配置失败: {e}")
                # 回退到本地文件
                salary_config = load_salary_config()
                print("⚠️ 从本地文件加载 Salary 配置")
            
            self._salary_cache = salary_config
            self._salary_last_updated = datetime.now()
            return True
            
        except Exception as e:
            logger.error(f"加载 Salary 配置失败: {e}")
            return False
    
    def get_rebate_config(self) -> Dict[str, Any]:
        """获取 Rebate 配置"""
        if not self._initialized:
            logger.warning("配置缓存未初始化，使用本地文件")
            return load_rebate_config()
        
        if self._rebate_cache is None:
            logger.warning("Rebate 缓存为空，使用本地文件")
            return load_rebate_config()
        
        self._rebate_access_count += 1
        return self._rebate_cache.copy()  # 返回副本避免外部修改
    
    def get_salary_config(self) -> Dict[str, Any]:
        """获取 Salary 配置"""
        if not self._initialized:
            logger.warning("配置缓存未初始化，使用本地文件")
            return load_salary_config()
        
        if self._salary_cache is None:
            logger.warning("Salary 缓存为空，使用本地文件")
            return load_salary_config()
        
        self._salary_access_count += 1
        return self._salary_cache.copy()  # 返回副本避免外部修改
    
    def _run_async_safely(self, coro):
        """安全运行异步协程"""
        try:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 在已有事件循环中运行
                    future = asyncio.run_coroutine_threadsafe(coro, loop)
                    return future.result(timeout=10)  # 10秒超时
                else:
                    # 创建新的事件循环
                    return asyncio.run(coro)
            except RuntimeError:
                # 没有事件循环，创建新的
                return asyncio.run(coro)
        except Exception as e:
            logger.error(f"异步操作执行失败: {e}")
            return None
    
    def get_rebate_ratio(self, venue: str, person: str) -> float:
        """
        获取返佣比例 - 三层策略（使用 config_manager）
        1. 内存缓存获取（优先）
        2. 本地文件获取（回退）  
        3. Google Sheet获取（最终回退）
        
        始终返回有效的返佣比例，对调用者完全透明
        """
        try:
            from config_manager import get_rebate_manager
            from alias_manager import resolve_with_aliases, LOCATION_ALIASES
            rebate_manager = get_rebate_manager()

            # 使用别名解析venue名称
            resolved_venue = resolve_with_aliases(venue, LOCATION_ALIASES, venue)
            logger.debug(f"解析venue名称: {venue} -> {resolved_venue}")
            
            # 第一层：内存缓存获取
            if self._initialized and self._rebate_cache is not None:
                ratio = rebate_manager.get_ratio(self._rebate_cache, resolved_venue, person)
                logger.debug(f"从内存缓存获取返佣比例: {resolved_venue}/{person} = {ratio}")
                return ratio
            
            # 第二层：本地文件获取
            config = self._run_async_safely(rebate_manager.load_local_config())
            if config:
                ratio = rebate_manager.get_ratio(config, resolved_venue, person)
                logger.info(f"从本地文件获取返佣比例: {resolved_venue}/{person} = {ratio}")
                
                # 更新缓存
                self._rebate_cache = config
                self._rebate_last_updated = datetime.now()
                return ratio
            
            # 第三层：Google Sheet获取
            config = self._run_async_safely(rebate_manager.load_from_google_sheet())
            if config:
                ratio = rebate_manager.get_ratio(config, resolved_venue, person)
                logger.info(f"从Google Sheet获取返佣比例: {resolved_venue}/{person} = {ratio}")
                
                # 更新缓存和本地文件
                self._rebate_cache = config
                self._rebate_last_updated = datetime.now()
                self._run_async_safely(rebate_manager.save_local_config(config))
                return ratio
            
            # 兜底默认值
            logger.info(f"所有来源均失败，使用默认返佣比例: 0.1")
            return 0.1
            
        except Exception as e:
            logger.error(f"获取返佣比例失败: {e}")
            return 0.1
    
    def find_salary_rule(self, rebate_ratio: float, game: str, profit: int) -> Optional[int]:
        """从缓存中查找工资规则"""
        try:
            salary_config = self.get_salary_config()
            
            rebate_key = str(rebate_ratio)
            if rebate_key not in salary_config:
                return None
            
            if game not in salary_config[rebate_key]:
                return None
            
            rules = salary_config[rebate_key][game]
            
            # 遍历规则，找到匹配的盈利范围
            for rule in rules:
                if not rule.get("enabled", True):
                    continue
                
                profit_min = rule.get("profit_min")
                profit_max = rule.get("profit_max")
                
                # 检查盈利是否在范围内
                if profit_min is not None and profit < profit_min:
                    continue
                if profit_max is not None and profit > profit_max:
                    continue
                
                # 找到匹配的规则
                return rule.get("salary", 0)
            
            return None
            
        except Exception as e:
            logger.error(f"从缓存查找工资规则失败: {e}")
            return None
    
    async def refresh_configs(self, force_google_sheet: bool = True) -> Dict[str, Any]:
        """刷新配置缓存"""
        try:
            print("🔄 刷新配置缓存...")
            
            rebate_success = False
            salary_success = False
            
            if force_google_sheet:
                # 强制从 Google Sheet 加载
                rebate_success = await self._load_rebate_cache()
                salary_success = await self._load_salary_cache()
            else:
                # 从本地文件加载
                try:
                    self._rebate_cache = load_rebate_config()
                    self._rebate_last_updated = datetime.now()
                    rebate_success = True
                except Exception as e:
                    logger.error(f"从本地加载 Rebate 配置失败: {e}")
                
                try:
                    self._salary_cache = load_salary_config()
                    self._salary_last_updated = datetime.now()
                    salary_success = True
                except Exception as e:
                    logger.error(f"从本地加载 Salary 配置失败: {e}")
            
            result = {
                'success': rebate_success and salary_success,
                'rebate_refreshed': rebate_success,
                'salary_refreshed': salary_success,
                'rebate_count': len(self._rebate_cache) if self._rebate_cache else 0,
                'salary_rules': self._count_salary_rules(),
                'timestamp': datetime.now().isoformat()
            }
            
            if result['success']:
                print(f"✅ 配置缓存刷新成功")
            else:
                print("⚠️ 配置缓存刷新部分失败")
            
            return result
            
        except Exception as e:
            logger.error(f"刷新配置缓存失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'initialized': self._initialized,
            'rebate_cached': self._rebate_cache is not None,
            'salary_cached': self._salary_cache is not None,
            'rebate_last_updated': self._rebate_last_updated.isoformat() if self._rebate_last_updated else None,
            'salary_last_updated': self._salary_last_updated.isoformat() if self._salary_last_updated else None,
            'rebate_access_count': self._rebate_access_count,
            'salary_access_count': self._salary_access_count,
            'rebate_config_count': len(self._rebate_cache) if self._rebate_cache else 0,
            'salary_rules_count': self._count_salary_rules(),
            'estimated_memory_kb': self._estimate_memory_usage()
        }
    
    def _count_salary_rules(self) -> int:
        """统计 Salary 规则数量"""
        if not self._salary_cache:
            return 0
        
        total_rules = 0
        for rebate_config in self._salary_cache.values():
            if isinstance(rebate_config, dict):
                for game_rules in rebate_config.values():
                    if isinstance(game_rules, list):
                        total_rules += len(game_rules)
        
        return total_rules
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（KB）"""
        try:
            rebate_size = len(json.dumps(self._rebate_cache, ensure_ascii=False)) if self._rebate_cache else 0
            salary_size = len(json.dumps(self._salary_cache, ensure_ascii=False)) if self._salary_cache else 0
            return (rebate_size + salary_size) / 1024  # 转换为KB
        except Exception:
            return 0.0
    
    def clear_cache(self):
        """清除缓存"""
        self._rebate_cache = None
        self._salary_cache = None
        self._rebate_last_updated = None
        self._salary_last_updated = None
        self._rebate_access_count = 0
        self._salary_access_count = 0
        self._initialized = False
        print("🗑️ 配置缓存已清除")


# 全局配置缓存实例
config_cache = ConfigCache()


# 便捷函数，替代原有的函数调用
def get_cached_rebate_config() -> Dict[str, Any]:
    """获取缓存的 Rebate 配置"""
    return config_cache.get_rebate_config()


def get_cached_salary_config() -> Dict[str, Any]:
    """获取缓存的 Salary 配置"""
    return config_cache.get_salary_config()


def get_cached_rebate_ratio(venue: str, person: str) -> Optional[float]:
    """从缓存获取 rebate 比例（已过时，建议直接使用 config_cache.get_rebate_ratio）"""
    # 为了保持向后兼容，这里返回 None 让原有逻辑继续工作
    # 但新代码应该直接使用 config_cache.get_rebate_ratio
    ratio = config_cache.get_rebate_ratio(venue, person)
    return ratio if ratio != 0.1 else None  # 如果是默认值，返回None让原逻辑处理


def find_cached_salary_rule(rebate_ratio: float, game: str, profit: int) -> Optional[int]:
    """从缓存查找工资规则"""
    return config_cache.find_salary_rule(rebate_ratio, game, profit)


async def refresh_config_cache(force_google_sheet: bool = True) -> Dict[str, Any]:
    """刷新配置缓存"""
    return await config_cache.refresh_configs(force_google_sheet)
