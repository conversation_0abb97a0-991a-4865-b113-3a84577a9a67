#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the final Response [200] fix for Google Sheets operations
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_response_object_simulation():
    """Test Response object simulation"""
    try:
        print("Testing Response object simulation...")
        
        # Create a mock Response object that might be thrown as exception
        class MockResponseException(Exception):
            def __init__(self, status_code=200):
                self.status_code = status_code
                super().__init__(f"<Response [{status_code}]>")
        
        # Test the exception handling
        try:
            raise MockResponseException(200)
        except Exception as e:
            print(f"Caught exception: {e}")
            
            # Test our fix logic
            if hasattr(e, '__str__') and "Response [200]" in str(e):
                print("✅ Successfully detected Response [200] in exception")
                return True
            else:
                print("❌ Failed to detect Response [200] in exception")
                return False
        
    except Exception as e:
        print(f"Response object simulation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_job_with_fix():
    """Test monthly job with the Response [200] fix"""
    try:
        print("\nTesting monthly job with Response [200] fix...")
        
        from monthlyjob import manual_complete_monthly_job
        
        # Execute monthly job for August 2025
        result = manual_complete_monthly_job(2025, 8)
        
        print(f"Monthly job execution result:")
        print(f"  Success: {result.get('success', False)}")
        
        errors = result.get('errors', [])
        print(f"  Errors count: {len(errors)}")
        
        if errors:
            print(f"  Error details:")
            response_200_errors = 0
            for i, error in enumerate(errors[:5], 1):  # Show first 5 errors
                print(f"    {i}. {error}")
                
                # Check if the error is still about Response [200]
                if "Response [200]" in str(error):
                    response_200_errors += 1
            
            if response_200_errors > 0:
                print(f"  ❌ WARNING: Still seeing {response_200_errors} Response [200] errors!")
                return False
            else:
                print(f"  ✅ SUCCESS: No Response [200] errors detected")
                return True
        else:
            print(f"  ✅ SUCCESS: No errors at all")
            return True
        
    except Exception as e:
        print(f"Monthly job test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_google_sheets_operations_fix():
    """Test Google Sheets operations with the fix"""
    try:
        print("\nTesting Google Sheets operations fix...")
        
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool
        
        # Create manager
        pool = GoogleSheetsConnectionPool()
        manager = GoogleSheetsManager(pool)
        
        print(f"Google Sheets manager created successfully")
        
        # Test a mock operation that simulates Response [200] exception
        def mock_operation_with_response_exception(client):
            # Simulate the case where gspread operation throws Response [200] as exception
            class MockResponseException(Exception):
                def __init__(self):
                    super().__init__("<Response [200]>")
            
            raise MockResponseException()
        
        # Test the operation
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                manager.execute_operation(mock_operation_with_response_exception)
            )
            print(f"Operation result: {result}")
            
            # If we get True, it means our fix worked
            if result is True:
                print("✅ SUCCESS: Response [200] exception handled correctly")
                return True
            else:
                print("❌ FAIL: Unexpected result")
                return False
                
        except Exception as e:
            print(f"Operation failed: {e}")
            # This should not happen with our fix
            return False
        finally:
            loop.close()
        
    except Exception as e:
        print(f"Google Sheets operations fix test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Testing Final Response [200] Fix for Google Sheets Operations")
    print("=" * 80)
    
    # Test 1: Response object simulation
    success1 = test_response_object_simulation()
    
    # Test 2: Google Sheets operations fix
    success2 = test_google_sheets_operations_fix()
    
    # Test 3: Monthly job with fix
    success3 = test_monthly_job_with_fix()
    
    print("\nTest Summary:")
    print(f"Response object simulation: {'PASS' if success1 else 'FAIL'}")
    print(f"Google Sheets operations fix: {'PASS' if success2 else 'FAIL'}")
    print(f"Monthly job with fix: {'PASS' if success3 else 'FAIL'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! Response [200] fix is working correctly.")
        print("The system should now properly handle Google Sheets operations.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
