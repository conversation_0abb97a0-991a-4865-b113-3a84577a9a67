#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the fix for expense_category_extra attribute error
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_expense_record_creation():
    """Test ExpenseRecord creation"""
    try:
        print("Testing ExpenseRecord creation...")
        
        from monthlyjob import ExpenseRecord
        from Parser import parse_datetime
        
        # Create a test record
        record = ExpenseRecord(
            timestamp=parse_datetime("2025-08-15 12:00:00"),
            report_date=parse_datetime("2025-08-15 12:00:00"),
            expense_person="Test Person",
            expense_category="其它",
            expense_amount=100.0,
            receipt_upload="test.jpg",
            remark="Test remark; Original remark",
            expense_date=parse_datetime("2025-08-15 12:00:00")
        )
        
        print(f"Record created successfully:")
        print(f"  Person: {record.expense_person}")
        print(f"  Category: {record.expense_category}")
        print(f"  Amount: {record.expense_amount}")
        print(f"  Remark: {record.remark}")
        
        return record
        
    except Exception as e:
        print(f"ExpenseRecord creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_expense_summary_calculation():
    """Test expense summary calculation"""
    try:
        print("\nTesting expense summary calculation...")
        
        from monthlyjob import ProfitReportGenerator
        from monthlyjob import ExpenseRecord
        from Parser import parse_datetime
        
        # Create test records
        records = [
            ExpenseRecord(
                timestamp=parse_datetime("2025-08-15 12:00:00"),
                report_date=parse_datetime("2025-08-15 12:00:00"),
                expense_person="Person1",
                expense_category="其它",
                expense_amount=100.0,
                receipt_upload="",
                remark="Test extra; Original remark",
                expense_date=parse_datetime("2025-08-15 12:00:00")
            ),
            ExpenseRecord(
                timestamp=parse_datetime("2025-08-15 12:00:00"),
                report_date=parse_datetime("2025-08-15 12:00:00"),
                expense_person="Person2",
                expense_category="交通",
                expense_amount=50.0,
                receipt_upload="",
                remark="Transport expense",
                expense_date=parse_datetime("2025-08-15 12:00:00")
            )
        ]
        
        # Create generator and test calculation
        generator = ProfitReportGenerator()
        summary = generator._calculate_expense_summary(records)
        
        print(f"Summary calculation successful:")
        for category, amount in summary.items():
            print(f"  {category}: ${amount}")
        
        return True
        
    except Exception as e:
        print(f"Expense summary calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_job_execution():
    """Test monthly job execution"""
    try:
        print("\nTesting monthly job execution...")
        
        from monthlyjob import manual_complete_monthly_job
        
        # Execute monthly job for August 2025
        result = manual_complete_monthly_job(2025, 8)
        
        print(f"Monthly job execution result:")
        print(f"  Success: {result.get('success', False)}")
        
        if result.get('errors'):
            print(f"  Errors: {len(result['errors'])}")
            for error in result['errors'][:3]:  # Show first 3 errors
                print(f"    - {error}")
        else:
            print(f"  No errors")
        
        if result.get('expense_data'):
            print(f"  Expense records: {len(result['expense_data'])}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"Monthly job execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Testing expense_category_extra fix")
    print("=" * 50)
    
    # Test 1: ExpenseRecord creation
    record = test_expense_record_creation()
    success1 = record is not None
    
    # Test 2: Expense summary calculation
    success2 = test_expense_summary_calculation()
    
    # Test 3: Monthly job execution
    success3 = test_monthly_job_execution()
    
    print("\nTest Summary:")
    print(f"ExpenseRecord creation: {'PASS' if success1 else 'FAIL'}")
    print(f"Expense summary calculation: {'PASS' if success2 else 'FAIL'}")
    print(f"Monthly job execution: {'PASS' if success3 else 'FAIL'}")
    
    if success1 and success2 and success3:
        print("\nAll tests passed! Fix is successful.")
    else:
        print("\nSome tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
