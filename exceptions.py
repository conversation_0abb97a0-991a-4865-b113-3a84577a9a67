#!/usr/bin/env python3
"""
统一异常处理模块
定义自定义异常类型和错误处理策略
"""

import logging
import traceback
from typing import Optional, Dict, Any, Union
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """错误严重级别"""
    LOW = "low"           # 轻微错误，不影响核心功能
    MEDIUM = "medium"     # 中等错误，影响部分功能
    HIGH = "high"         # 严重错误，影响核心功能
    CRITICAL = "critical" # 致命错误，系统无法继续运行


class ErrorCategory(Enum):
    """错误分类"""
    VALIDATION = "validation"         # 输入验证错误
    AUTHENTICATION = "authentication" # 认证授权错误
    NETWORK = "network"              # 网络连接错误
    FILE_SYSTEM = "file_system"      # 文件系统错误
    DATABASE = "database"            # 数据库相关错误
    EXTERNAL_API = "external_api"    # 外部API错误
    BUSINESS_LOGIC = "business_logic" # 业务逻辑错误
    SYSTEM = "system"                # 系统级错误
    CONFIGURATION = "configuration"  # 配置错误


class BaseCustomException(Exception):
    """自定义异常基类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = None,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Dict[str, Any] = None,
        user_message: str = None,
        recoverable: bool = True
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.user_message = user_message or self._get_default_user_message()
        self.recoverable = recoverable
        self.timestamp = datetime.now()
        
        # 记录异常到日志
        self._log_exception()
    
    def _get_default_user_message(self) -> str:
        """获取默认的用户友好错误消息"""
        return "系统遇到了一个问题，请稍后重试。"
    
    def _log_exception(self):
        """记录异常到日志"""
        log_data = {
            'error_code': self.error_code,
            'category': self.category.value,
            'severity': self.severity.value,
            'message': self.message,
            'details': self.details,
            'recoverable': self.recoverable,
            'timestamp': self.timestamp.isoformat()
        }
        
        if self.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            logger.error(f"[{self.error_code}] {self.message}", extra=log_data)
        elif self.severity == ErrorSeverity.MEDIUM:
            logger.warning(f"[{self.error_code}] {self.message}", extra=log_data)
        else:
            logger.info(f"[{self.error_code}] {self.message}", extra=log_data)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code,
            'category': self.category.value,
            'severity': self.severity.value,
            'message': self.message,
            'user_message': self.user_message,
            'details': self.details,
            'recoverable': self.recoverable,
            'timestamp': self.timestamp.isoformat()
        }


# ==================== 具体异常类型 ====================

class ValidationError(BaseCustomException):
    """输入验证异常"""
    
    def __init__(self, message: str, field_name: str = None, field_value: Any = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            details={'field_name': field_name, 'field_value': str(field_value) if field_value else None},
            user_message=f"输入验证失败：{message}",
            recoverable=True,
            **kwargs
        )


class AuthenticationError(BaseCustomException):
    """认证授权异常"""
    
    def __init__(self, message: str, user_id: str = None, required_level: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.MEDIUM,
            details={'user_id': user_id, 'required_level': required_level},
            user_message="权限不足，请联系管理员。",
            recoverable=False,
            **kwargs
        )


class NetworkError(BaseCustomException):
    """网络连接异常"""
    
    def __init__(self, message: str, endpoint: str = None, timeout: bool = False, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.MEDIUM,
            details={'endpoint': endpoint, 'timeout': timeout},
            user_message="网络连接异常，请检查网络连接或稍后重试。",
            recoverable=True,
            **kwargs
        )


class FileSystemError(BaseCustomException):
    """文件系统异常"""
    
    def __init__(self, message: str, file_path: str = None, operation: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.FILE_SYSTEM,
            severity=ErrorSeverity.MEDIUM,
            details={'file_path': file_path, 'operation': operation},
            user_message="文件操作失败，请检查文件权限或稍后重试。",
            recoverable=True,
            **kwargs
        )


class DatabaseError(BaseCustomException):
    """数据库异常"""
    
    def __init__(self, message: str, query: str = None, connection_failed: bool = False, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH if connection_failed else ErrorSeverity.MEDIUM,
            details={'query': query, 'connection_failed': connection_failed},
            user_message="数据库操作失败，请稍后重试。",
            recoverable=True,
            **kwargs
        )


class ExternalAPIError(BaseCustomException):
    """外部API异常"""
    
    def __init__(self, message: str, api_name: str = None, status_code: int = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.EXTERNAL_API,
            severity=ErrorSeverity.MEDIUM,
            details={'api_name': api_name, 'status_code': status_code},
            user_message="外部服务暂时不可用，请稍后重试。",
            recoverable=True,
            **kwargs
        )


class GoogleSheetsError(ExternalAPIError):
    """Google Sheets API 异常"""
    
    def __init__(self, message: str, sheet_name: str = None, operation: str = None, **kwargs):
        super().__init__(
            message,
            api_name="Google Sheets",
            details={'sheet_name': sheet_name, 'operation': operation},
            user_message="Google Sheets 操作失败，请检查网络连接和权限。",
            **kwargs
        )


class BusinessLogicError(BaseCustomException):
    """业务逻辑异常"""
    
    def __init__(self, message: str, operation: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.BUSINESS_LOGIC,
            severity=ErrorSeverity.MEDIUM,
            details={'operation': operation},
            user_message=f"操作失败：{message}",
            recoverable=True,
            **kwargs
        )


class ConfigurationError(BaseCustomException):
    """配置异常"""
    
    def __init__(self, message: str, config_key: str = None, config_file: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.CONFIGURATION,
            severity=ErrorSeverity.HIGH,
            details={'config_key': config_key, 'config_file': config_file},
            user_message="系统配置异常，请联系管理员。",
            recoverable=False,
            **kwargs
        )


class SystemError(BaseCustomException):
    """系统级异常"""
    
    def __init__(self, message: str, component: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.CRITICAL,
            details={'component': component},
            user_message="系统异常，请联系管理员。",
            recoverable=False,
            **kwargs
        )


# ==================== 异常处理装饰器 ====================

def handle_exceptions(
    fallback_return=None,
    log_traceback: bool = True,
    reraise: bool = False,
    custom_handler: callable = None
):
    """异常处理装饰器"""
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BaseCustomException as e:
                # 自定义异常直接处理
                if custom_handler:
                    return custom_handler(e)
                if reraise:
                    raise
                return fallback_return
            except Exception as e:
                # 标准异常转换为自定义异常
                if log_traceback:
                    logger.error(f"Unhandled exception in {func.__name__}: {e}", exc_info=True)
                
                # 转换为系统异常
                custom_exc = SystemError(
                    f"Unexpected error in {func.__name__}: {str(e)}",
                    component=func.__module__
                )
                
                if custom_handler:
                    return custom_handler(custom_exc)
                if reraise:
                    raise custom_exc
                return fallback_return
        
        # 支持异步函数
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            async def async_wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except BaseCustomException as e:
                    if custom_handler:
                        result = custom_handler(e)
                        if hasattr(result, '__await__'):
                            return await result
                        return result
                    if reraise:
                        raise
                    return fallback_return
                except Exception as e:
                    if log_traceback:
                        logger.error(f"Unhandled exception in {func.__name__}: {e}", exc_info=True)
                    
                    custom_exc = SystemError(
                        f"Unexpected error in {func.__name__}: {str(e)}",
                        component=func.__module__
                    )
                    
                    if custom_handler:
                        result = custom_handler(custom_exc)
                        if hasattr(result, '__await__'):
                            return await result
                        return result
                    if reraise:
                        raise custom_exc
                    return fallback_return
            
            return async_wrapper
        
        return wrapper
    return decorator


# ==================== 异常处理工具函数 ====================

def convert_standard_exception(exc: Exception) -> BaseCustomException:
    """将标准异常转换为自定义异常"""
    
    if isinstance(exc, BaseCustomException):
        return exc
    
    error_message = str(exc)
    exc_type = type(exc).__name__
    
    # 根据异常类型映射到对应的自定义异常
    if isinstance(exc, ValueError):
        return ValidationError(f"数值错误: {error_message}")
    elif isinstance(exc, KeyError):
        return ValidationError(f"缺少必要的键: {error_message}")
    elif isinstance(exc, FileNotFoundError):
        return FileSystemError(f"文件未找到: {error_message}", operation="read")
    elif isinstance(exc, PermissionError):
        return FileSystemError(f"权限不足: {error_message}", operation="access")
    elif isinstance(exc, ConnectionError):
        return NetworkError(f"连接错误: {error_message}")
    elif isinstance(exc, TimeoutError):
        return NetworkError(f"连接超时: {error_message}", timeout=True)
    else:
        return SystemError(f"未处理的异常 ({exc_type}): {error_message}")


def log_exception_details(exc: BaseCustomException, context: Dict[str, Any] = None):
    """记录异常详细信息"""
    context = context or {}
    
    log_data = exc.to_dict()
    log_data.update(context)
    
    if exc.severity == ErrorSeverity.CRITICAL:
        logger.critical(f"CRITICAL ERROR: {exc.message}", extra=log_data)
    elif exc.severity == ErrorSeverity.HIGH:
        logger.error(f"HIGH SEVERITY: {exc.message}", extra=log_data)
    elif exc.severity == ErrorSeverity.MEDIUM:
        logger.warning(f"MEDIUM SEVERITY: {exc.message}", extra=log_data)
    else:
        logger.info(f"LOW SEVERITY: {exc.message}", extra=log_data)


def get_user_friendly_message(exc: Exception) -> str:
    """获取用户友好的错误消息"""
    if isinstance(exc, BaseCustomException):
        return exc.user_message
    else:
        # 标准异常的用户友好消息
        converted = convert_standard_exception(exc)
        return converted.user_message


def is_recoverable_error(exc: Exception) -> bool:
    """判断错误是否可恢复"""
    if isinstance(exc, BaseCustomException):
        return exc.recoverable
    else:
        # 大多数标准异常都是可恢复的
        return not isinstance(exc, (SystemExit, KeyboardInterrupt))


# ==================== 异常统计和监控 ====================

class ExceptionTracker:
    """异常跟踪器"""
    
    def __init__(self):
        self._exception_counts = {}
        self._recent_exceptions = []
        self._max_recent = 100
    
    def track_exception(self, exc: BaseCustomException):
        """跟踪异常"""
        # 统计异常数量
        key = f"{exc.category.value}:{exc.error_code}"
        self._exception_counts[key] = self._exception_counts.get(key, 0) + 1
        
        # 记录最近的异常
        self._recent_exceptions.append({
            'timestamp': exc.timestamp,
            'error_code': exc.error_code,
            'category': exc.category.value,
            'severity': exc.severity.value,
            'message': exc.message
        })
        
        # 保持最近异常列表大小
        if len(self._recent_exceptions) > self._max_recent:
            self._recent_exceptions = self._recent_exceptions[-self._max_recent:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取异常统计信息"""
        return {
            'total_exceptions': sum(self._exception_counts.values()),
            'exception_counts': self._exception_counts.copy(),
            'recent_exceptions': self._recent_exceptions[-10:],  # 最近10个异常
            'categories': self._get_category_stats(),
            'severities': self._get_severity_stats()
        }
    
    def _get_category_stats(self) -> Dict[str, int]:
        """获取分类统计"""
        stats = {}
        for key, count in self._exception_counts.items():
            category = key.split(':')[0]
            stats[category] = stats.get(category, 0) + count
        return stats
    
    def _get_severity_stats(self) -> Dict[str, int]:
        """获取严重级别统计"""
        stats = {}
        for exc in self._recent_exceptions:
            severity = exc['severity']
            stats[severity] = stats.get(severity, 0) + 1
        return stats


# 全局异常跟踪器实例
exception_tracker = ExceptionTracker()


# ==================== 上下文管理器 ====================

class ExceptionContext:
    """异常处理上下文管理器"""
    
    def __init__(
        self,
        operation_name: str,
        fallback_return=None,
        log_success: bool = False,
        track_exceptions: bool = True
    ):
        self.operation_name = operation_name
        self.fallback_return = fallback_return
        self.log_success = log_success
        self.track_exceptions = track_exceptions
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            # 操作成功
            if self.log_success:
                logger.info(f"Operation '{self.operation_name}' completed successfully")
            return False
        
        # 处理异常
        if isinstance(exc_val, BaseCustomException):
            custom_exc = exc_val
        else:
            custom_exc = convert_standard_exception(exc_val)
        
        # 跟踪异常
        if self.track_exceptions:
            exception_tracker.track_exception(custom_exc)
        
        # 记录异常
        log_exception_details(custom_exc, {'operation': self.operation_name})
        
        # 抑制异常，返回 fallback 值
        return True
    
    def get_result(self):
        """获取结果（在异常情况下返回 fallback 值）"""
        return self.fallback_return