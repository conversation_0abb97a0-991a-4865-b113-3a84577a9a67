# CODEBUDDY.md

This file provides guidance to CodeBuddy Code when working with code in this repository.

## Project Overview

This is a sophisticated Telegram bot for managing and analyzing casino game records. It supports natural language data entry, statistical analysis, automatic report generation, and multi-platform data storage (Excel + Google Sheets). The bot features intelligent record matching, alias management, salary calculations, and comprehensive monitoring.

## Development Commands

### Running the Application
- **Development mode**: `python Group_record.py`
- **Production with monitoring**: `python main_with_monitoring.py`
- **Docker deployment**: `docker-compose up` (uses files in `docker/`)

### Testing
- **Run all tests**: `python run_tests.py --all`
- **Unit tests only**: `python run_tests.py --unit`
- **Integration tests**: `python run_tests.py --integration`
- **Fast tests (excludes network/slow)**: `python run_tests.py --fast`
- **With coverage**: `python run_tests.py --coverage`
- **Specific test**: `python run_tests.py --test tests/unit/test_example.py`

### Code Quality
- **Lint code**: `python run_tests.py --lint`
- **Format code**: `python run_tests.py --format`
- **Check dependencies**: `python run_tests.py --check-deps`
- **Install test deps**: `python run_tests.py --install-deps`

### Deployment
- **Windows deployment**: `deploy.bat` (uses NSSM service manager)
- **Quick deployment**: `quick_deploy.bat` or `quick_deploy.sh`

## Architecture Overview

### Core Components

1. **Main Application Layer**
   - `Group_record.py`: Core Telegram bot with message handling and command processing
   - `main_with_monitoring.py`: Production entry point with health monitoring
   - `commander.py`: Command handlers and intelligent record matching algorithms

2. **Data Processing Layer**
   - `Parser.py`: Natural language message parsing with multi-format date support
   - `data_models.py`: Core data structures for game records, rebate rules, salary rules
   - `input_validator.py`: Input validation and sanitization

3. **Storage Architecture** 
   - `storage_service.py`: New unified storage service (primary interface)
   - `storage.py`: Legacy compatibility layer for backward compatibility
   - Dual storage: Local Excel files + Google Sheets synchronization
   - `message_cache.py`: Caches failed Google Sheets syncs with automatic retry

4. **Configuration System**
   - `config.py`: Environment-aware configuration management
   - `config_cache.py`: Configuration caching with refresh capabilities
   - `config_manager.py`: Advanced configuration management

5. **Business Logic**
   - `salary.py`: Automatic salary calculation based on game results and rebate ratios
   - `alias_manager.py`: Manages aliases for persons, venues, and games
   - `charts.py`: Data visualization and chart generation
   - `report.py`: Automated report generation (daily/weekly/monthly)

6. **Infrastructure**
   - `job.py`: Scheduled tasks using APScheduler
   - `monitoring.py` + `health_check.py`: Application health monitoring
   - `error_strategy.py` + `exceptions.py`: Comprehensive error handling with retry mechanisms
   - `security_utils.py`: Security utilities and input validation

### Key Architectural Patterns

- **Strategy Pattern**: Error handling with decorators (`error_strategy.py`)
- **Service Layer**: Storage abstraction with sync/async support
- **Caching Layer**: Multi-level caching (config, message, data)
- **Event-Driven**: Telegram bot event handling with command routing
- **Dual Storage**: Excel + Google Sheets with automatic fallback/retry

## Configuration Files

- **Environment**: `.env` file with dev/prod settings
- **Aliases**: `aliases.json` - Person/venue/game name aliases
- **Rebate Rules**: `rebate_config.json` - Venue-specific rebate percentages
- **Salary Rules**: `salary_config.json` - Salary calculation rules
- **Test Config**: `pytest.ini` - Comprehensive test configuration with markers

## Development Environment Setup

1. **Python Requirements**: Python 3.8+ with dependencies from `requirements.txt`
2. **Environment Variables**: Copy `.env.template` to `.env` and configure:
   - `ENV=dev` or `ENV=prod`
   - Telegram bot tokens for each environment
   - Group chat IDs
   - Google Sheets credentials paths
   - Timezone settings
3. **Google Sheets**: Optional integration requires service account credentials
4. **Testing**: Uses pytest with async support, coverage reporting, and parallel execution

## Key Features & Implementation Notes

### Intelligent Record Matching
- **Location**: `commander.py:calculate_match_score()`
- Uses weighted scoring system (person=3, card=2, date=2)
- Supports fuzzy matching with confidence scores
- Handles multiple date formats and normalization

### Natural Language Processing
- **Location**: `Parser.py`
- Parses free-form text into structured game records
- Supports multiple date formats and Chinese number conversion
- Handles various input patterns and aliases

### Dual Storage Strategy
- **Primary**: Local Excel files for immediate access
- **Secondary**: Google Sheets for collaboration and backup
- **Fallback**: Message caching when Google Sheets sync fails
- **Retry Logic**: Automatic retry for failed operations

### Configuration Management
- **Multi-environment**: Automatic dev/prod configuration switching
- **Caching**: Configuration caching with manual refresh capability
- **Hot Reload**: Alias and configuration reloading without restart

### Error Handling & Monitoring
- **Strategy Pattern**: Consistent error handling across modules
- **Health Monitoring**: Application health checks and metrics
- **Comprehensive Logging**: Structured logging with rotation
- **Graceful Degradation**: Continues operation when external services fail

## Testing Strategy

- **Unit Tests**: `tests/unit/` - Individual component testing
- **Integration Tests**: `tests/integration/` - End-to-end functionality
- **Test Markers**: `unit`, `integration`, `slow`, `network`, `google_api`
- **Coverage**: 80% minimum coverage requirement
- **Async Testing**: Full async/await support with pytest-asyncio

## Important Implementation Details

1. **Async/Sync Compatibility**: Storage service supports both async and sync operations
2. **Backward Compatibility**: Legacy `storage.py` maintains compatibility with existing code
3. **Environment Isolation**: Complete separation between dev and prod environments
4. **Security**: Input validation and sanitization for all user inputs
5. **Monitoring**: Health checks, performance metrics, and comprehensive logging
6. **Resilience**: Automatic retry logic, graceful error handling, and fallback mechanisms