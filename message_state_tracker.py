#!/usr/bin/env python3
"""
Message processing state tracker to detect interrupted messages
"""

import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class MessageStateTracker:
    """Track messages currently being processed"""
    
    def __init__(self, state_file: str = "message_processing_state.json"):
        self.state_file = state_file
        self.processing_messages: Dict[str, Dict[str, Any]] = {}
    
    def start_processing(self, message_id: str, message_data: Dict[str, Any]) -> None:
        """Mark a message as starting processing"""
        try:
            processing_info = {
                "message_id": message_id,
                "start_time": datetime.now().isoformat(),
                "start_timestamp": time.time(),
                "message_data": message_data,
                "status": "processing",
                "last_step": "started"
            }
            
            self.processing_messages[message_id] = processing_info
            self._save_state()
            
            logger.debug(f"[MSG-{message_id}] 开始处理状态跟踪")
            
        except Exception as e:
            logger.error(f"[MSG-{message_id}] 启动处理状态跟踪失败: {e}")
    
    def update_step(self, message_id: str, step: str) -> None:
        """Update the current processing step"""
        try:
            if message_id in self.processing_messages:
                self.processing_messages[message_id]["last_step"] = step
                self.processing_messages[message_id]["last_update"] = datetime.now().isoformat()
                self._save_state()
                
                logger.debug(f"[MSG-{message_id}] 处理步骤更新: {step}")
            
        except Exception as e:
            logger.error(f"[MSG-{message_id}] 更新处理步骤失败: {e}")
    
    def complete_processing(self, message_id: str, success: bool = True) -> None:
        """Mark a message as completed processing"""
        try:
            if message_id in self.processing_messages:
                self.processing_messages[message_id]["status"] = "completed" if success else "failed"
                self.processing_messages[message_id]["end_time"] = datetime.now().isoformat()
                self.processing_messages[message_id]["end_timestamp"] = time.time()
                
                # Calculate processing duration
                start_time = self.processing_messages[message_id]["start_timestamp"]
                duration = time.time() - start_time
                self.processing_messages[message_id]["duration_seconds"] = duration
                
                self._save_state()
                
                logger.debug(f"[MSG-{message_id}] 处理完成: {'成功' if success else '失败'}, 耗时: {duration:.3f}秒")
                
                # Remove from active processing after a delay (keep for debugging)
                # We'll clean up old entries periodically
            
        except Exception as e:
            logger.error(f"[MSG-{message_id}] 完成处理状态跟踪失败: {e}")
    
    def _save_state(self) -> None:
        """Save current state to file"""
        try:
            state_data = {
                "last_update": datetime.now().isoformat(),
                "processing_messages": self.processing_messages
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存消息处理状态失败: {e}")
    
    def load_state(self) -> None:
        """Load state from file"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                self.processing_messages = state_data.get("processing_messages", {})
                logger.info(f"已加载消息处理状态: {len(self.processing_messages)} 条记录")
            
        except Exception as e:
            logger.error(f"加载消息处理状态失败: {e}")
            self.processing_messages = {}
    
    def get_interrupted_messages(self) -> List[Dict[str, Any]]:
        """Get messages that were interrupted during processing"""
        interrupted = []
        
        try:
            for message_id, info in self.processing_messages.items():
                if info.get("status") == "processing":
                    # Check if message was interrupted (no recent update)
                    start_time = info.get("start_timestamp", 0)
                    current_time = time.time()
                    
                    # If processing started more than 5 minutes ago and still "processing", 
                    # it was likely interrupted
                    if current_time - start_time > 300:  # 5 minutes
                        interrupted.append(info)
            
        except Exception as e:
            logger.error(f"获取中断消息失败: {e}")
        
        return interrupted
    
    def cleanup_old_entries(self, max_age_hours: int = 24) -> None:
        """Clean up old completed entries"""
        try:
            current_time = time.time()
            cutoff_time = current_time - (max_age_hours * 3600)
            
            to_remove = []
            for message_id, info in self.processing_messages.items():
                end_time = info.get("end_timestamp")
                if end_time and end_time < cutoff_time:
                    to_remove.append(message_id)
            
            for message_id in to_remove:
                del self.processing_messages[message_id]
            
            if to_remove:
                self._save_state()
                logger.info(f"清理了 {len(to_remove)} 条旧的处理记录")
                
        except Exception as e:
            logger.error(f"清理旧记录失败: {e}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        try:
            total = len(self.processing_messages)
            processing = sum(1 for info in self.processing_messages.values() 
                           if info.get("status") == "processing")
            completed = sum(1 for info in self.processing_messages.values() 
                          if info.get("status") == "completed")
            failed = sum(1 for info in self.processing_messages.values() 
                       if info.get("status") == "failed")
            
            return {
                "total": total,
                "processing": processing,
                "completed": completed,
                "failed": failed,
                "interrupted": len(self.get_interrupted_messages())
            }
            
        except Exception as e:
            logger.error(f"获取处理统计失败: {e}")
            return {"error": str(e)}


# Global message state tracker
message_state_tracker = MessageStateTracker()


def start_message_processing(message_id: str, message_data: Dict[str, Any]) -> None:
    """Start tracking message processing"""
    message_state_tracker.start_processing(message_id, message_data)


def update_processing_step(message_id: str, step: str) -> None:
    """Update current processing step"""
    message_state_tracker.update_step(message_id, step)


def complete_message_processing(message_id: str, success: bool = True) -> None:
    """Complete message processing tracking"""
    message_state_tracker.complete_processing(message_id, success)


def load_message_state() -> None:
    """Load message processing state"""
    message_state_tracker.load_state()


def get_interrupted_messages() -> List[Dict[str, Any]]:
    """Get interrupted messages"""
    return message_state_tracker.get_interrupted_messages()


def cleanup_old_message_state() -> None:
    """Clean up old message state entries"""
    message_state_tracker.cleanup_old_entries()


def get_message_processing_stats() -> Dict[str, Any]:
    """Get message processing statistics"""
    return message_state_tracker.get_processing_stats()


async def check_interrupted_messages():
    """Check for interrupted messages and log them"""
    try:
        interrupted = get_interrupted_messages()
        
        if interrupted:
            logger.warning(f"发现 {len(interrupted)} 条可能被中断的消息:")
            for msg_info in interrupted:
                message_id = msg_info.get("message_id", "Unknown")
                start_time = msg_info.get("start_time", "Unknown")
                last_step = msg_info.get("last_step", "Unknown")
                logger.warning(f"  - MSG-{message_id}: 开始时间={start_time}, 最后步骤={last_step}")
            
            logger.info("这些消息应该已通过早期缓存机制保存，将自动重试")
        else:
            logger.info("未发现被中断的消息")
            
    except Exception as e:
        logger.error(f"检查中断消息失败: {e}")
