#!/usr/bin/env python3
"""
快速修复 rebate_config.json 文件的脚本
"""

import json
import shutil
from datetime import datetime

def fix_rebate_config():
    """修复 rebate_config.json 文件"""
    try:
        # 备份原文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"rebate_config.json.backup_{timestamp}"
        shutil.copy2("rebate_config.json", backup_path)
        print(f"✅ 已备份原文件: {backup_path}")
        
        # 读取原文件
        with open("rebate_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"📖 读取配置，共 {len(config)} 项")
        
        # 检查是否有问题
        has_global_default = "默认比例" in config
        if has_global_default:
            global_default = config["默认比例"]
            print(f"🔍 发现全局默认比例: {global_default}")
            
            # 移除错误的全局默认比例
            del config["默认比例"]
            
            # 重新添加到正确位置（文件末尾）
            config["默认比例"] = global_default
            
            print("🔧 已修复全局默认比例位置")
        
        # 写入修复后的文件
        with open("rebate_config.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print("✅ 文件修复完成")
        
        # 验证修复结果
        with open("rebate_config.json", 'r', encoding='utf-8') as f:
            test_config = json.load(f)
        
        print(f"🔍 验证结果: 配置包含 {len(test_config)} 项")
        if "默认比例" in test_config:
            print(f"   全局默认比例: {test_config['默认比例']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 快速修复 rebate_config.json")
    print("=" * 40)
    
    if fix_rebate_config():
        print("\n🎉 修复成功！")
    else:
        print("\n💥 修复失败！")
