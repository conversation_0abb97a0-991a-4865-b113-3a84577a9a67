#!/usr/bin/env python3
"""
Pytest configuration and fixtures
"""

import pytest
import asyncio
import tempfile
import os
import shutil
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)

@pytest.fixture
def mock_config(temp_dir):
    """Mock configuration for tests."""
    with patch('config.Config') as mock_config:
        mock_config.EXCEL_FILE = os.path.join(temp_dir, "test_records.xlsx")
        mock_config.REBATE_FILE = os.path.join(temp_dir, "test_rebate.json")
        mock_config.SALARY_FILE = os.path.join(temp_dir, "test_salary.json")
        mock_config.GOOGLE_SHEET_NAME = "test_sheet"
        mock_config.REBATE_CONFIG_SHEET_NAME = "test_rebate_config"
        mock_config.SALARY_CONFIG_SHEET_NAME = "test_salary_config"
        mock_config.CREDENTIALS_FILE = os.path.join(temp_dir, "test_creds.json")
        mock_config.ENV = "test"
        yield mock_config

@pytest.fixture
def sample_game_record():
    """Sample game record for testing."""
    return {
        'msg_time': datetime(2025, 1, 1, 12, 0, 0),
        'work_date': '2025-01-01',
        'person': '张三',
        'venue': 'TestVenue',
        'game': 'BJ',
        'card_no': 'TEST001',
        'principal': 1000,
        'code': 100,
        'salary': 20,
        'rebate': 50,
        'profit': 500,
        'remark': '测试记录'
    }

@pytest.fixture
def sample_rebate_config():
    """Sample rebate configuration for testing."""
    return {
        "TestVenue": {
            "默认比例": 0.1,
            "张三": 0.15,
            "李四": 0.12
        },
        "AnotherVenue": {
            "默认比例": 0.08
        },
        "默认比例": 0.1
    }

@pytest.fixture
def sample_salary_config():
    """Sample salary configuration for testing."""
    return {
        "0.1": {
            "BJ": [
                {
                    "enabled": True,
                    "profit_min": 100,
                    "profit_max": 999,
                    "salary": 20,
                    "description": "BJ 100-999"
                },
                {
                    "enabled": True,
                    "profit_min": 1000,
                    "profit_max": None,
                    "salary": 50,
                    "description": "BJ 1000+"
                }
            ]
        },
        "0.2": {
            "UTH": [
                {
                    "enabled": True,
                    "profit_min": 200,
                    "profit_max": 1999,
                    "salary": 30,
                    "description": "UTH 200-1999"
                }
            ]
        }
    }

@pytest.fixture
def mock_google_sheets():
    """Mock Google Sheets client."""
    mock_client = Mock()
    mock_sheet = Mock()
    mock_worksheet = Mock()
    
    mock_client.open.return_value = mock_sheet
    mock_sheet.sheet1 = mock_worksheet
    mock_sheet.get_worksheet.return_value = mock_worksheet
    
    # Mock worksheet methods
    mock_worksheet.get_all_values.return_value = [
        ["场子", "人员", "比例", "备注"],
        ["TestVenue", "*", "0.1", "默认"],
        ["TestVenue", "张三", "0.15", "个人设置"],
        ["默认", "*", "0.1", "全局默认"]
    ]
    mock_worksheet.append_row = Mock()
    mock_worksheet.update_cell = Mock()
    
    return mock_client

@pytest.fixture
def mock_file_manager():
    """Mock async file manager."""
    mock_manager = AsyncMock()
    mock_manager.read_json_async.return_value = {}
    mock_manager.write_json_async.return_value = True
    mock_manager.read_excel_async.return_value = []
    mock_manager.write_excel_row_async.return_value = True
    mock_manager.create_excel_async.return_value = True
    mock_manager.get_file_stats_async.return_value = {
        'size': 1024,
        'modified': datetime.now(),
        'exists': True
    }
    return mock_manager

@pytest.fixture
def mock_sheets_manager():
    """Mock Google Sheets manager."""
    mock_manager = AsyncMock()
    mock_manager.read_sheet.return_value = [
        ["场子", "人员", "比例", "备注"],
        ["TestVenue", "*", "0.1", "默认"],
        ["TestVenue", "张三", "0.15", "个人设置"]
    ]
    mock_manager.append_row = AsyncMock()
    mock_manager.update_cell = AsyncMock()
    return mock_manager

@pytest.fixture
def mock_telegram_update():
    """Mock Telegram update object."""
    mock_update = Mock()
    mock_message = Mock()
    mock_user = Mock()
    
    mock_user.first_name = "TestUser"
    mock_message.from_user = mock_user
    mock_message.text = "测试消息"
    mock_message.date = datetime.now()
    mock_message.chat.id = 12345
    mock_message.reply_text = AsyncMock()
    
    mock_update.message = mock_message
    return mock_update

@pytest.fixture
def mock_telegram_context():
    """Mock Telegram context object."""
    mock_context = Mock()
    mock_context.args = []
    return mock_context

@pytest.fixture(autouse=True)
def reset_singletons():
    """Reset singleton instances between tests."""
    # Reset global instances
    import storage_service
    import async_file_ops
    import google_sheets_pool
    
    storage_service._storage_service = None
    async_file_ops._file_manager = None
    async_file_ops._excel_processor = None
    google_sheets_pool._connection_pool = None
    google_sheets_pool._sheets_manager = None
    
    yield
    
    # Clean up after test
    storage_service._storage_service = None
    async_file_ops._file_manager = None
    async_file_ops._excel_processor = None
    google_sheets_pool._connection_pool = None
    google_sheets_pool._sheets_manager = None

# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow
pytest.mark.network = pytest.mark.network
