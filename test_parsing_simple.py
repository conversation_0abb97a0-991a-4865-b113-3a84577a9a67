#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple message parsing test
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def test_parsing():
    """Test message parsing"""
    # User's original message
    test_message = """日期：9月24日
人员：吴风
场子： ChamadaP
游戏：俄罗斯
卡号:  1277182
本金：1400
点码:  3475
工资：0
输反：0
赢亏:  2075
备注：中了个炸弹。盈利里包含9月18日未记录的输返75"""

    print("=== Testing Message Parsing ===")

    try:
        from Parser import parse_message
        parsed, missing = parse_message(test_message)

        # Check key fields
        principal = parsed.get('起始本金', 'NOT_FOUND')
        profit = parsed.get('盈利', 'NOT_FOUND')
        person = parsed.get('人员', 'NOT_FOUND')

        print(f"Principal (should be 1400): {principal}")
        print(f"Profit (should be 2075): {profit}")
        print(f"Person (should be 吴风): {person}")

        # Verify fix
        success = (principal == 1400 and profit == 2075 and person == '吴风')
        print(f"Fix Status: {'SUCCESS' if success else 'FAILED'}")

        return success

    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    test_parsing()