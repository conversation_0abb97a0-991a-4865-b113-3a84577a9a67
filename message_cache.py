#!/usr/bin/env python3
"""
消息缓存和重试同步模块
"""

import json
import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from config import Config
from storage import sync_to_google_sheets, write_to_excel
import logging

logger = logging.getLogger(__name__)

# 缓存文件路径
CACHE_FILE = "message_cache.txt"
RETRY_LOG_FILE = "retry_log.txt"

class MessageCache:
    """消息缓存管理器"""
    
    def __init__(self):
        self.cache_file = CACHE_FILE
        self.retry_log_file = RETRY_LOG_FILE
    
    def add_message(self, parsed_data: Dict[str, Any], msg_time: str, original_message: str = "", write_status: str = "pending") -> None:
        """添加消息到缓存"""
        try:
            cache_entry = {
                "timestamp": datetime.now().isoformat(),
                "msg_time": msg_time,
                "parsed_data": parsed_data,
                "original_message": original_message,
                "retry_count": 0,
                "last_retry": None,
                "next_retry": self._calculate_next_retry(0).isoformat(),
                "write_status": write_status,  # pending, excel_success, sheets_success, complete
                "excel_written": False,
                "sheets_written": False,
                "write_attempts": 0
            }
            
            # 读取现有缓存
            cached_messages = self._load_cache()
            cached_messages.append(cache_entry)
            
            # 保存到文件
            self._save_cache(cached_messages)
            
            # 记录日志
            self._log_retry_action(f"消息已缓存: {parsed_data.get('人员', 'Unknown')} - {parsed_data.get('场子', 'Unknown')}")
            
        except Exception as e:
            logger.error(f"添加消息到缓存失败: {e}")
    
    def get_pending_messages(self) -> List[Dict[str, Any]]:
        """获取待重试的消息（只返回未完全写入的消息）"""
        try:
            cached_messages = self._load_cache()
            now = datetime.now()

            pending = []
            for msg in cached_messages:
                # 只处理未完全写入的消息
                write_status = msg.get("write_status", "pending")
                if write_status in ["pending", "partial"]:
                    next_retry = datetime.fromisoformat(msg["next_retry"])
                    if now >= next_retry:
                        pending.append(msg)
                elif write_status == "complete":
                    # 完全写入的消息应该被清理
                    logger.warning(f"发现已完全写入但未清理的消息: {msg.get('parsed_data', {}).get('人员', 'Unknown')}")

            return pending

        except Exception as e:
            logger.error(f"获取待重试消息失败: {e}")
            return []

    def update_write_status(self, parsed_data: Dict[str, Any], msg_time: str, excel_success: bool = None, sheets_success: bool = None) -> bool:
        """更新消息的写入状态"""
        try:
            cached_messages = self._load_cache()
            updated = False

            for msg in cached_messages:
                if (msg.get('parsed_data') == parsed_data and
                    msg.get('msg_time') == msg_time):

                    msg["write_attempts"] = msg.get("write_attempts", 0) + 1

                    if excel_success is not None:
                        msg["excel_written"] = excel_success
                    if sheets_success is not None:
                        msg["sheets_written"] = sheets_success

                    # Update overall status
                    if msg.get("excel_written") and msg.get("sheets_written"):
                        msg["write_status"] = "complete"
                    elif msg.get("excel_written") or msg.get("sheets_written"):
                        msg["write_status"] = "partial"
                    else:
                        msg["write_status"] = "pending"

                    updated = True
                    logger.debug(f"更新写入状态: Excel={msg.get('excel_written')}, Sheets={msg.get('sheets_written')}, Status={msg.get('write_status')}")
                    break

            if updated:
                self._save_cache(cached_messages)

            return updated

        except Exception as e:
            logger.error(f"更新写入状态失败: {e}")
            return False

    def remove_message(self, message_entry: Dict[str, Any]) -> None:
        """从缓存中移除消息"""
        try:
            cached_messages = self._load_cache()
            
            # 根据时间戳和数据匹配要移除的消息
            cached_messages = [
                msg for msg in cached_messages 
                if not (msg["timestamp"] == message_entry["timestamp"] and 
                       msg["parsed_data"] == message_entry["parsed_data"])
            ]
            
            self._save_cache(cached_messages)
            
        except Exception as e:
            logger.error(f"从缓存移除消息失败: {e}")

    def is_message_written(self, parsed_data: Dict[str, Any], msg_time: str) -> Dict[str, bool]:
        """检查消息是否已经写入"""
        try:
            cached_messages = self._load_cache()

            for msg in cached_messages:
                if (msg.get('parsed_data') == parsed_data and
                    msg.get('msg_time') == msg_time):
                    return {
                        "excel_written": msg.get("excel_written", False),
                        "sheets_written": msg.get("sheets_written", False),
                        "write_status": msg.get("write_status", "pending"),
                        "write_attempts": msg.get("write_attempts", 0)
                    }

            return {
                "excel_written": False,
                "sheets_written": False,
                "write_status": "not_cached",
                "write_attempts": 0
            }

        except Exception as e:
            logger.error(f"检查消息写入状态失败: {e}")
            return {
                "excel_written": False,
                "sheets_written": False,
                "write_status": "error",
                "write_attempts": 0
            }

    def update_retry_info(self, message_entry: Dict[str, Any]) -> None:
        """更新消息的重试信息"""
        try:
            cached_messages = self._load_cache()
            
            for msg in cached_messages:
                if (msg["timestamp"] == message_entry["timestamp"] and 
                    msg["parsed_data"] == message_entry["parsed_data"]):
                    
                    msg["retry_count"] += 1
                    msg["last_retry"] = datetime.now().isoformat()
                    msg["next_retry"] = self._calculate_next_retry(msg["retry_count"]).isoformat()
                    break
            
            self._save_cache(cached_messages)
            
        except Exception as e:
            logger.error(f"更新重试信息失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            cached_messages = self._load_cache()
            
            stats = {
                "total_cached": len(cached_messages),
                "pending_retry": 0,
                "oldest_message": None,
                "retry_counts": {}
            }
            
            now = datetime.now()
            for msg in cached_messages:
                # 统计待重试消息
                next_retry = datetime.fromisoformat(msg["next_retry"])
                if now >= next_retry:
                    stats["pending_retry"] += 1
                
                # 找到最老的消息
                msg_time = datetime.fromisoformat(msg["timestamp"])
                if stats["oldest_message"] is None or msg_time < stats["oldest_message"]:
                    stats["oldest_message"] = msg_time
                
                # 统计重试次数分布
                retry_count = msg["retry_count"]
                stats["retry_counts"][retry_count] = stats["retry_counts"].get(retry_count, 0) + 1
            
            return stats
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {"total_cached": 0, "pending_retry": 0, "oldest_message": None, "retry_counts": {}}
    
    def _load_cache(self) -> List[Dict[str, Any]]:
        """加载缓存文件"""
        if not os.path.exists(self.cache_file):
            return []
        
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                return [json.loads(line.strip()) for line in f if line.strip()]
        except Exception as e:
            logger.error(f"加载缓存文件失败: {e}")
            return []
    
    def _save_cache(self, messages: List[Dict[str, Any]]) -> None:
        """保存缓存文件"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                for msg in messages:
                    f.write(json.dumps(msg, ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"保存缓存文件失败: {e}")
    
    def _calculate_next_retry(self, retry_count: int) -> datetime:
        """计算下次重试时间"""
        now = datetime.now()
        
        if retry_count == 0:
            # 第一次重试：30分钟后
            return now + timedelta(minutes=30)
        elif retry_count == 1:
            # 第二次重试：1小时后
            return now + timedelta(hours=1)
        elif retry_count == 2:
            # 第三次重试：3小时后
            return now + timedelta(hours=3)
        else:
            # 后续重试：每天早上11点
            next_11am = now.replace(hour=11, minute=0, second=0, microsecond=0)
            if now.hour >= 11:
                # 如果已经过了今天的11点，安排到明天11点
                next_11am += timedelta(days=1)
            return next_11am
    
    def _log_retry_action(self, message: str) -> None:
        """记录重试日志"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            
            with open(self.retry_log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
        except Exception as e:
            logger.error(f"记录重试日志失败: {e}")


# 全局缓存管理器实例
message_cache = MessageCache()


async def retry_sync_cached_messages() -> Dict[str, Any]:
    """重试同步缓存的消息"""
    try:
        pending_messages = message_cache.get_pending_messages()
        
        if not pending_messages:
            return {"success": 0, "failed": 0, "total": 0}
        
        success_count = 0
        failed_count = 0
        
        for msg_entry in pending_messages:
            try:
                parsed_data = msg_entry["parsed_data"]
                msg_time = msg_entry["msg_time"]

                # 检查当前写入状态
                excel_written = msg_entry.get("excel_written", False)
                sheets_written = msg_entry.get("sheets_written", False)

                logger.info(f"重试消息: {parsed_data.get('人员', 'Unknown')} - Excel已写入: {excel_written}, Sheets已写入: {sheets_written}")

                # 只写入尚未成功的部分
                excel_success = excel_written
                sheets_success = sheets_written

                # 尝试写入Excel（如果尚未成功）
                if not excel_written:
                    try:
                        write_to_excel(parsed_data, msg_time)
                        excel_success = True
                        logger.info(f"重试Excel写入成功: {parsed_data.get('人员', 'Unknown')}")
                    except Exception as excel_error:
                        logger.error(f"重试Excel写入失败: {excel_error}")

                # 尝试同步到Google Sheets（如果尚未成功）
                if not sheets_written:
                    try:
                        sync_to_google_sheets(parsed_data, msg_time)
                        sheets_success = True
                        logger.info(f"重试Google Sheets同步成功: {parsed_data.get('人员', 'Unknown')}")
                    except Exception as sheets_error:
                        logger.error(f"重试Google Sheets同步失败: {sheets_error}")

                # 更新写入状态
                message_cache.update_write_status(parsed_data, msg_time, excel_success, sheets_success)

                # 如果完全成功，从缓存中移除
                if excel_success and sheets_success:
                    message_cache.remove_message(msg_entry)
                    success_count += 1
                    message_cache._log_retry_action(
                        f"重试同步完全成功: {parsed_data.get('人员', 'Unknown')} - {parsed_data.get('场子', 'Unknown')}"
                    )
                else:
                    # 部分成功，更新重试信息但不移除
                    message_cache.update_retry_info(msg_entry)
                    logger.warning(f"重试部分成功: {parsed_data.get('人员', 'Unknown')} - Excel: {excel_success}, Sheets: {sheets_success}")
                    if excel_success or sheets_success:
                        success_count += 1  # 计算部分成功
                
            except Exception as e:
                # 同步失败，更新重试信息
                message_cache.update_retry_info(msg_entry)
                failed_count += 1
                
                message_cache._log_retry_action(
                    f"重试同步失败 (第{msg_entry['retry_count'] + 1}次): {parsed_data.get('人员', 'Unknown')} - {parsed_data.get('场子', 'Unknown')}, 错误: {e}"
                )
        
        return {
            "success": success_count,
            "failed": failed_count,
            "total": len(pending_messages)
        }
        
    except Exception as e:
        logger.error(f"重试同步缓存消息失败: {e}")
        return {"success": 0, "failed": 0, "total": 0, "error": str(e)}
