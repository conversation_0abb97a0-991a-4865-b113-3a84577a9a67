# 月度工资检查功能使用手册

## 📋 功能概述

月度工资检查功能是一个自动化定时任务，每月1日中午12:30(UTC+4时间)自动执行，检查Excel表格中上个月的工资数据是否与配置规则一致，并自动更新不一致的记录。

## 🎯 核心特性

### 1. 自动定时执行
- **执行时间**: 每月1日 12:30 UTC+4
- **检查范围**: 上月1日 12:00:00 到 本月1日 11:59:59 (UTC+4)
- **过滤字段**: Excel第1列"填报时间"字段

### 2. 智能环境适配
- **开发环境**: UTC+5 → 自动调整为 07:30 UTC
- **生产环境**: UTC+8 → 自动调整为 04:30 UTC
- **数据时区**: Excel中的时间数据为UTC+4

### 3. 完整通知系统
- **成功通知**: "✅ 月度工资复核完成..."
- **无数据通知**: "❌ 上月无数据，请检查..."
- **失败通知**: "❌ 月度工资复核失败，请检查..."

## 🚀 快速开始

### 1. 验证系统环境
```python
from monthlyjob import validate_salary_check_environment

# 检查运行环境
if validate_salary_check_environment():
    print("✅ 环境验证通过")
else:
    print("❌ 环境验证失败，请检查配置")
```

### 2. 手动执行检查
```python
from monthlyjob import manual_monthly_salary_check

# 检查上个月数据（默认）
result = manual_monthly_salary_check()

# 检查指定月份数据
result = manual_monthly_salary_check(target_year=2025, target_month=1)

print(f"检查结果: {result}")
```

### 3. 测试功能
```python
from monthlyjob import test_check_and_update_salaries

# 运行完整测试
result = test_check_and_update_salaries()
```

## 📊 配置说明

### Excel文件要求
确保Excel文件包含以下列（顺序重要）：
1. **填报时间** (第1列) - 用于时间过滤
2. **人员** - 人员姓名
3. **场子** - 场馆名称
4. **游戏** - 游戏类型
5. **盈利** - 盈利金额
6. **工资** - 工资数额
7. **日期** - 工作日期

### 配置文件
系统会自动读取以下配置：
- `salary_config.json` - 工资规则配置
- `rebate_config.json` - 返佣比例配置
- `Config.EXCEL_FILE` - Excel文件路径

## 🔧 API参考

### 主要函数

#### `check_and_update_salaries(start_date=None, end_date=None, progress_callback=None, filter_by_report_time=False)`
执行工资检查和更新。

**参数:**
- `start_date`: 开始时间（用于填报时间过滤）
- `end_date`: 结束时间（用于填报时间过滤）
- `progress_callback`: 进度回调函数
- `filter_by_report_time`: 是否按填报时间过滤

**返回值:**
```python
{
    'total_records': 总记录数,
    'checked_records': 检查的记录数,
    'inconsistent_records': 不一致的记录数,
    'updated_records': 更新的记录数,
    'errors': 错误列表,
    'time_range': 时间范围信息
}
```

#### `manual_monthly_salary_check(target_year=None, target_month=None)`
手动执行月度检查。

**参数:**
- `target_year`: 目标年份（默认为上个月）
- `target_month`: 目标月份（默认为上个月）

#### `get_monthly_salary_check_range()`
计算月度检查的时间范围。

**返回值:**
- `(start_date, end_date)` - 开始和结束时间的元组

## 📅 使用场景

### 1. 定时自动检查
系统会在每月1日自动执行，无需人工干预。检查结果会自动发送到配置的群组。

### 2. 手动补充检查
如果定时任务失败或需要检查特定月份：
```python
# 检查2025年1月的数据
result = manual_monthly_salary_check(2025, 1)

# 检查上个月的数据
result = manual_monthly_salary_check()
```

### 3. 开发调试
```python
# 测试功能是否正常
test_check_and_update_salaries()

# 验证环境配置
validate_salary_check_environment()

# 查看时间范围计算
start_date, end_date = get_monthly_salary_check_range()
print(f"检查范围: {start_date} - {end_date}")
```

## 📝 输出文件

### Salary_check.txt
每次检查后会生成详细报告，包含：
- 检查时间和范围
- 不一致记录的详细信息
- 更新前后的工资对比
- 使用的返佣比例

示例内容：
```
工资检查报告
检查时间: 2025-02-01 12:30:15
总共发现 3 条工资不一致的记录
================================================================================

序号 人员     日期         场子       游戏   盈利     Excel工资 规则工资 Rebate比例
1    张三     2025-01-15   Iveria     BJ     -1500    10       15       0.12
2    李四     2025-01-20   Palace     BJ     2000     25       30       0.15
3    王五     2025-01-25   Crown      BJ     -800     5        8        0.10

================================================================================
说明:
- Excel工资: Excel文件中原有的工资数值
- 规则工资: 根据salary_config.json规则计算得出的工资
- 所有不一致的记录已自动更新为规则工资
- 请核实更新结果是否正确
```

## 🚨 故障排除

### 1. 常见错误

#### "Excel文件缺少必要的列: 填报时间"
**解决方案**: 确保Excel文件第1列为"填报时间"字段

#### "配置缓存初始化失败"
**解决方案**: 检查配置文件是否存在且格式正确

#### "未配置群组ID，无法发送通知"
**解决方案**: 在Config中设置`GROUP_CHAT_ID`

### 2. 调试步骤

1. **检查环境**:
   ```python
   validate_salary_check_environment()
   ```

2. **验证时间计算**:
   ```python
   start_date, end_date = get_monthly_salary_check_range()
   print(f"时间范围: {start_date} - {end_date}")
   ```

3. **测试手动执行**:
   ```python
   result = manual_monthly_salary_check()
   print(f"结果: {result}")
   ```

### 3. 日志查看
检查日志文件中的相关信息：
- 任务执行开始和结束时间
- 处理的记录数量
- 发现的不一致情况
- 错误和警告信息

## 🔄 维护和更新

### 1. 配置更新
- 修改工资规则：更新 `salary_config.json`
- 修改返佣比例：更新 `rebate_config.json`
- 更改群组通知：修改 `Config.GROUP_CHAT_ID`

### 2. 时间设置调整
如需修改执行时间，在 `Group_record.py` 中调整：
```python
job_queue.run_monthly(
    callback=monthly_salary_check_job,
    when=dt_time(hour=新小时, minute=新分钟),  # UTC时间
    day=1
)
```

### 3. 功能扩展
可以基于现有框架扩展功能：
- 添加更多的数据验证规则
- 支持多个Excel文件
- 增加更详细的统计报告
- 集成邮件通知

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件中的错误信息
2. 运行环境验证和测试函数
3. 检查Excel文件格式和配置文件
4. 联系技术支持并提供详细的错误信息

---

**版本**: 1.0.0  
**更新时间**: 2025-08-31  
**兼容性**: 向后兼容现有 `check_and_update_salaries()` 函数