# monthlyjob.py 工资检查功能使用说明

## 功能概述

`check_and_update_salaries()` 函数实现了自动检查和更新 Excel 文件中工资数据的功能。它会：

1. 读取 `group_records.xlsx` 文件中的所有记录
2. 根据 `salary_config.json` 和 `rebate_config.json` 中的规则计算每条记录应有的工资
3. 比较 Excel 中的工资与规则计算的工资
4. 自动更新不一致的工资数据
5. 生成详细的检查报告

## 使用方法

### 1. 直接调用函数

```python
from monthlyjob import check_and_update_salaries

# 执行工资检查和更新
result = check_and_update_salaries()

# 查看结果
print(f"检查了 {result['checked_records']} 条记录")
print(f"发现 {result['inconsistent_records']} 条不一致")
print(f"更新了 {result['updated_records']} 条记录")
```

### 2. 直接运行脚本

```bash
python monthlyjob.py
```

## 输入文件要求

### Excel 文件格式 (group_records.xlsx)

Excel 文件必须包含以下列（列名必须完全匹配）：

| 列名 | 说明 | 示例 |
|------|------|------|
| 消息时间 | 记录的消息时间 | 2025-08-25 06:09:05 |
| 日期 | 工作日期 | 2025-08-25 |
| 人员 | 人员姓名 | 老王 |
| 场子 | 场馆名称 | Iveria |
| 游戏 | 游戏类型 | BJ |
| 卡号 | 卡号 | 2026 |
| 本金 | 起始本金 | 2000 |
| 点码 | 点码 | 0 |
| 工资 | 工资数额 | 10 |
| 输反 | 输返金额 | 400 |
| 赢亏 | 盈利金额 | -1600 |
| 备注 | 备注信息 | 一个晚上都打不赢 |

### 配置文件要求

1. **salary_config.json**: 工资规则配置
2. **rebate_config.json**: 输返比例配置

这两个文件通过 `ConfigCache` 自动加载。

## 输出结果

### 返回值

函数返回一个字典，包含以下字段：

```python
{
    'total_records': 总记录数,
    'checked_records': 检查的记录数,
    'inconsistent_records': 不一致的记录数,
    'updated_records': 更新的记录数,
    'errors': 错误列表
}
```

### 生成的文件

1. **更新的 Excel 文件**: 原 `group_records.xlsx` 文件会被直接更新
2. **检查报告**: `Salary_check.txt` 文件，包含详细的检查结果

### Salary_check.txt 报告格式

```
工资检查报告
检查时间: 2025-08-29 14:30:15
总共发现 3 条工资不一致的记录
================================================================================

序号 人员     日期         场子       游戏   盈利     Excel工资 规则工资 Rebate比例
--------------------------------------------------------------------------------
1    老王     2025-08-25   Iveria     BJ     -1600    10       20       0.2
2    敏       2025-08-26   Otium      俄罗斯  800      15       30       0.2
3    俊       2025-08-27   Iveria     UTH    500      5        20       0.2

================================================================================
说明:
- Excel工资: Excel文件中原有的工资数值
- 规则工资: 根据salary_config.json规则计算得出的工资
- 所有不一致的记录已自动更新为规则工资
- 请核实更新结果是否正确
```

## 工资计算逻辑

1. **获取 Rebate 比例**: 根据场子和人员从 `rebate_config.json` 中查找对应的比例
2. **查找工资规则**: 根据 Rebate 比例、游戏类型和盈利金额从 `salary_config.json` 中查找匹配的规则
3. **应用规则**: 
   - 如果找到匹配的规则，使用规则中的工资
   - 如果没有找到匹配的规则，工资设为 0

## 错误处理

函数具有完善的错误处理机制：

- **文件不存在**: 检查 Excel 文件是否存在
- **配置加载失败**: 自动初始化配置缓存
- **数据格式错误**: 跳过格式错误的行并记录警告
- **文件权限问题**: 适当的异常处理和错误报告
- **合并单元格问题**: 检测并安全处理合并单元格

## 日志记录

函数会记录详细的处理日志，包括：

- 任务开始和结束
- 每条记录的处理过程
- 错误和警告信息
- 更新操作的详细信息

## 注意事项

1. **备份数据**: 运行前建议备份原始 Excel 文件
2. **文件权限**: 确保程序有读写 Excel 文件的权限
3. **配置正确**: 确保 `salary_config.json` 和 `rebate_config.json` 配置正确
4. **Excel 格式**: Excel 文件必须按照指定格式创建
5. **重复运行**: 函数可以安全地重复运行，不会破坏数据格式

## 测试功能

脚本包含测试功能，可以通过以下方式测试：

```python
from monthlyjob import test_check_and_update_salaries

# 运行测试
test_check_and_update_salaries()
```

测试会输出详细的运行结果和统计信息。

## 故障排除

### 常见问题

1. **"Excel文件不存在"**: 检查 `Config.EXCEL_FILE` 指定的文件路径
2. **"配置缓存初始化失败"**: 检查配置文件是否存在和格式是否正确
3. **"Excel文件缺少必要的列"**: 检查 Excel 文件的列名是否完全匹配要求
4. **"无法获取rebate比例"**: 检查 rebate_config.json 中是否有对应的场子和人员配置

### 调试建议

1. 检查日志输出获取详细错误信息
2. 确认 Excel 文件格式正确
3. 验证配置文件的 JSON 格式
4. 检查文件权限设置

## 扩展功能

当前实现的 `check_and_update_salaries()` 函数可以作为月度任务的一部分，与其他功能集成：

- 定期自动执行工资检查
- 集成到 Telegram Bot 命令中
- 作为月度报告生成的前置步骤