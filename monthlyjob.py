# 针对团队的专用的每月任务
# 包括：
# 1. 每月1号检查上月的工资录入情况
# 2. 获取团队上月的开支数据
# 3. 根据团队的总体盈利情况、工资、开支情况，做出并记录团队上月盈利情况报告
# 4. 根据每个月的盈利情况，做出并记录团队盈利趋势图

import os
import logging
import glob
import asyncio
from datetime import datetime, time as dt_time, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import openpyxl
from openpyxl import Workbook
from openpyxl.cell.cell import MergedCell

from config import Config
from config_cache import config_cache
from data_models import FIELDS_CHINESE
from Parser import parse_datetime, get_last_month_range
from report import generate_report

# 配置日志
logger = logging.getLogger(__name__)

# 常量定义
HEADER_ROW = 1
DATA_START_ROW = 2
BATCH_SIZE = 1000

# 配置
ENV = Config.ENV

# 配置类
class SalaryCheckConfig:
    """工资检查配置类"""
    EXCEL_FILE_PATH = Config.EXCEL_FILE
    REPORT_FILE_PATH = "Salary_check.txt"
    REQUIRED_COLUMNS = ["填报时间", "人员", "场子", "游戏", "盈利", "工资", "工作日期"]
    EXPECTED_HEADERS = ["消息时间"] + FIELDS_CHINESE

# 更新全局变量以保持向后兼容性
excel_file = SalaryCheckConfig.EXCEL_FILE_PATH

# 工具函数
def safe_convert_to_int(value, default=0):
    """安全转换为整数"""
    try:
        if value is None or value == "":
            return default
        return int(float(str(value)))
    except (ValueError, TypeError):
        logger.warning(f"无法转换值到整数: {value}，使用默认值: {default}")
        return default

def safe_convert_to_str(value, default=""):
    """安全转换为字符串"""
    try:
        if value is None:
            return default
        return str(value).strip()
    except Exception:
        return default

def get_cell_value(worksheet, row, col, default=""):
    """安全获取单元格值"""
    try:
        cell = worksheet.cell(row=row, column=col)
        if isinstance(cell, MergedCell):
            logger.warning(f"第{row}行第{col}列是合并单元格，返回默认值")
            return default
        return safe_convert_to_str(cell.value, default)
    except Exception as e:
        logger.warning(f"获取单元格值失败 ({row},{col}): {e}")
        return default

def get_cell_numeric_value(worksheet, row, col, default=0):
    """安全获取单元格数值"""
    try:
        cell = worksheet.cell(row=row, column=col)
        if isinstance(cell, MergedCell):
            logger.warning(f"第{row}行第{col}列是合并单元格，返回默认值")
            return default
        return safe_convert_to_int(cell.value, default)
    except Exception as e:
        logger.warning(f"获取单元格数值失败 ({row},{col}): {e}")
        return default

def validate_record_data(person, venue, game, profit, salary, work_date):
    """验证记录数据"""
    errors = []
    if not person or len(person.strip()) == 0:
        errors.append("人员不能为空")
    if not venue or len(venue.strip()) == 0:
        errors.append("场子不能为空")
    if not game or len(game.strip()) == 0:
        errors.append("游戏不能为空")
    if not isinstance(profit, (int, float)):
        errors.append("盈利必须是数字")
    if not isinstance(salary, (int, float)):
        errors.append("工资必须是数字")
    return errors

def safe_update_cell(worksheet, row, col, value):
    """安全更新单元格值"""
    try:
        cell = worksheet.cell(row=row, column=col)
        if isinstance(cell, MergedCell):
            logger.warning(f"第{row}行第{col}列是合并单元格，无法直接更新")
            return False
        cell.value = value
        return True
    except Exception as e:
        logger.error(f"更新单元格失败 ({row},{col}): {e}")
        return False

def get_monthly_salary_check_range():
    """
    计算月度工资检查的时间范围
    返回：上月1日 12:00:00 到 本月1日 11:59:59 (UTC+4)
    """
    from Parser import TIME_ZONE
    
    try:
        now = datetime.now(TIME_ZONE)
        
        # 本月1日 12:00:00  
        current_month_start = now.replace(day=1, hour=12, minute=0, second=0, microsecond=0)
        
        # 本月1日 11:59:59（检查结束时间）
        check_end = current_month_start.replace(hour=11, minute=59, second=59, microsecond=999999)
        
        # 上月1日 12:00:00（检查开始时间）
        last_month_year = current_month_start.year
        last_month_month = current_month_start.month - 1
        if last_month_month == 0:
            last_month_month = 12
            last_month_year -= 1
        
        check_start = current_month_start.replace(
            year=last_month_year, 
            month=last_month_month, 
            day=1, 
            hour=12, minute=0, second=0, microsecond=0
        )
        
        logger.info(f"月度检查时间范围: {check_start.strftime('%Y-%m-%d %H:%M:%S')} 到 {check_end.strftime('%Y-%m-%d %H:%M:%S')} (UTC+4)")
        return check_start, check_end
        
    except Exception as e:
        logger.error(f"计算月度检查时间范围失败: {e}")
        raise

def get_monthly_check_schedule_time():
    """获取月度检查的调度时间（UTC时间）"""
    env = ENV
    
    # 目标时间：UTC*****:30 
    # 转换为UTC时间用于调度
    if env == 'prod':  # UTC+8环境
        return dt_time(hour=4, minute=30)   # UTC*****:30 = UTC 08:30 = UTC*****:30 → UTC 04:30
    else:  # UTC+5环境  
        return dt_time(hour=7, minute=30)   # UTC*****:30 = UTC 08:30 = UTC*****:30 → UTC 07:30


            
def parse_excel_headers(worksheet):
    """解析Excel表头并返回列索引"""
    try:
        actual_headers = [cell.value for cell in worksheet[HEADER_ROW] if cell.value is not None]
        
        # 找到关键列的索引（从1开始）
        column_mapping = {}
        required_columns = SalaryCheckConfig.REQUIRED_COLUMNS
        
        for col_name in required_columns:
            if col_name in actual_headers:
                column_mapping[col_name] = actual_headers.index(col_name) + 1
            else:
                raise ValueError(f"Excel文件缺少必要的列: {col_name}")
        
        logger.info(f"Excel表头解析成功，列映射: {column_mapping}")
        return column_mapping, actual_headers
        
    except Exception as e:
        logger.error(f"解析Excel表头失败: {e}")
        raise

def process_excel_row(worksheet, row_num, column_mapping, start_date=None, end_date=None):
    """处理Excel中的一行数据，支持时间过滤"""
    try:
        # 获取关键数据
        person = get_cell_value(worksheet, row_num, column_mapping["人员"])
        venue = get_cell_value(worksheet, row_num, column_mapping["场子"])
        game = get_cell_value(worksheet, row_num, column_mapping["游戏"])
        work_date = get_cell_value(worksheet, row_num, column_mapping["工作日期"])

        # 对人员、场子和游戏类型进行别名转换，确保与配置中的标准名称匹配
        if person or venue or game:
            from alias_manager import resolve_with_aliases, PERSON_ALIASES, LOCATION_ALIASES, GAME_ALIASES

            # 转换人员名称
            if person:
                person = resolve_with_aliases(person, PERSON_ALIASES, person)

            # 转换场子名称
            if venue:
                venue = resolve_with_aliases(venue, LOCATION_ALIASES, venue)

            # 转换游戏类型
            if game:
                game = resolve_with_aliases(game, GAME_ALIASES, game)
        
        # 获取填报时间（用于时间过滤）
        report_time_str = None
        if "填报时间" in column_mapping:
            report_time_str = get_cell_value(worksheet, row_num, column_mapping["填报时间"])
        
        # 获取数值数据
        profit = get_cell_numeric_value(worksheet, row_num, column_mapping["盈利"])
        current_salary = get_cell_numeric_value(worksheet, row_num, column_mapping["工资"])
        
        # 跳过空行或无效行
        if not person or not venue or not game:
            return None
        
        # 时间过滤检查
        if start_date and end_date and report_time_str:
            try:
                report_time = parse_datetime(report_time_str)
                if not (start_date <= report_time <= end_date):
                    return None  # 不在时间范围内，跳过
            except Exception as e:
                logger.warning(f"第{row_num}行填报时间解析失败: {e}，跳过过滤")
                # 时间解析失败时，不进行过滤，继续处理
        
        # 数据验证
        validation_errors = validate_record_data(person, venue, game, profit, current_salary, work_date)
        if validation_errors:
            logger.warning(f"第{row_num}行数据验证失败: {validation_errors}")
            return None
        
        return {
            'row': row_num,
            'person': person,
            'venue': venue,
            'game': game,
            'work_date': work_date,
            'report_time': report_time_str,
            'profit': profit,
            'current_salary': current_salary
        }
        
    except Exception as e:
        logger.error(f"处理第{row_num}行数据失败: {e}")
        return None

def calculate_expected_salary(venue, person, game, profit):
    """计算预期工资"""
    try:
        # 获取该记录应有的rebate ratio
        rebate_ratio = config_cache.get_rebate_ratio(venue, person)
        
        # 根据规则计算应有的工资
        expected_salary = config_cache.find_salary_rule(rebate_ratio, game, profit)
        if expected_salary is None:
            expected_salary = 0
            
        return expected_salary, rebate_ratio
        
    except Exception as e:
        logger.error(f"计算预期工资失败: {e}")
        return 0, 0.0

def process_salary_inconsistencies(worksheet, column_mapping, total_rows, start_date=None, end_date=None, progress_callback=None):
    """处理工资不一致问题，支持时间过滤"""
    inconsistent_records = []
    checked_count = 0
    updated_count = 0
    
    filter_info = ""
    if start_date and end_date:
        filter_info = f" (时间范围: {start_date.strftime('%Y-%m-%d %H:%M')} - {end_date.strftime('%Y-%m-%d %H:%M')})"
    
    logger.info(f"开始处理 {total_rows - 1} 行数据{filter_info}")
    
    for row_num in range(DATA_START_ROW, total_rows + 1):
        try:
            # 处理当前行数据，支持时间过滤
            row_data = process_excel_row(worksheet, row_num, column_mapping, start_date, end_date)
            if row_data is None:
                continue
                
            checked_count += 1
            
            # 计算预期工资
            expected_salary, rebate_ratio = calculate_expected_salary(
                row_data['venue'], row_data['person'], 
                row_data['game'], row_data['profit']
            )
            
            # 比较当前工资与预期工资
            if row_data['current_salary'] != expected_salary:
                # 记录不一致的情况
                inconsistent_record = {
                    'row': row_num,
                    'person': row_data['person'],
                    'date': row_data['work_date'],
                    'venue': row_data['venue'],
                    'game': row_data['game'],
                    'profit': row_data['profit'],
                    'rebate_ratio': rebate_ratio,
                    'current_salary': row_data['current_salary'],
                    'expected_salary': expected_salary
                }
                inconsistent_records.append(inconsistent_record)
                
                # 更新Excel中的工资列
                if safe_update_cell(worksheet, row_num, column_mapping["工资"], expected_salary):
                    updated_count += 1
                    logger.info(f"更新第{row_num}行工资: {row_data['person']} {row_data['venue']} {row_data['game']} "
                              f"盈利{row_data['profit']} 工资 {row_data['current_salary']} -> {expected_salary} "
                              f"(rebate: {rebate_ratio})")
            
            # 进度回调
            if progress_callback and checked_count % 100 == 0:
                progress_callback(checked_count, total_rows - 1)
                
        except Exception as e:
            logger.error(f"处理第{row_num}行时出错: {e}")
            continue
    
    return inconsistent_records, checked_count, updated_count
def check_and_update_salaries(start_date=None, end_date=None, progress_callback=None, filter_by_report_time=True):
    """
    检查group_records.xlsx中的"工资"列数值是否与salary_config.json中的设置一致
    
    功能说明：
    1. 读取Excel文件中的所有记录或指定时间范围内的记录
    2. 对每条记录根据场子、人员、游戏、盈利计算应有的工资
    3. 比较Excel中的工资与规则工资，记录不一致的情况
    4. 自动更新不一致的工资并保存Excel文件
    5. 将检查结果写入Salary_check.txt文件
    
    Args:
        start_date: 开始时间（用于填报时间过滤）
        end_date: 结束时间（用于填报时间过滤）
        progress_callback: 进度回调函数，接收(current, total)参数
        filter_by_report_time: 是否按填报时间过滤
    
    Returns:
        Dict: 包含检查结果的字典，格式：
        {
            'total_records': 总记录数,
            'checked_records': 检查的记录数,
            'inconsistent_records': 不一致的记录数,
            'updated_records': 更新的记录数,
            'errors': 错误列表,
            'time_range': 时间范围信息 (如果有过滤)
        }
    """
    
    # 结果统计
    result = {
        'total_records': 0,
        'checked_records': 0,
        'inconsistent_records': 0,
        'updated_records': 0,
        'errors': [],
        'time_range': None
    }
    
    # 记录时间过滤信息
    if filter_by_report_time and start_date and end_date:
        result['time_range'] = {
            'start': start_date.strftime('%Y-%m-%d %H:%M:%S'),
            'end': end_date.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    workbook = None
    
    try:
        logger.info("开始工资检查和更新任务")
        
        # 1. 检查Excel文件是否存在
        if not os.path.exists(SalaryCheckConfig.EXCEL_FILE_PATH):
            error_msg = f"Excel文件不存在: {SalaryCheckConfig.EXCEL_FILE_PATH}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
            return result
        
        # 2. 读取Excel文件
        logger.info(f"读取Excel文件: {SalaryCheckConfig.EXCEL_FILE_PATH}")
        
        try:
            workbook = openpyxl.load_workbook(SalaryCheckConfig.EXCEL_FILE_PATH)
            worksheet = workbook.active
            
            if worksheet is None:
                error_msg = "Excel文件工作表无效"
                logger.error(error_msg)
                result['errors'].append(error_msg)
                return result
            
            # 3. 解析Excel表头
            column_mapping, actual_headers = parse_excel_headers(worksheet)
            
            # 4. 获取总行数
            total_rows = worksheet.max_row
            result['total_records'] = max(0, total_rows - 1)  # 减去表头行
            
            logger.info(f"Excel文件包含 {result['total_records']} 条数据记录")
            
            if result['total_records'] == 0:
                logger.warning("Excel文件中没有数据记录")
                return result
            
            # 5. 处理工资不一致问题
            filter_start = start_date if filter_by_report_time else None
            filter_end = end_date if filter_by_report_time else None
            
            inconsistent_records, checked_count, updated_count = process_salary_inconsistencies(
                worksheet, column_mapping, total_rows, filter_start, filter_end, progress_callback
            )
            
            # 6. 更新结果统计
            result['checked_records'] = checked_count
            result['inconsistent_records'] = len(inconsistent_records)
            result['updated_records'] = updated_count
            
            # 7. 保存更新后的Excel文件
            if updated_count > 0:
                logger.info(f"保存更新后的Excel文件，共更新了 {updated_count} 条记录")
                workbook.save(SalaryCheckConfig.EXCEL_FILE_PATH)
                logger.info("Excel文件保存成功")
            else:
                logger.info("没有需要更新的记录")
            
            # 8. 生成工资检查报告
            if inconsistent_records:
                _write_salary_check_file(inconsistent_records)
                logger.info(f"工资检查报告已写入 {SalaryCheckConfig.REPORT_FILE_PATH}，共 {len(inconsistent_records)} 条不一致记录")
            else:
                _write_salary_check_file([], check_time=datetime.now())
                logger.info("所有工资记录都与规则一致，已生成检查报告")
            
            logger.info(f"工资检查任务完成: 检查 {result['checked_records']} 条记录，"
                       f"发现 {result['inconsistent_records']} 条不一致，更新 {result['updated_records']} 条")
            
            return result
            
        except Exception as excel_error:
            error_msg = f"读取或处理Excel文件时出错: {excel_error}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
            return result
        
    except Exception as e:
        error_msg = f"工资检查任务执行失败: {e}"
        logger.error(error_msg)
        result['errors'].append(error_msg)
        return result
    
    finally:
        # 确保工作簿被正确关闭
        if workbook is not None:
            try:
                workbook.close()
                logger.debug("Excel工作簿已关闭")
            except Exception as e:
                logger.warning(f"关闭工作簿时出错: {e}")


def _write_salary_check_file(inconsistent_records: List[Dict[str, Any]], check_time: Optional[datetime] = None):
    """
    将工资检查结果写入 Salary_check.txt 文件
    
    Args:
        inconsistent_records: 不一致的记录列表
        check_time: 检查时间，如果为None则使用当前时间
    """
    if check_time is None:
        check_time = datetime.now()
    
    try:
        with open(SalaryCheckConfig.REPORT_FILE_PATH, "w", encoding="utf-8") as f:
            f.write(f"工资检查报告\n")
            f.write(f"检查时间: {check_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总共发现 {len(inconsistent_records)} 条工资不一致的记录\n")
            f.write("="*80 + "\n\n")
            
            if inconsistent_records:
                # 写入表头
                f.write(f"{'序号':<4} {'人员':<8} {'日期':<12} {'场子':<10} {'游戏':<6} "
                       f"{'盈利':<8} {'Excel工资':<8} {'规则工资':<8} {'Rebate比例':<10}\n")
                f.write("-"*80 + "\n")
                
                # 写入每条不一致的记录
                for i, record in enumerate(inconsistent_records, 1):
                    f.write(f"{i:<4} {record['person']:<8} {record['date']:<12} "
                           f"{record['venue']:<10} {record['game']:<6} {record['profit']:<8} "
                           f"{record['current_salary']:<8} {record['expected_salary']:<8} "
                           f"{record['rebate_ratio']:<10}\n")
                
                f.write("\n" + "="*80 + "\n")
                f.write("说明:\n")
                f.write("- Excel工资: Excel文件中原有的工资数值\n")
                f.write("- 规则工资: 根据salary_config.json规则计算得出的工资\n")
                f.write("- 所有不一致的记录已自动更新为规则工资\n")
                f.write("- 请核实更新结果是否正确\n")
            else:
                f.write("🎉 所有工资记录都与配置规则一致！\n")
                f.write("没有发现需要更新的记录。\n")
        
        logger.info(f"工资检查报告写入成功: {SalaryCheckConfig.REPORT_FILE_PATH}")
        
    except Exception as e:
        logger.error(f"写入工资检查报告失败: {e}")

def validate_salary_check_environment():
    """
    验证工资检查功能的运行环境
    
    Returns:
        bool: 环境验证是否通过
    """
    errors = []
    
    # 检查Excel文件
    if not os.path.exists(SalaryCheckConfig.EXCEL_FILE_PATH):
        errors.append(f"Excel文件不存在: {SalaryCheckConfig.EXCEL_FILE_PATH}")
    
    # 检查配置缓存
    try:
        # 测试配置缓存是否可用
        test_ratio = config_cache.get_rebate_ratio("test_venue", "test_person")
        if test_ratio is None:
            errors.append("配置缓存返回None，可能存在配置问题")
    except Exception as e:
        errors.append(f"配置缓存测试失败: {e}")
    
    # 检查文件写入权限
    try:
        test_file = SalaryCheckConfig.REPORT_FILE_PATH + ".test"
        with open(test_file, "w", encoding="utf-8") as f:
            f.write("test")
        os.remove(test_file)
    except Exception as e:
        errors.append(f"报告文件写入权限检查失败: {e}")
    
    if errors:
        logger.error("环境验证失败:")
        for error in errors:
            logger.error(f"  - {error}")
        return False
    
    logger.info("环境验证通过")
    return True

async def monthly_salary_check_job(context):
    """
    月度工资检查定时任务
    每月1日 12:30 (UTC+4) 执行，检查上月1日 12:00 到 本月1日 11:59:59 的数据
    """
    try:
        logger.info("开始执行月度工资检查任务")
        
        # 计算上月4的时间范围
        start_date, end_date = get_monthly_salary_check_range()
        
        # 执行工资检查
        result = check_and_update_salaries(
            start_date=start_date, 
            end_date=end_date, 
            filter_by_report_time=True
        )
        
        # 发送结果通知
        await send_monthly_check_notification(context, result, start_date, end_date)
        
        logger.info(f"月度工资检查任务完成: 检查{result['checked_records']}条，更新{result['updated_records']}条")
        
    except Exception as e:
        error_msg = f"月度工资检查任务失败: {e}"
        logger.error(error_msg)
        
        # 发送失败通知
        try:
            await send_monthly_check_error_notification(context, error_msg)
        except Exception as notify_error:
            logger.error(f"发送失败通知失败: {notify_error}")

async def send_monthly_check_notification(context, result, start_date, end_date):
    """发送月度检查结果通知"""
    try:
        if result['checked_records'] == 0:
            # 无数据情况
            message = (
                "❌ 上月无数据，请检查\n"
                f"📅 查询期间: {start_date.strftime('%Y年%m月%d日')} - {end_date.strftime('%Y年%m月%d日')}\n"
                "🔍 建议检查Excel文件中填报时间字段是否正确"
            )
        elif result['errors']:
            # 有错误情况
            error_summary = "; ".join(result['errors'][:3])  # 只显示前3个错误
            message = (
                "❌ 月度工资复核失败，请检查\n"
                f"❗ 错误信息: {error_summary}\n"
                "🔧 建议联系技术支持"
            )
        else:
            # 成功情况
            consistency_rate = 0
            if result['checked_records'] > 0:
                consistency_rate = ((result['checked_records'] - result['inconsistent_records']) / result['checked_records']) * 100
            
            message = (
                "✅ 月度工资复核完成\n"
                f"📅 检查期间: {start_date.strftime('%Y年%m月%d日')} - {end_date.strftime('%Y年%m月%d日')}\n"
                f"📈 处理结果: 检查{result['checked_records']}条，发现{result['inconsistent_records']}条不一致，更新{result['updated_records']}条\n"
                f"📊 数据一致性: {consistency_rate:.1f}%\n"
                f"📄 详细报告: {SalaryCheckConfig.REPORT_FILE_PATH}"
            )
        
        # 获取bot实例并发送消息
        bot = context.bot
        chat_id = None
        
        # 尝试从不同来源获取chat_id
        if hasattr(context, 'job') and context.job and hasattr(context.job, 'data'):
            # 定时任务场景
            chat_id = context.job.data.get('chat_id') if context.job.data else None
        elif hasattr(context, 'chat_data') and context.chat_data:
            # 命令执行场景
            chat_id = context.chat_data.get('chat_id') if hasattr(context.chat_data, 'get') else None
        elif hasattr(context, 'effective_chat') and context.effective_chat:
            # 直接从消息中获取
            chat_id = context.effective_chat.id
        
        # 如果没有从context获取到chat_id，使用配置的默认群组ID
        if not chat_id:
            chat_id = Config.GROUP_CHAT_ID

        if chat_id:
            await bot.send_message(chat_id=chat_id, text=message)
            logger.info(f"月度检查通知已发送到群组: {chat_id}")
        else:
            logger.warning("未配置群组ID，无法发送通知")
            
    except Exception as e:
        logger.error(f"发送月度检查通知失败: {e}")

async def send_monthly_check_error_notification(context, error_msg):
    """发送月度检查错误通知"""
    try:
        message = (
            "❌ 月度工资复核失败，请检查\n"
            f"❗ 错误信息: {error_msg}\n"
            "🔧 建议联系技术支持"
        )
        
        bot = context.bot
        chat_id = None
        
        # 尝试从不同来源获取chat_id
        if hasattr(context, 'job') and context.job and hasattr(context.job, 'data'):
            # 定时任务场景
            chat_id = context.job.data.get('chat_id') if context.job.data else None
        elif hasattr(context, 'chat_data') and context.chat_data:
            # 命令执行场景
            chat_id = context.chat_data.get('chat_id') if hasattr(context.chat_data, 'get') else None
        elif hasattr(context, 'effective_chat') and context.effective_chat:
            # 直接从消息中获取
            chat_id = context.effective_chat.id
        
        # 如果没有从context获取到chat_id，使用配置的默认群组ID
        if not chat_id:
            chat_id = Config.GROUP_CHAT_ID

        if chat_id:
            await bot.send_message(chat_id=chat_id, text=message)
            logger.info(f"月度检查错误通知已发送到群组: {chat_id}")
        else:
            logger.warning("未配置群组ID，无法发送错误通知")

    except Exception as e:
        logger.error(f"发送错误通知失败: {e}")

def manual_monthly_salary_check(target_year=None, target_month=None):
    """
    手动执行月度工资检查
    
    Args:
        target_year: 目标年份，默认为上个月
        target_month: 目标月份，默认为上个月
    
    Returns:
        Dict: 检查结果
    """
    try:
        from Parser import TIME_ZONE
        
        if target_year is None or target_month is None:
            # 使用默认的上个月范围
            start_date, end_date = get_last_month_range()
        else:
            # 使用指定的年月
            from datetime import datetime
            start_date = datetime(target_year, target_month, 1, 12, 0, 0, tzinfo=TIME_ZONE)
            
            # 下个月的第一天 11:59:59
            next_month = target_month + 1
            next_year = target_year
            if next_month > 12:
                next_month = 1
                next_year += 1
            end_date = datetime(next_year, next_month, 1, 11, 59, 59, 999999, tzinfo=TIME_ZONE)
        
        logger.info(f"手动执行月度工资检查: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 执行检查
        result = check_and_update_salaries(
            start_date=start_date,
            end_date=end_date,
            filter_by_report_time=True
        )
        
        result['manual_execution'] = True
        result['target_period'] = {
            'year': start_date.year,
            'month': start_date.month,
            'start_date': start_date.strftime('%Y-%m-%d %H:%M:%S'),
            'end_date': end_date.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return result
        
    except Exception as e:
        logger.error(f"手动月度检查失败: {e}")
        return {
            'manual_execution': True,
            'errors': [str(e)],
            'checked_records': 0,
            'inconsistent_records': 0,
            'updated_records': 0
        }



# ==================== 新增：月度任务扩展功能 ====================

import glob
from dataclasses import dataclass
from typing import Tuple
from collections import defaultdict

# 异常类定义
class MonthlyJobException(Exception):
    """月度任务异常基类"""
    pass

class ExpenseDataException(MonthlyJobException):
    """开支数据异常"""
    pass

class ReportGenerationException(MonthlyJobException):
    """报表生成异常"""
    pass

class GoogleSheetException(MonthlyJobException):
    """Google Sheet操作异常"""
    pass

# 数据模型
@dataclass
class ExpenseRecord:
    """开支记录数据模型（统一采用Google Sheet格式）"""
    timestamp: datetime           # 时间戳
    report_date: datetime         # 填报日期
    expense_person: str           # 开支人
    expense_category: str         # 开支类别
    expense_amount: float         # 开支金额
    receipt_upload: str           # 开支凭证
    remark: str                   # 备注
    expense_date: datetime        # 开支项目发生时间

@dataclass
class MonthlyReport:
    """月报数据模型"""
    report_month: str             # 报告月份 (2025-08)
    report_time: datetime         # 报告生成时间
    total_profit: float           # 总盈利
    total_salary: float           # 总工资
    total_expense: float          # 总开支
    net_profit: float             # 净利润
    profit_members_count: int     # 盈利成员数
    loss_members_count: int       # 亏损成员数
    total_work_sessions: int      # 开工总次数
    main_expense_category: str    # 主要开支类别
    remark: str                   # 备注

@dataclass
class MemberContribution:
    """成员贡献数据模型"""
    member_name: str              # 成员姓名
    profit: float                 # 盈利金额
    salary: float                 # 工资金额
    work_sessions: int            # 开工次数
    profit_percentage: float      # 盈利占比

# 配置扩展
class MonthlyJobConfig:
    """月度任务配置"""
    MONTHLY_REPORT_SHEET_NAME = "Group_profit_report"

    # 开支数据源配置
    EXPENSE_EXCEL_FILE = "G国先锋开支明细.xlsx"
    EXPENSE_GOOGLE_SHEET_NAME = "G_team_expense"
    EXPENSE_GOOGLE_WORKSHEET_NAME = "expense"

    # 数据源切换时间点配置（可配置化）
    DATA_SOURCE_SWITCH_TIME = "2025-09-01 11:59:59"

    # 字段映射配置（可配置化）
    EXCEL_FIELD_MAPPING = {
        "submit_time": "提交时间",
        "fill_id": "填写ID",
        "answer_time": "答题时间",
        "submitter": "昵称",
        "report_date": "填报日期",
        "expense_person": "开支人",
        "expense_category": "开支类别",
        "expense_category_extra": "开支类别:补充填空",
        "expense_amount": "开支金额（$）",
        "expense_date": "开支项目发生日期时间",
        "receipt_upload": "票据上传:文件_1",
        "remark": "备注"
    }

    GOOGLE_SHEET_FIELD_MAPPING = {
        "timestamp": "时间戳",
        "report_date": "填报日期",
        "expense_person": "开支人",
        "expense_category": "开支类别",
        "expense_amount": "开支金额",
        "receipt_upload": "开支凭证",
        "remark": "备注",
        "expense_date": "开支项目发生时间"
    }

    # 报表格式配置
    CURRENCY_FORMAT = "{:+,.0f}"
    PERCENTAGE_FORMAT = "{:.1f}%"

    # 月度任务执行时间配置
    MONTHLY_JOB_HOUR = 13  # UTC+4 13:00 执行完整月度任务
    MONTHLY_JOB_MINUTE = 0

# 开支数据管理器
class ExpenseDataManager:
    """开支数据管理器"""

    def __init__(self):
        self.excel_file = MonthlyJobConfig.EXPENSE_EXCEL_FILE
        self.google_sheet_name = MonthlyJobConfig.EXPENSE_GOOGLE_SHEET_NAME
        self.google_worksheet_name = MonthlyJobConfig.EXPENSE_GOOGLE_WORKSHEET_NAME

        # 解析数据源切换时间点
        from Parser import parse_datetime
        self.switch_time = parse_datetime(MonthlyJobConfig.DATA_SOURCE_SWITCH_TIME)

    def get_monthly_expenses(self, start_date: datetime, end_date: datetime) -> List[ExpenseRecord]:
        """
        获取指定时间范围内的开支数据
        根据时间分界点自动选择数据源并合并数据

        Args:
            start_date: 开始时间
            end_date: 结束时间

        Returns:
            开支记录列表（统一采用Google Sheet格式）
        """
        try:
            logger.info(f"开始获取开支数据，时间范围: {start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"数据源切换时间点: {self.switch_time.strftime('%Y-%m-%d %H:%M:%S')}")

            all_records = []
            errors = []

            # 判断查询时间范围与分界点的关系
            if end_date <= self.switch_time:
                # 完全在分界点之前，只使用Excel
                logger.info("查询时间范围完全在分界点之前，使用Excel数据源")
                try:
                    records = self._get_expenses_from_excel(start_date, end_date)
                    all_records.extend(records)
                except Exception as e:
                    error_msg = f"Excel数据源获取失败: {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            elif start_date > self.switch_time:
                # 完全在分界点之后，只使用Google Sheet
                logger.info("查询时间范围完全在分界点之后，使用Google Sheet数据源")
                try:
                    records = self._get_expenses_from_google_sheet(start_date, end_date)
                    all_records.extend(records)
                except Exception as e:
                    error_msg = f"Google Sheet数据源获取失败: {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            else:
                # 跨越分界点，需要从两个数据源获取数据
                logger.info("查询时间范围跨越分界点，从两个数据源获取数据")

                # 从Excel获取分界点之前的数据
                try:
                    excel_records = self._get_expenses_from_excel(start_date, self.switch_time)
                    all_records.extend(excel_records)
                    logger.info(f"从Excel获取到 {len(excel_records)} 条记录")
                except Exception as e:
                    error_msg = f"Excel数据源获取失败: {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)

                # 从Google Sheet获取分界点之后的数据
                try:
                    # 分界点之后的开始时间应该是分界点+1秒
                    google_start = self.switch_time + timedelta(seconds=1)
                    google_records = self._get_expenses_from_google_sheet(google_start, end_date)
                    all_records.extend(google_records)
                    logger.info(f"从Google Sheet获取到 {len(google_records)} 条记录")
                except Exception as e:
                    error_msg = f"Google Sheet数据源获取失败: {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            # 按时间排序合并后的数据
            all_records.sort(key=lambda x: x.expense_date)

            logger.info(f"开支数据获取完成: 总计 {len(all_records)} 条记录")
            if errors:
                logger.warning(f"获取过程中出现 {len(errors)} 个错误: {'; '.join(errors)}")

            return all_records

        except Exception as e:
            logger.error(f"获取开支数据失败: {e}")
            raise ExpenseDataException(f"获取开支数据失败: {e}")

    def _get_expenses_from_excel(self, start_date: datetime, end_date: datetime) -> List[ExpenseRecord]:
        """从Excel文件获取开支数据，转换为Google Sheet格式"""
        try:
            import os
            if not os.path.exists(self.excel_file):
                logger.warning(f"Excel文件不存在: {self.excel_file}")
                return []

            logger.info(f"从Excel文件获取开支数据: {self.excel_file}")

            records = []
            workbook = None

            try:
                workbook = openpyxl.load_workbook(self.excel_file)
                worksheet = workbook.active

                # 获取表头映射
                header_mapping = self._get_excel_header_mapping(worksheet)
                if not header_mapping:
                    logger.error("无法解析Excel表头")
                    return []

                # 解析数据
                for row_num in range(2, worksheet.max_row + 1):  # 从第2行开始（跳过表头）
                    try:
                        record = self._parse_expense_record_from_excel(worksheet, row_num, header_mapping)
                        if record:
                            records.append(record)
                    except Exception as e:
                        logger.warning(f"解析第{row_num}行开支数据失败: {e}")
                        continue

            finally:
                if workbook:
                    workbook.close()

            # 按时间范围过滤
            filtered_records = self._filter_by_date_range(records, start_date, end_date, "expense_date")
            logger.info(f"Excel文件共 {len(records)} 条记录，时间过滤后 {len(filtered_records)} 条")

            return filtered_records

        except Exception as e:
            logger.error(f"从Excel获取开支数据失败: {e}")
            # 根据错误处理策略，返回空数据但记录错误
            return []

    def _get_excel_header_mapping(self, worksheet) -> Dict[str, int]:
        """获取Excel表头到列索引的映射"""
        try:
            header_mapping = {}
            field_mapping = MonthlyJobConfig.EXCEL_FIELD_MAPPING

            # 读取第一行表头
            for col_idx in range(1, worksheet.max_column + 1):
                cell_value = worksheet.cell(row=1, column=col_idx).value
                if cell_value:
                    cell_value = str(cell_value).strip()
                    # 查找匹配的字段
                    for field_key, field_name in field_mapping.items():
                        if cell_value == field_name:
                            header_mapping[field_key] = col_idx
                            break

            logger.debug(f"Excel表头映射: {header_mapping}")
            return header_mapping

        except Exception as e:
            logger.error(f"解析Excel表头失败: {e}")
            return {}

    def _parse_expense_record_from_excel(self, worksheet, row_num: int, header_mapping: Dict[str, int]) -> Optional[ExpenseRecord]:
        """从Excel行解析开支记录，转换为Google Sheet格式"""
        try:
            from Parser import parse_datetime

            # 获取各列数据（使用动态映射）
            def get_field_value(field_key: str, is_numeric: bool = False):
                if field_key not in header_mapping:
                    return None
                col_idx = header_mapping[field_key]
                if is_numeric:
                    return get_cell_numeric_value(worksheet, row_num, col_idx)
                else:
                    return get_cell_value(worksheet, row_num, col_idx)

            # 获取基础字段
            submit_time_str = get_field_value("submit_time")
            report_date_str = get_field_value("report_date")
            expense_person = get_field_value("expense_person")
            expense_category = get_field_value("expense_category")
            expense_category_extra = get_field_value("expense_category_extra")
            expense_amount = get_field_value("expense_amount", is_numeric=True)
            expense_date_str = get_field_value("expense_date")
            remark = get_field_value("remark")
            receipt_upload = get_field_value("receipt_upload")

            # 数据验证
            if not expense_person or not expense_category:
                return None

            # 处理负数金额（丢弃该条数据）
            if expense_amount is not None and expense_amount < 0:
                logger.warning(f"第{row_num}行开支金额为负数({expense_amount})，丢弃该条数据")
                return None

            if expense_amount is None or expense_amount == 0:
                return None

            # 解析时间
            timestamp = parse_datetime(submit_time_str) if submit_time_str else None
            report_date = parse_datetime(report_date_str) if report_date_str else None
            expense_date = parse_datetime(expense_date_str) if expense_date_str else None

            if not timestamp or not expense_date:
                logger.warning(f"第{row_num}行关键时间字段解析失败，跳过")
                return None

            # 处理未来日期（记录日志但正常处理）
            from datetime import datetime
            now = datetime.now(expense_date.tzinfo if expense_date.tzinfo else None)
            if expense_date > now:
                logger.info(f"第{row_num}行开支项目发生时间为未来日期: {expense_date}")

            # 处理开支类别补充字段
            final_category = expense_category
            final_remark = remark or ""

            if expense_category == "其它" and expense_category_extra:
                # 保持"其它"，补充内容放到备注开头
                if final_remark:
                    final_remark = f"{expense_category_extra}; {final_remark}"
                else:
                    final_remark = expense_category_extra

            # 创建统一格式的记录（Google Sheet格式）
            return ExpenseRecord(
                timestamp=timestamp,
                report_date=report_date or timestamp,
                expense_person=expense_person,
                expense_category=final_category,
                expense_amount=expense_amount,
                receipt_upload=receipt_upload or "",
                remark=final_remark,
                expense_date=expense_date
            )

        except Exception as e:
            logger.error(f"解析第{row_num}行开支记录失败: {e}")
            return None

    def _filter_by_date_range(self, records: List[ExpenseRecord],
                            start_date: datetime, end_date: datetime,
                            filter_field: str = "expense_date") -> List[ExpenseRecord]:
        """按时间范围过滤记录"""
        filtered_records = []
        for record in records:
            # 根据指定字段进行过滤
            filter_time = getattr(record, filter_field)
            if filter_time and start_date <= filter_time <= end_date:
                filtered_records.append(record)
        return filtered_records

    def _get_expenses_from_google_sheet(self, start_date: datetime, end_date: datetime) -> List[ExpenseRecord]:
        """从Google Sheet获取开支数据"""
        try:
            from google_sheets_pool import get_sheets_manager

            logger.info(f"从Google Sheet获取开支数据: {self.google_sheet_name}/{self.google_worksheet_name}")

            # 获取Google Sheets管理器
            sheets_manager = get_sheets_manager()
            if not sheets_manager:
                raise ExpenseDataException("Google Sheets管理器不可用")

            # 读取工作表数据
            def read_expense_sheet(client, sheet_name, worksheet_name):
                sheet = client.open(sheet_name)
                worksheet = sheet.worksheet(worksheet_name)
                return worksheet.get_all_values()

            # 使用同步方式获取数据，避免事件循环冲突
            try:
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                data = loop.run_until_complete(
                    sheets_manager.execute_operation(
                        read_expense_sheet,
                        self.google_sheet_name,
                        self.google_worksheet_name
                    )
                )
                loop.close()
            except Exception as e:
                logger.error(f"Google Sheet异步操作失败: {e}")
                # 尝试直接访问（如果可能）
                raise ExpenseDataException(f"Google Sheet数据获取失败: {e}")

            if not data:
                logger.warning("Google Sheet中没有数据")
                return []

            # 解析数据
            records = []
            header_mapping = self._get_google_sheet_header_mapping(data[0])

            if not header_mapping:
                logger.error("无法解析Google Sheet表头")
                return []

            for row_idx, row_data in enumerate(data[1:], start=2):  # 跳过表头
                try:
                    record = self._parse_expense_record_from_google_sheet(row_data, row_idx, header_mapping)
                    if record:
                        records.append(record)
                except Exception as e:
                    logger.warning(f"解析第{row_idx}行Google Sheet数据失败: {e}")
                    continue

            # 按时间范围过滤
            filtered_records = self._filter_by_date_range(records, start_date, end_date, "expense_date")
            logger.info(f"Google Sheet共 {len(records)} 条记录，时间过滤后 {len(filtered_records)} 条")

            return filtered_records

        except Exception as e:
            logger.error(f"从Google Sheet获取开支数据失败: {e}")
            # 根据错误处理策略，返回空数据但记录错误
            return []

    def _get_google_sheet_header_mapping(self, header_row: List[str]) -> Dict[str, int]:
        """获取Google Sheet表头到列索引的映射"""
        try:
            header_mapping = {}
            field_mapping = MonthlyJobConfig.GOOGLE_SHEET_FIELD_MAPPING

            for col_idx, cell_value in enumerate(header_row):
                if cell_value:
                    cell_value = str(cell_value).strip()
                    # 查找匹配的字段
                    for field_key, field_name in field_mapping.items():
                        if cell_value == field_name:
                            header_mapping[field_key] = col_idx
                            break

            logger.debug(f"Google Sheet表头映射: {header_mapping}")
            return header_mapping

        except Exception as e:
            logger.error(f"解析Google Sheet表头失败: {e}")
            return {}

    def _parse_expense_record_from_google_sheet(self, row_data: List[str], row_idx: int,
                                              header_mapping: Dict[str, int]) -> Optional[ExpenseRecord]:
        """从Google Sheet行解析开支记录"""
        try:
            from Parser import parse_datetime

            # 获取各列数据（使用动态映射）
            def get_field_value(field_key: str, is_numeric: bool = False):
                if field_key not in header_mapping:
                    return None
                col_idx = header_mapping[field_key]
                if col_idx >= len(row_data):
                    return None
                value = row_data[col_idx].strip() if row_data[col_idx] else None
                if not value:
                    return None
                if is_numeric:
                    try:
                        return float(value)
                    except ValueError:
                        return None
                return value

            # 获取基础字段
            timestamp_str = get_field_value("timestamp")
            report_date_str = get_field_value("report_date")
            expense_person = get_field_value("expense_person")
            expense_category = get_field_value("expense_category")
            expense_amount = get_field_value("expense_amount", is_numeric=True)
            expense_date_str = get_field_value("expense_date")
            remark = get_field_value("remark")
            receipt_upload = get_field_value("receipt_upload")

            # 数据验证
            if not expense_person or not expense_category:
                return None

            # 处理负数金额（丢弃该条数据）
            if expense_amount is not None and expense_amount < 0:
                logger.warning(f"第{row_idx}行开支金额为负数({expense_amount})，丢弃该条数据")
                return None

            if expense_amount is None or expense_amount == 0:
                return None

            # 解析时间
            timestamp = parse_datetime(timestamp_str) if timestamp_str else None
            report_date = parse_datetime(report_date_str) if report_date_str else None
            expense_date = parse_datetime(expense_date_str) if expense_date_str else None

            if not timestamp or not expense_date:
                logger.warning(f"第{row_idx}行关键时间字段解析失败，跳过")
                return None

            # 处理未来日期（记录日志但正常处理）
            from datetime import datetime
            now = datetime.now(expense_date.tzinfo if expense_date.tzinfo else None)
            if expense_date > now:
                logger.info(f"第{row_idx}行开支项目发生时间为未来日期: {expense_date}")

            # 创建记录
            return ExpenseRecord(
                timestamp=timestamp,
                report_date=report_date or timestamp,
                expense_person=expense_person,
                expense_category=expense_category,
                expense_amount=expense_amount,
                receipt_upload=receipt_upload or "",
                remark=remark or "",
                expense_date=expense_date
            )

        except Exception as e:
            logger.error(f"解析第{row_idx}行Google Sheet记录失败: {e}")
            return None

# 月报数据记录管理器
class MonthlyReportManager:
    """月报数据管理器"""

    def __init__(self, google_sheets_manager):
        self.sheets_manager = google_sheets_manager
        self.report_sheet_name = MonthlyJobConfig.MONTHLY_REPORT_SHEET_NAME

    async def record_monthly_report(self, report_data: MonthlyReport) -> bool:
        """
        记录月报数据到Google Sheet

        Args:
            report_data: 月报数据

        Returns:
            是否成功记录
        """
        try:
            logger.info(f"开始记录月报数据到Google Sheet: {self.report_sheet_name}")

            # 确保Sheet结构正确
            structure_success = await self._ensure_sheet_structure()
            if not structure_success:
                logger.error("Sheet结构确保失败，尝试使用简单方法")
                structure_success = await self._ensure_sheet_structure_simple()
                if not structure_success:
                    logger.error("Sheet结构确保完全失败")
                    return False

            # 更新或追加月报记录
            success = await self._update_or_append_report(report_data)

            if success:
                logger.info(f"月报数据记录成功: {report_data.report_month}")
            else:
                logger.error(f"月报数据记录失败: {report_data.report_month}")

            return success

        except Exception as e:
            logger.error(f"记录月报数据失败: {e}")
            raise GoogleSheetException(f"记录月报数据失败: {e}")

    async def _ensure_sheet_structure(self) -> bool:
        """确保Sheet结构正确"""
        try:
            # 定义表头
            headers = [
                "报告月份", "报告生成时间", "总盈利", "总工资", "总开支", "净利润",
                "盈利成员数", "亏损成员数", "开工总次数", "主要开支类别", "备注"
            ]

            def _check_and_create_headers(client):
                try:
                    # 尝试打开工作表
                    sheet = client.open(self.report_sheet_name)
                    worksheet = sheet.sheet1

                    # 检查是否有表头
                    existing_headers = worksheet.row_values(1)
                    if not existing_headers or existing_headers != headers:
                        # 设置表头
                        try:
                            clear_response = worksheet.clear()
                            append_response = worksheet.append_row(headers)

                            # 检查操作是否成功（gspread操作会返回字典类型的响应）
                            logger.debug(f"Clear response: {clear_response}")
                            logger.debug(f"Append response: {append_response}")
                            logger.info(f"已设置Google Sheet表头: {self.report_sheet_name}")
                        except Exception as inner_e:
                            # 如果是Response对象被误当作异常，检查其内容
                            if hasattr(inner_e, '__str__') and "Response [200]" in str(inner_e):
                                logger.info("Google Sheets操作成功（Response [200]）")
                            else:
                                logger.error(f"设置表头操作失败: {inner_e}")
                                raise

                    return True

                except Exception as e:
                    # 检查是否是Response对象被误当作异常
                    if hasattr(e, 'status_code'):
                        logger.debug(f"收到Response对象: {e}, status_code: {e.status_code}")
                        if e.status_code == 200:
                            logger.info("操作成功完成（HTTP 200）")
                            return True

                    # 检查是否是Response [200]字符串
                    if "Response [200]" in str(e):
                        logger.info("Google Sheets操作成功（Response [200]）")
                        return True

                    if "not found" in str(e).lower():
                        # Sheet不存在，需要创建
                        logger.info(f"Google Sheet不存在，需要手动创建: {self.report_sheet_name}")
                        return False
                    else:
                        logger.error(f"设置表头时出现异常: {e}")
                        # 不要抛出异常，返回False表示失败
                        return False

            # 注意：execute_operation会自动传递client参数作为第一个参数
            return await self.sheets_manager.execute_operation(_check_and_create_headers)

        except Exception as e:
            logger.error(f"确保Sheet结构失败: {e}")
            return False

    async def _ensure_sheet_structure_simple(self) -> bool:
        """使用简单方法确保Sheet结构正确（备用方案）"""
        try:
            from config import Config
            import gspread
            from oauth2client.service_account import ServiceAccountCredentials

            # 定义表头
            headers = [
                "报告月份", "报告生成时间", "总盈利", "总工资", "总开支", "净利润",
                "盈利成员数", "亏损成员数", "开工总次数", "主要开支类别", "备注"
            ]

            # 使用现有Storage管理器的方法
            scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
            creds = ServiceAccountCredentials.from_json_keyfile_name(Config.CREDENTIALS_FILE, scope)
            client = gspread.authorize(creds)

            # 打开Google Sheet
            sheet = client.open(self.report_sheet_name)
            worksheet = sheet.sheet1

            # 检查是否有表头
            existing_headers = worksheet.row_values(1)
            if not existing_headers or existing_headers != headers:
                # 设置表头
                worksheet.clear()
                worksheet.append_row(headers)
                logger.info(f"已设置Google Sheet表头: {self.report_sheet_name}")

            return True

        except Exception as e:
            logger.error(f"简单方法确保Sheet结构失败: {e}")
            return False

    async def _update_or_append_report(self, report_data: MonthlyReport) -> bool:
        """更新或追加月报记录"""
        try:
            def _update_report_operation(client):
                sheet = client.open(self.report_sheet_name)
                worksheet = sheet.sheet1

                # 查找是否已存在该月份的记录
                all_values = worksheet.get_all_values()
                target_row = None

                for i, row in enumerate(all_values[1:], start=2):  # 跳过表头
                    if row and row[0] == report_data.report_month:
                        target_row = i
                        break

                # 准备数据行
                data_row = [
                    report_data.report_month,
                    report_data.report_time.strftime('%Y-%m-%d %H:%M:%S'),
                    report_data.total_profit,
                    report_data.total_salary,
                    report_data.total_expense,
                    report_data.net_profit,
                    report_data.profit_members_count,
                    report_data.loss_members_count,
                    report_data.total_work_sessions,
                    report_data.main_expense_category,
                    report_data.remark
                ]

                try:
                    if target_row:
                        # 更新现有记录
                        for col, value in enumerate(data_row, start=1):
                            update_response = worksheet.update_cell(target_row, col, value)
                            logger.debug(f"Update cell response: {update_response}")
                        logger.info(f"更新现有月报记录: {report_data.report_month}, 行号: {target_row}")
                    else:
                        # 追加新记录
                        append_response = worksheet.append_row(data_row)
                        logger.debug(f"Append row response: {append_response}")
                        logger.info(f"追加新月报记录: {report_data.report_month}")
                except Exception as inner_e:
                    # 如果是Response对象被误当作异常，检查其内容
                    if hasattr(inner_e, '__str__') and "Response [200]" in str(inner_e):
                        logger.info("Google Sheets更新操作成功（Response [200]）")
                    else:
                        logger.error(f"更新月报记录失败: {inner_e}")
                        raise

                return True

            return await self.sheets_manager.execute_operation(_update_report_operation)

        except Exception as e:
            logger.error(f"更新或追加月报记录失败: {e}")
            return False

    async def get_historical_reports(self, months: int = 12) -> List[MonthlyReport]:
        """获取历史月报数据"""
        try:
            async def _get_reports_operation(client):
                sheet = client.open(self.report_sheet_name)
                worksheet = sheet.sheet1

                all_values = worksheet.get_all_values()
                reports = []

                # 跳过表头，解析数据
                for row in all_values[1:]:
                    if len(row) >= 11 and row[0]:  # 确保有足够的列和月份不为空
                        try:
                            report = MonthlyReport(
                                report_month=row[0],
                                report_time=datetime.strptime(row[1], '%Y-%m-%d %H:%M:%S'),
                                total_profit=float(row[2]) if row[2] else 0.0,
                                total_salary=float(row[3]) if row[3] else 0.0,
                                total_expense=float(row[4]) if row[4] else 0.0,
                                net_profit=float(row[5]) if row[5] else 0.0,
                                profit_members_count=int(row[6]) if row[6] else 0,
                                loss_members_count=int(row[7]) if row[7] else 0,
                                total_work_sessions=int(row[8]) if row[8] else 0,
                                main_expense_category=row[9] if row[9] else "",
                                remark=row[10] if row[10] else ""
                            )
                            reports.append(report)
                        except Exception as e:
                            logger.warning(f"解析历史月报记录失败: {row[0]}, {e}")
                            continue

                # 按月份排序，返回最近的记录
                reports.sort(key=lambda x: x.report_month, reverse=True)
                return reports[:months]

            return await self.sheets_manager.execute_operation(_get_reports_operation)

        except Exception as e:
            logger.error(f"获取历史月报数据失败: {e}")
            return []

# 净利润报表生成器
class ProfitReportGenerator:
    """净利润报表生成器"""

    def __init__(self):
        self.currency_format = MonthlyJobConfig.CURRENCY_FORMAT
        self.percentage_format = MonthlyJobConfig.PERCENTAGE_FORMAT

    def generate_monthly_profit_report(self, profit_data: Dict, expense_data: List[ExpenseRecord],
                                     report_month: str, total_profit: float) -> str:
        """
        生成月度净利润报表

        Args:
            profit_data: 盈利数据 (来自generate_report)
            expense_data: 开支数据
            report_month: 报告月份
            total_profit: 总盈利

        Returns:
            格式化的报表文本
        """
        try:
            logger.info(f"开始生成月度净利润报表: {report_month}")

            # 计算总开支
            total_expense = sum(record.expense_amount for record in expense_data)

            # 计算净利润
            total_salary = sum(data["salary"] for data in profit_data.values())
            net_profit = total_profit - total_salary - total_expense

            # 生成各个板块
            overview_section = self._generate_overview_section(total_profit, total_expense, net_profit, report_month)
            member_section = self._generate_member_contribution_section(profit_data, total_profit)
            salary_section = self._generate_salary_section(profit_data)
            expense_section = self._generate_expense_section(expense_data)

            # 组合完整报表
            report_text = f"""📊 {report_month} 月度净利润报表

{overview_section}

{member_section}

{salary_section}

{expense_section}

📝 报表生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            logger.info(f"月度净利润报表生成完成: {report_month}")
            return report_text

        except Exception as e:
            logger.error(f"生成月度净利润报表失败: {e}")
            raise ReportGenerationException(f"生成月度净利润报表失败: {e}")

    def _generate_overview_section(self, total_profit: float, total_expense: float,
                                 net_profit: float, report_month: str) -> str:
        """生成概览板块"""
        try:
            # 解析月份
            year, month = report_month.split('-')
            month_name = f"{year}年{int(month)}月"

            overview = f"""📅 报告周期：{month_name}
💰 总盈利：{self._format_currency(total_profit)}
🧾 总支出：{self._format_currency(-total_expense)}
🏦 净利润：{self._format_currency(net_profit)}"""

            return overview

        except Exception as e:
            logger.error(f"生成概览板块失败: {e}")
            return "📅 概览信息生成失败"

    def _generate_member_contribution_section(self, profit_data: Dict, total_profit: float) -> str:
        """生成成员贡献板块"""
        try:
            if not profit_data:
                return "👥 成员贡献：无数据"

            # 计算成员贡献
            contributions = []
            for member, data in profit_data.items():
                profit = data["profit"]
                percentage = (profit / total_profit * 100) if total_profit != 0 else 0
                contributions.append({
                    'member': member,
                    'profit': profit,
                    'percentage': percentage
                })

            # 按盈利排序
            contributions.sort(key=lambda x: x['profit'], reverse=True)

            # 分离盈利和亏损成员
            profit_members = [c for c in contributions if c['profit'] > 0]
            loss_members = [c for c in contributions if c['profit'] < 0]

            section = "👥 成员贡献\n"

            # 盈利成员
            if profit_members:
                section += "🟢 盈利贡献榜：\n"
                for contrib in profit_members:
                    section += f"  {contrib['member']}: {self._format_currency(contrib['profit'])} ({self._format_percentage(contrib['percentage'])})\n"

            # 亏损成员
            if loss_members:
                section += "🔴 亏损拖累榜：\n"
                for contrib in loss_members:
                    section += f"  {contrib['member']}: {self._format_currency(contrib['profit'])} ({self._format_percentage(contrib['percentage'])})\n"

            # 汇总
            section += f"📊 合计: {self._format_currency(total_profit)} (100%)"

            return section.rstrip()

        except Exception as e:
            logger.error(f"生成成员贡献板块失败: {e}")
            return "👥 成员贡献：生成失败"

    def _generate_salary_section(self, profit_data: Dict) -> str:
        """生成成员工资板块"""
        try:
            if not profit_data:
                return "💰 成员工资：无数据"

            section = "💰 成员工资\n"
            section += "| 成员 | 工资 | 开工次数 | 备注 |\n"
            section += "|------|------|----------|------|\n"

            # 按工资排序
            sorted_members = sorted(profit_data.items(), key=lambda x: x[1]["salary"], reverse=True)

            for member, data in sorted_members:
                salary = data["salary"]
                work_sessions = data["count"]
                section += f"| {member} | {self._format_currency(salary)} | {work_sessions} | - |\n"

            # 汇总
            total_salary = sum(data["salary"] for data in profit_data.values())
            total_sessions = sum(data["count"] for data in profit_data.values())
            section += f"| **合计** | **{self._format_currency(total_salary)}** | **{total_sessions}** | - |"

            return section

        except Exception as e:
            logger.error(f"生成工资板块失败: {e}")
            return "💰 成员工资：生成失败"

    def _generate_expense_section(self, expense_data: List[ExpenseRecord]) -> str:
        """生成开支明细板块"""
        try:
            if not expense_data:
                return "🧾 开支明细：无数据"

            # 计算开支汇总
            expense_summary = self._calculate_expense_summary(expense_data)
            total_expense = sum(expense_summary.values())

            if total_expense == 0:
                return "🧾 开支明细：无开支"

            section = "🧾 开支明细（汇总版）\n"
            section += "| 开支项 | 金额 | 占总支出的比例 |\n"
            section += "|--------|------|----------------|\n"

            # 按金额排序
            sorted_expenses = sorted(expense_summary.items(), key=lambda x: x[1], reverse=True)

            for category, amount in sorted_expenses:
                percentage = (amount / total_expense * 100) if total_expense > 0 else 0
                section += f"| {category} | {self._format_currency(amount)} | {self._format_percentage(percentage)} |\n"

            # 汇总
            section += f"| **合计** | **{self._format_currency(total_expense)}** | **100%** |"

            return section

        except Exception as e:
            logger.error(f"生成开支明细板块失败: {e}")
            return "🧾 开支明细：生成失败"

    def _calculate_expense_summary(self, expense_data: List[ExpenseRecord]) -> Dict[str, float]:
        """计算开支汇总"""
        summary = defaultdict(float)

        for record in expense_data:
            category = record.expense_category
            # 新的数据模型中，expense_category_extra的内容已经合并到remark中
            # 如果是"其它"类别且备注中有内容，可以从备注中提取补充信息
            if category == "其它" and record.remark:
                # 尝试从备注开头提取补充信息（格式：补充信息; 原备注）
                remark_parts = record.remark.split(';', 1)
                if len(remark_parts) > 1:
                    # 有分号分隔，说明可能有补充信息
                    extra_info = remark_parts[0].strip()
                    if extra_info and len(extra_info) < 20:  # 合理的补充信息长度
                        category = f"{category}-{extra_info}"

            summary[category] += record.expense_amount

        return dict(summary)

    def _format_currency(self, amount: float) -> str:
        """格式化货币显示"""
        return self.currency_format.format(amount)

    def _format_percentage(self, percentage: float) -> str:
        """格式化百分比显示"""
        return self.percentage_format.format(percentage)

# 完整月度工作流管理器
class CompleteMonthlyJobManager:
    """完整月度工作流管理器"""

    def __init__(self):
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool

        # 初始化各个管理器
        self.expense_manager = ExpenseDataManager()

        # 创建Google Sheets连接池和管理器
        pool = GoogleSheetsConnectionPool()  # 使用默认参数
        sheets_manager = GoogleSheetsManager(pool)
        self.report_manager = MonthlyReportManager(sheets_manager)
        self.profit_generator = ProfitReportGenerator()

    async def execute_complete_monthly_job(self, context) -> Dict[str, Any]:
        """
        执行完整的月度工作流

        Args:
            context: Telegram bot context

        Returns:
            执行结果统计
        """
        result = {
            'success': False,
            'salary_check_result': None,
            'profit_data': None,
            'expense_data': None,
            'monthly_report': None,
            'report_text': None,
            'errors': []
        }

        try:
            logger.info("开始执行完整月度工作流")

            # 1. 计算时间范围
            start_date, end_date = get_monthly_salary_check_range()
            report_month = start_date.strftime('%Y-%m')

            logger.info(f"月度工作流时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

            # 2. 执行工资检查
            logger.info("步骤1: 执行工资检查")
            salary_result = await self._execute_salary_check(start_date, end_date)
            result['salary_check_result'] = salary_result

            if salary_result.get('errors'):
                result['errors'].extend(salary_result['errors'])

            # 3. 收集月度数据
            logger.info("步骤2: 收集月度数据")
            profit_data, total_profit, expense_data = await self._collect_monthly_data(start_date, end_date)
            result['profit_data'] = profit_data
            result['expense_data'] = expense_data

            # 4. 计算月度汇总
            logger.info("步骤3: 计算月度汇总")
            monthly_report = await self._calculate_monthly_summary(
                profit_data, expense_data, total_profit, report_month
            )
            result['monthly_report'] = monthly_report

            # 5. 记录到Google Sheet
            logger.info("步骤4: 记录到Google Sheet")
            try:
                sheet_success = await self.report_manager.record_monthly_report(monthly_report)
                if not sheet_success:
                    result['errors'].append("记录到Google Sheet失败")
            except Exception as e:
                error_msg = f"记录到Google Sheet异常: {e}"
                logger.error(error_msg)
                result['errors'].append(error_msg)

            # 6. 生成净利润报表
            logger.info("步骤5: 生成净利润报表")
            report_text = self.profit_generator.generate_monthly_profit_report(
                profit_data, expense_data, report_month, total_profit
            )
            result['report_text'] = report_text

            # 7. 发送通知
            logger.info("步骤6: 发送月度通知")
            try:
                await self._send_monthly_notification(context, report_text, monthly_report, salary_result)
            except Exception as e:
                error_msg = f"发送月度通知失败: {e}"
                logger.error(error_msg)
                result['errors'].append(error_msg)

            # 判断整体成功状态：如果有错误则为失败
            if result['errors']:
                result['success'] = False
                logger.warning(f"完整月度工作流执行完成，但有 {len(result['errors'])} 个错误")
            else:
                result['success'] = True
                logger.info("完整月度工作流执行成功")

        except Exception as e:
            error_msg = f"完整月度工作流执行失败: {e}"
            logger.error(error_msg)
            result['errors'].append(error_msg)

            # 发送失败通知
            try:
                await self._send_error_notification(context, error_msg)
            except Exception as notify_error:
                logger.error(f"发送失败通知失败: {notify_error}")

        return result

    async def _execute_salary_check(self, start_date: datetime, end_date: datetime) -> Dict:
        """执行工资检查"""
        try:
            # 调用现有的工资检查函数
            result = check_and_update_salaries(
                start_date=start_date,
                end_date=end_date,
                filter_by_report_time=True
            )
            return result
        except Exception as e:
            logger.error(f"工资检查执行失败: {e}")
            return {
                'checked_records': 0,
                'inconsistent_records': 0,
                'updated_records': 0,
                'errors': [str(e)]
            }

    async def _collect_monthly_data(self, start_date: datetime, end_date: datetime) -> Tuple:
        """收集月度数据"""
        try:
            # 获取盈利数据
            profit_data, total_profit = generate_report(start_date, end_date)

            # 获取开支数据
            expense_data = self.expense_manager.get_monthly_expenses(start_date, end_date)

            logger.info(f"数据收集完成: 盈利数据 {len(profit_data)} 人, 开支数据 {len(expense_data)} 条")

            return profit_data, total_profit, expense_data

        except Exception as e:
            logger.error(f"收集月度数据失败: {e}")
            raise

    async def _calculate_monthly_summary(self, profit_data: Dict, expense_data: List[ExpenseRecord],
                                       total_profit: float, report_month: str) -> MonthlyReport:
        """计算月度汇总"""
        try:
            # 计算各项统计
            total_salary = sum(data["salary"] for data in profit_data.values())
            total_expense = sum(record.expense_amount for record in expense_data)
            net_profit = total_profit - total_salary - total_expense

            # 统计成员数量
            profit_members_count = sum(1 for data in profit_data.values() if data["profit"] > 0)
            loss_members_count = sum(1 for data in profit_data.values() if data["profit"] < 0)

            # 统计开工次数
            total_work_sessions = sum(data["count"] for data in profit_data.values())

            # 计算主要开支类别
            expense_summary = self.profit_generator._calculate_expense_summary(expense_data)
            main_expense_category = max(expense_summary.items(), key=lambda x: x[1])[0] if expense_summary else "无"

            monthly_report = MonthlyReport(
                report_month=report_month,
                report_time=datetime.now(),
                total_profit=total_profit,
                total_salary=total_salary,
                total_expense=total_expense,
                net_profit=net_profit,
                profit_members_count=profit_members_count,
                loss_members_count=loss_members_count,
                total_work_sessions=total_work_sessions,
                main_expense_category=main_expense_category,
                remark=""
            )

            logger.info(f"月度汇总计算完成: 净利润 {net_profit:+.0f}")
            return monthly_report

        except Exception as e:
            logger.error(f"计算月度汇总失败: {e}")
            raise

    async def _send_monthly_notification(self, context, report_text: str, summary: MonthlyReport, salary_result: Dict):
        """发送月度通知"""
        try:
            # 构建通知消息
            notification = f"""✅ 月度工作流执行完成

📊 工资检查结果：
  - 检查记录：{salary_result.get('checked_records', 0)} 条
  - 更新记录：{salary_result.get('updated_records', 0)} 条

📈 月度汇总：
  - 净利润：{summary.net_profit:+,.0f}
  - 盈利成员：{summary.profit_members_count} 人
  - 开工次数：{summary.total_work_sessions} 次

📄 详细报表将在下一条消息发送"""

            # 获取bot实例并发送消息
            bot = context.bot if context and hasattr(context, 'bot') else None
            chat_id = None

            # 尝试获取chat_id
            if context and hasattr(context, 'job') and context.job and hasattr(context.job, 'data') and context.job.data:
                chat_id = context.job.data.get('chat_id')

            # 如果没有从context获取到chat_id，使用配置的默认群组ID
            if not chat_id:
                chat_id = Config.GROUP_CHAT_ID

            # 检查bot和chat_id是否有效
            if bot and chat_id:
                # 发送通知消息
                await bot.send_message(chat_id=chat_id, text=notification)

                # 发送详细报表（分段发送避免消息过长）
                await self._send_report_in_chunks(bot, chat_id, report_text)

                logger.info(f"月度通知已发送到群组: {chat_id}")
            else:
                # 如果无法发送Telegram通知，至少记录通知内容到日志
                logger.warning(f"无法发送通知 - bot: {bot is not None}, chat_id: {chat_id}")
                logger.info("月度通知内容:")
                logger.info(notification)
                logger.info("详细报表内容:")
                # 将报表内容按行记录到日志
                for line in report_text.split('\n'):
                    if line.strip():
                        logger.info(f"  {line}")
                logger.info("月度通知记录完成（仅日志）")

        except Exception as e:
            logger.error(f"发送月度通知失败: {e}")

    async def _send_report_in_chunks(self, bot, chat_id: int, report_text: str):
        """分段发送报表"""
        try:
            # 按段落分割报表
            sections = report_text.split('\n\n')
            current_chunk = ""

            for section in sections:
                # 检查当前块加上新段落是否超过长度限制
                if len(current_chunk + section) > 3500:  # 留一些余量
                    if current_chunk:
                        await bot.send_message(chat_id=chat_id, text=current_chunk, parse_mode="Markdown")
                        current_chunk = section
                    else:
                        # 单个段落太长，直接发送
                        await bot.send_message(chat_id=chat_id, text=section, parse_mode="Markdown")
                else:
                    current_chunk = current_chunk + "\n\n" + section if current_chunk else section

            # 发送最后一块
            if current_chunk:
                await bot.send_message(chat_id=chat_id, text=current_chunk, parse_mode="Markdown")

        except Exception as e:
            logger.error(f"分段发送报表失败: {e}")
            # 降级为纯文本发送
            try:
                await bot.send_message(chat_id=chat_id, text=report_text)
            except Exception as fallback_error:
                logger.error(f"降级发送也失败: {fallback_error}")

    async def _send_error_notification(self, context, error_msg: str):
        """发送错误通知"""
        try:
            message = f"""❌ 月度工作流执行失败

❗ 错误信息: {error_msg}
🔧 建议联系技术支持检查系统状态"""

            bot = context.bot
            chat_id = context.job.data.get('chat_id') if context.job.data else None

            # 如果没有从context获取到chat_id，使用配置的默认群组ID
            if not chat_id:
                chat_id = Config.GROUP_CHAT_ID

            if chat_id:
                await bot.send_message(chat_id=chat_id, text=message)
                logger.info(f"月度工作流错误通知已发送到群组: {chat_id}")
            else:
                logger.warning("未配置群组ID，无法发送月度工作流错误通知")

        except Exception as e:
            logger.error(f"发送错误通知失败: {e}")

# 全局实例
monthly_job_manager = CompleteMonthlyJobManager()

# 主要的异步任务函数
async def complete_monthly_job(context):
    """
    完整月度任务的异步入口函数
    每月1日 13:00 (UTC+4) 执行，整合所有月度任务
    """
    try:
        logger.info("开始执行完整月度任务")

        # 执行完整的月度工作流
        result = await monthly_job_manager.execute_complete_monthly_job(context)

        if result['success']:
            logger.info("完整月度任务执行成功")
        else:
            logger.error(f"完整月度任务执行失败: {result['errors']}")

        return result

    except Exception as e:
        error_msg = f"完整月度任务执行异常: {e}"
        logger.error(error_msg)

        # 发送失败通知
        try:
            await monthly_job_manager._send_error_notification(context, error_msg)
        except Exception as notify_error:
            logger.error(f"发送失败通知失败: {notify_error}")

        return {
            'success': False,
            'errors': [error_msg]
        }

def get_complete_monthly_job_schedule_time():
    """获取完整月度任务的调度时间（UTC时间）"""
    env = ENV

    # 目标时间：UTC+4 13:00
    # 转换为UTC时间用于调度
    if env == 'prod':  # UTC+8环境
        return dt_time(hour=5, minute=0)   # UTC+4 13:00 = UTC 09:00 = UTC+8 17:00 → UTC 05:00
    else:  # UTC+5环境
        return dt_time(hour=8, minute=0)   # UTC+4 13:00 = UTC 09:00 = UTC+5 14:00 → UTC 08:00

def manual_complete_monthly_job(target_year=None, target_month=None):
    """
    手动执行完整月度任务

    Args:
        target_year: 目标年份，默认为上个月
        target_month: 目标月份，默认为上个月

    Returns:
        Dict: 执行结果
    """
    try:
        import asyncio
        from Parser import TIME_ZONE

        if target_year is None or target_month is None:
            # 使用默认的上个月范围
            start_date, end_date = get_last_month_range()
        else:
            # 使用指定的年月
            from datetime import datetime
            start_date = datetime(target_year, target_month, 1, 12, 0, 0, tzinfo=TIME_ZONE)

            # 下个月的第一天 11:59:59
            next_month = target_month + 1
            next_year = target_year
            if next_month > 12:
                next_month = 1
                next_year += 1
            end_date = datetime(next_year, next_month, 1, 11, 59, 59, 999999, tzinfo=TIME_ZONE)

        logger.info(f"手动执行完整月度任务: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

        # 创建模拟的context对象
        class MockContext:
            def __init__(self):
                # 尝试获取真实的bot实例
                try:
                    from config import Config
                    from telegram import Bot
                    self.bot = Bot(token=Config.BOT_TOKEN)
                    logger.info("手动执行时成功创建bot实例")
                except Exception as e:
                    logger.warning(f"手动执行时无法创建bot实例: {e}")
                    self.bot = None
                self.job = MockJob()

        class MockJob:
            def __init__(self):
                # 手动执行时也发送通知到默认群组
                from config import Config
                self.data = {'chat_id': Config.GROUP_CHAT_ID}

        context = MockContext()

        # 运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(monthly_job_manager.execute_complete_monthly_job(context))
        finally:
            loop.close()

        result['manual_execution'] = True
        result['target_period'] = {
            'year': start_date.year,
            'month': start_date.month,
            'start_date': start_date.strftime('%Y-%m-%d %H:%M:%S'),
            'end_date': end_date.strftime('%Y-%m-%d %H:%M:%S')
        }

        return result

    except Exception as e:
        logger.error(f"手动完整月度任务失败: {e}")
        return {
            'manual_execution': True,
            'success': False,
            'errors': [str(e)]
        }

def generate_team_profit_monthly_report():
    """
    根据团队的总体盈利情况、工资、开支情况，做出并记录团队上月盈利情况报告

    注意：此函数已被新的完整月度工作流替代，建议使用：
    - complete_monthly_job() - 异步定时任务
    - manual_complete_monthly_job() - 手动执行
    """
    logger.warning("generate_team_profit_monthly_report() 已被新的完整月度工作流替代")
    return manual_complete_monthly_job()


# 测试函数
def test_check_and_update_salaries():
    """
    测试check_and_update_salaries函数
    """
    try:
        print("📝 开始测试工资检查功能...")
        print(f"📂 Excel文件路径: {SalaryCheckConfig.EXCEL_FILE_PATH}")
        print(f"📄 报告文件路径: {SalaryCheckConfig.REPORT_FILE_PATH}")
        
        # 检查文件是否存在
        if not os.path.exists(SalaryCheckConfig.EXCEL_FILE_PATH):
            print(f"❌ Excel文件不存在: {SalaryCheckConfig.EXCEL_FILE_PATH}")
            return None
        
        # 定义进度回调函数
        def progress_callback(current, total):
            if total > 0:
                percentage = (current / total) * 100
                print(f"⏳ 处理进度: {current}/{total} ({percentage:.1f}%)")
        
        # 调用工资检查函数
        print("🔍 开始执行工资检查...")
        result = check_and_update_salaries(progress_callback)
        
        print("\n📈 测试结果:")
        print(f"  - 总记录数: {result['total_records']}")
        print(f"  - 检查记录数: {result['checked_records']}")
        print(f"  - 不一致记录数: {result['inconsistent_records']}")
        print(f"  - 更新记录数: {result['updated_records']}")
        
        if result['errors']:
            print(f"  - 错误数量: {len(result['errors'])}")
            for i, error in enumerate(result['errors'], 1):
                print(f"    {i}. {error}")
        else:
            print("  - 无错误")
        
        # 检查结果文件是否生成
        if os.path.exists(SalaryCheckConfig.REPORT_FILE_PATH):
            print(f"✓ 检查报告已生成: {SalaryCheckConfig.REPORT_FILE_PATH}")
        else:
            print("⚠️  检查报告未生成")
        
        # 性能统计
        if result['checked_records'] > 0:
            consistency_rate = ((result['checked_records'] - result['inconsistent_records']) / result['checked_records']) * 100
            print(f"📊 数据一致性: {consistency_rate:.1f}%")
        
        print("✓ 测试完成")
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

# 新增测试函数
def test_complete_monthly_job():
    """
    测试完整月度任务功能
    """
    try:
        print("📝 开始测试完整月度任务功能...")

        # 执行手动完整月度任务
        print("🔍 开始执行完整月度任务...")
        result = manual_complete_monthly_job()

        print("\n📈 测试结果:")
        print(f"  - 执行成功: {result.get('success', False)}")
        print(f"  - 手动执行: {result.get('manual_execution', False)}")

        if result.get('target_period'):
            period = result['target_period']
            print(f"  - 目标期间: {period['year']}年{period['month']}月")

        if result.get('salary_check_result'):
            salary_result = result['salary_check_result']
            print(f"  - 工资检查: 检查{salary_result.get('checked_records', 0)}条，更新{salary_result.get('updated_records', 0)}条")

        if result.get('monthly_report'):
            report = result['monthly_report']
            print(f"  - 净利润: {report.net_profit:+,.0f}")
            print(f"  - 盈利成员: {report.profit_members_count}人，亏损成员: {report.loss_members_count}人")

        if result.get('errors'):
            print(f"  - 错误数量: {len(result['errors'])}")
            for i, error in enumerate(result['errors'], 1):
                print(f"    {i}. {error}")
        else:
            print("  - 无错误")

        print("✓ 测试完成")
        return result

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_expense_data_manager():
    """
    测试开支数据管理器
    """
    try:
        print("📝 开始测试开支数据管理器...")

        # 创建开支数据管理器
        expense_manager = ExpenseDataManager()

        # 获取上个月的时间范围
        start_time, end_time = get_last_month_range()
        print(f"📅 测试时间范围: {start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}")

        # 获取开支数据
        print("🔍 开始获取开支数据...")
        expense_data = expense_manager.get_monthly_expenses(start_time, end_time)

        print(f"\n📈 测试结果:")
        print(f"  - 开支记录数: {len(expense_data)}")

        if expense_data:
            total_expense = sum(record.expense_amount for record in expense_data)
            print(f"  - 总开支金额: {total_expense:,.2f}")

            # 统计开支类别
            categories = {}
            for record in expense_data:
                category = record.expense_category
                categories[category] = categories.get(category, 0) + record.expense_amount

            print(f"  - 开支类别统计:")
            for category, amount in sorted(categories.items(), key=lambda x: x[1], reverse=True):
                print(f"    {category}: {amount:,.2f}")

        print("✓ 测试完成")
        return expense_data

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 monthlyjob.py 主程序启动")
    print("=" * 50)

    # 选择测试模式
    import sys
    if len(sys.argv) > 1:
        test_mode = sys.argv[1]
    else:
        test_mode = "complete"  # 默认测试完整功能

    if test_mode == "salary":
        print("📋 执行工资检查测试")
        result = test_check_and_update_salaries()
    elif test_mode == "expense":
        print("📋 执行开支数据测试")
        result = test_expense_data_manager()
    elif test_mode == "complete":
        print("📋 执行完整月度任务测试")
        result = test_complete_monthly_job()
    else:
        print(f"❌ 未知的测试模式: {test_mode}")
        print("可用模式: salary, expense, complete")
        sys.exit(1)

    print("=" * 50)
    if result:
        print("✅ 测试执行完成")
    else:
        print("❌ 测试执行失败")
        sys.exit(1)