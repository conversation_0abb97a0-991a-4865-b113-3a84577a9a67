#!/usr/bin/env python3
"""
修复配置 JSON 文件格式的工具脚本
解决 JSON 格式错误和结构问题
"""

import json
import os
import shutil
from datetime import datetime
from typing import Dict, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def backup_file(filepath: str) -> str:
    """备份原文件"""
    if not os.path.exists(filepath):
        return None
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{filepath}.backup_{timestamp}"
    shutil.copy2(filepath, backup_path)
    logger.info(f"已备份文件: {filepath} -> {backup_path}")
    return backup_path

def clean_rebate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """清理和修复 rebate 配置结构"""
    cleaned_config = {}
    global_default = None
    
    logger.info("开始清理 rebate 配置...")
    
    for key, value in config.items():
        if key == "默认比例":
            # 保存全局默认比例
            if isinstance(value, (int, float)) and 0 <= value <= 1:
                global_default = value
                logger.info(f"找到全局默认比例: {value}")
            continue
        
        # 处理场馆配置
        if isinstance(value, dict):
            cleaned_venue = {}
            for person_key, person_value in value.items():
                if isinstance(person_value, (int, float)) and 0 <= person_value <= 1:
                    cleaned_venue[person_key] = person_value
            
            if cleaned_venue:  # 只有当场馆有有效配置时才添加
                cleaned_config[key] = cleaned_venue
                logger.info(f"清理场馆配置: {key} -> {len(cleaned_venue)} 项")
        elif isinstance(value, (int, float)) and 0 <= value <= 1:
            # 处理直接的比例值（错误格式）
            logger.warning(f"发现格式错误，修复: {key}:{value} -> {key}:{{\"默认比例\": {value}}}")
            cleaned_config[key] = {"默认比例": value}
        else:
            logger.warning(f"忽略无效配置项: {key} = {value}")
    
    # 添加全局默认比例（如果存在）
    if global_default is not None:
        cleaned_config["默认比例"] = global_default
    
    logger.info(f"rebate 配置清理完成，共 {len(cleaned_config)} 项")
    return cleaned_config

def clean_salary_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """清理和修复 salary 配置结构"""
    cleaned_config = {}
    
    logger.info("开始清理 salary 配置...")
    
    for rebate_key, rebate_value in config.items():
        if not isinstance(rebate_value, dict):
            logger.warning(f"忽略无效的 salary 配置项: {rebate_key} = {rebate_value}")
            continue
        
        cleaned_rebate = {}
        for game_key, game_value in rebate_value.items():
            if not isinstance(game_value, list):
                logger.warning(f"忽略无效的游戏配置: {rebate_key}/{game_key} = {game_value}")
                continue
            
            cleaned_rules = []
            for rule in game_value:
                if isinstance(rule, dict) and all(key in rule for key in ["enabled", "profit_min", "profit_max", "salary"]):
                    cleaned_rules.append(rule)
                else:
                    logger.warning(f"忽略无效的规则: {rule}")
            
            if cleaned_rules:
                cleaned_rebate[game_key] = cleaned_rules
        
        if cleaned_rebate:
            cleaned_config[rebate_key] = cleaned_rebate
            logger.info(f"清理 rebate 配置: {rebate_key} -> {len(cleaned_rebate)} 个游戏")
    
    logger.info(f"salary 配置清理完成，共 {len(cleaned_config)} 项")
    return cleaned_config

def fix_json_file(filepath: str, config_type: str) -> bool:
    """修复指定的 JSON 配置文件"""
    try:
        if not os.path.exists(filepath):
            logger.warning(f"文件不存在: {filepath}")
            return False
        
        logger.info(f"开始修复文件: {filepath}")
        
        # 备份原文件
        backup_path = backup_file(filepath)
        
        # 读取原文件
        with open(filepath, 'r', encoding='utf-8') as f:
            try:
                original_config = json.load(f)
            except json.JSONDecodeError as e:
                logger.error(f"JSON 格式错误: {e}")
                return False
        
        # 清理配置
        if config_type == "rebate":
            cleaned_config = clean_rebate_config(original_config)
        elif config_type == "salary":
            cleaned_config = clean_salary_config(original_config)
        else:
            logger.error(f"未知的配置类型: {config_type}")
            return False
        
        # 写入修复后的文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(cleaned_config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"文件修复完成: {filepath}")
        
        # 验证修复后的文件
        with open(filepath, 'r', encoding='utf-8') as f:
            try:
                json.load(f)
                logger.info("修复后的文件 JSON 格式验证通过")
            except json.JSONDecodeError as e:
                logger.error(f"修复后的文件仍有 JSON 格式错误: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"修复文件失败 {filepath}: {e}")
        return False

def main():
    """主函数"""
    print("🔧 配置文件修复工具")
    print("=" * 50)
    
    # 要修复的文件列表
    files_to_fix = [
        ("rebate_config.json", "rebate"),
        ("salary_config.json", "salary")
    ]
    
    success_count = 0
    total_count = len(files_to_fix)
    
    for filepath, config_type in files_to_fix:
        print(f"\n📁 处理文件: {filepath}")
        print("-" * 30)
        
        if fix_json_file(filepath, config_type):
            print(f"✅ {filepath} 修复成功")
            success_count += 1
        else:
            print(f"❌ {filepath} 修复失败")
    
    print(f"\n📊 修复结果统计:")
    print(f"总文件数: {total_count}")
    print(f"成功修复: {success_count}")
    print(f"修复失败: {total_count - success_count}")
    
    if success_count == total_count:
        print("\n🎉 所有配置文件修复完成！")
    else:
        print(f"\n⚠️ 有 {total_count - success_count} 个文件修复失败，请检查日志")
    
    print("\n💡 提示:")
    print("- 原文件已自动备份（.backup_时间戳）")
    print("- 如果修复后有问题，可以从备份文件恢复")
    print("- 建议重新从 Google Sheets 加载配置以确保数据完整性")

if __name__ == "__main__":
    main()
