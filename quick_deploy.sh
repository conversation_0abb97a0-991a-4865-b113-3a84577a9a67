#!/bin/bash

# 🚀 Telegram Bot 统一部署脚本
# 支持 Docker 部署和传统部署两种方式

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 全局变量
DEPLOYMENT_METHOD=""
PYTHON_CMD=""
VENV_PATH="venv"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_highlight() {
    echo -e "${CYAN}[HIGHLIGHT]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        return 1
    fi
    return 0
}

# 检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        log_error "文件不存在: $1"
        return 1
    fi
    return 0
}

# 检查 Python 版本
check_python_version() {
    local python_cmd=$1
    local version_output=$($python_cmd --version 2>&1)
    local version=$(echo $version_output | grep -oE '[0-9]+\.[0-9]+' | head -1)
    local major=$(echo $version | cut -d. -f1)
    local minor=$(echo $version | cut -d. -f2)

    if [ "$major" -ge 3 ] && [ "$minor" -ge 9 ]; then
        return 0
    else
        return 1
    fi
}

# 查找合适的 Python 命令
find_python() {
    local python_commands=("python3" "python" "python3.9" "python3.10" "python3.11" "python3.12")

    for cmd in "${python_commands[@]}"; do
        if check_command "$cmd"; then
            if check_python_version "$cmd"; then
                PYTHON_CMD="$cmd"
                log_success "找到合适的 Python: $cmd ($($cmd --version))"
                return 0
            fi
        fi
    done

    log_error "未找到 Python 3.9+ 版本"
    return 1
}

# 选择部署方法
select_deployment_method() {
    echo ""
    echo "🚀 Telegram Bot 统一部署脚本"
    echo "======================================"
    echo ""
    echo "请选择部署方式："
    echo "1) 🐳 Docker 部署 (推荐生产环境)"
    echo "   - 容器化部署，易于管理"
    echo "   - 包含监控和健康检查"
    echo "   - 需要 Docker 和 Docker Compose"
    echo ""
    echo "2) 🐍 传统部署 (Python 虚拟环境)"
    echo "   - 直接在系统上运行"
    echo "   - 更灵活的配置选项"
    echo "   - 需要 Python 3.9+"
    echo ""

    while true; do
        read -p "请输入选择 (1 或 2): " choice
        case $choice in
            1)
                DEPLOYMENT_METHOD="docker"
                log_highlight "选择了 Docker 部署方式"
                break
                ;;
            2)
                DEPLOYMENT_METHOD="traditional"
                log_highlight "选择了传统部署方式"
                break
                ;;
            *)
                log_warning "无效选择，请输入 1 或 2"
                ;;
        esac
    done
}

# Docker 部署前置检查
check_docker_requirements() {
    log_step "检查 Docker 环境..."

    if ! check_command "docker"; then
        log_error "Docker 未安装，请先安装 Docker"
        echo "安装指南: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! check_command "docker-compose"; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        echo "安装指南: https://docs.docker.com/compose/install/"
        exit 1
    fi

    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi

    log_success "Docker 环境检查完成"
}

# 传统部署前置检查
check_traditional_requirements() {
    log_step "检查 Python 环境..."

    if ! find_python; then
        log_error "需要 Python 3.9 或更高版本"
        echo "请安装 Python 3.9+ 后重试"
        exit 1
    fi

    log_success "Python 环境检查完成"
}
    
# 通用文件检查
check_common_files() {
    log_step "检查项目文件..."
    
    # 检查配置文件
    if [ ! -f ".env" ]; then
        if [ -f ".env.template" ]; then
            log_warning ".env 文件不存在，从模板创建..."
            cp .env.template .env
            log_warning "请编辑 .env 文件，填入你的配置信息"
            echo "主要需要配置的项目："
            echo "  - BOT_TOKEN: 你的 Telegram Bot Token"
            echo "  - CHAT_ID: 你的群组 Chat ID"
            echo "  - GOOGLE_SHEET_NAME: 你的 Google Sheets 名称"
            read -p "配置完成后按 Enter 继续..."
        else
            log_error ".env.template 文件不存在"
            exit 1
        fi
    fi
    
    # 检查 Google Sheets 凭据
    if [ ! -f "mysheetapp.json" ]; then
        log_error "Google Sheets 凭据文件 mysheetapp.json 不存在"
        log_error "请将你的 Google Sheets API 凭据文件重命名为 mysheetapp.json 并放到项目根目录"
        exit 1
    fi
    
    # 检查特定部署方式的文件
    if [ "$DEPLOYMENT_METHOD" = "docker" ]; then
        if [ ! -f "docker/docker-compose.yml" ]; then
            log_error "Docker Compose 配置文件不存在: docker/docker-compose.yml"
            exit 1
        fi
        log_success "Docker 配置文件检查完成"
    fi

    log_success "项目文件检查完成"
}

# 创建必要目录
create_directories() {
    log_step "创建数据目录..."
    mkdir -p data logs backups
    chmod 755 data logs backups
    log_success "数据目录创建完成"
}

# 创建虚拟环境
create_virtual_environment() {
    log_step "创建 Python 虚拟环境..."

    if [ -d "$VENV_PATH" ]; then
        log_info "虚拟环境已存在，跳过创建"
        return 0
    fi

    $PYTHON_CMD -m venv "$VENV_PATH"
    log_success "虚拟环境创建完成"
}

# 激活虚拟环境
activate_virtual_environment() {
    if [ -f "$VENV_PATH/bin/activate" ]; then
        source "$VENV_PATH/bin/activate"
        log_success "虚拟环境已激活"
    else
        log_error "虚拟环境激活脚本不存在"
        exit 1
    fi
}

# 安装 Python 依赖
install_python_dependencies() {
    log_step "安装 Python 依赖..."

    # 升级 pip
    pip install --upgrade pip
    log_info "pip 已升级到最新版本"

    # 安装依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        log_success "依赖安装完成"
    else
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
}

# 运行测试
run_tests() {
    log_step "运行测试..."

    if [ -f "run_tests.py" ]; then
        python run_tests.py --fast
        log_success "测试通过"
    else
        log_warning "测试脚本不存在，跳过测试"
    fi
}

# 传统部署流程
deploy_traditional() {
    log_highlight "开始传统部署流程..."

    check_traditional_requirements
    check_common_files
    create_directories
    create_virtual_environment
    activate_virtual_environment
    install_python_dependencies

    # 询问是否运行测试
    echo ""
    read -p "是否运行测试？(y/N): " run_test
    if [[ $run_test =~ ^[Yy]$ ]]; then
        run_tests
    fi

    log_success "传统部署完成！"

    echo ""
    echo "🎉 部署完成！"
    echo "==============="
    echo ""
    echo "📝 启动应用:"
    echo "  source $VENV_PATH/bin/activate"
    echo "  python Group_record.py"
    echo ""
    echo "📝 或者使用增强版本（包含监控）:"
    echo "  source $VENV_PATH/bin/activate"
    echo "  python main_with_monitoring.py"
    echo ""
    echo "📊 监控端点 (如果使用增强版本):"
    echo "  • 健康检查: http://localhost:8080/health"
    echo "  • 应用状态: http://localhost:8080/status"
    echo "  • 指标数据: http://localhost:8080/metrics"

    # 询问是否立即启动
    echo ""
    read -p "是否立即启动应用？(y/N): " start_app
    if [[ $start_app =~ ^[Yy]$ ]]; then
        echo ""
        echo "选择启动方式："
        echo "1) 基础版本 (Group_record.py)"
        echo "2) 增强版本 (main_with_monitoring.py - 包含监控)"
        read -p "请选择 (1 或 2): " app_choice

        case $app_choice in
            1)
                log_info "启动基础版本..."
                python Group_record.py
                ;;
            2)
                log_info "启动增强版本..."
                python main_with_monitoring.py
                ;;
            *)
                log_info "启动基础版本..."
                python Group_record.py
                ;;
        esac
    fi
}
    
# Docker 部署流程
deploy_docker() {
    log_highlight "开始 Docker 部署流程..."

    check_docker_requirements
    check_common_files
    create_directories

    # 询问部署模式
    echo ""
    echo "请选择 Docker 部署模式："
    echo "1) 基础部署 (仅 Telegram Bot)"
    echo "2) 完整部署 (包含监控: Prometheus + Grafana)"
    read -p "请输入选择 (1 或 2): " deploy_mode

    # 进入 Docker 目录
    cd docker

    # 根据选择部署
    case $deploy_mode in
        1)
            log_info "开始基础部署..."
            docker-compose up -d telegram-bot
            ;;
        2)
            log_info "开始完整部署（包含监控）..."
            docker-compose --profile monitoring up -d
            ;;
        *)
            log_warning "无效选择，使用基础部署..."
            docker-compose up -d telegram-bot
            ;;
    esac
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10

    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose ps

    # 检查健康状态
    log_info "检查应用健康状态..."
    for i in {1..30}; do
        if curl -f http://localhost:8080/health &> /dev/null; then
            log_success "应用健康检查通过"
            break
        fi
        if [ $i -eq 30 ]; then
            log_warning "健康检查超时，请手动检查服务状态"
        else
            echo -n "."
            sleep 2
        fi
    done

    # 显示部署结果
    echo ""
    echo "🎉 Docker 部署完成！"
    echo "===================="

    # 显示服务信息
    echo "📊 服务状态:"
    docker-compose ps

    echo ""
    echo "🔗 可用的服务端点:"
    echo "  • 健康检查: http://localhost:8080/health"
    echo "  • 详细健康检查: http://localhost:8080/health/detailed"
    echo "  • 应用状态: http://localhost:8080/status"
    echo "  • 指标数据: http://localhost:8080/metrics"

    if [ "$deploy_mode" = "2" ]; then
        echo "  • Grafana 监控面板: http://localhost:3000 (admin/admin123)"
        echo "  • Prometheus: http://localhost:9090"
    fi

    echo ""
    echo "📝 常用命令:"
    echo "  • 查看日志: docker-compose logs -f telegram-bot"
    echo "  • 重启服务: docker-compose restart telegram-bot"
    echo "  • 停止服务: docker-compose stop"
    echo "  • 进入容器: docker-compose exec telegram-bot bash"

    echo ""
    echo "📚 更多信息请查看: DOCKER_DEPLOYMENT_GUIDE.md"

    # 询问是否查看日志
    echo ""
    read -p "是否查看实时日志？(y/N): " show_logs
    if [[ $show_logs =~ ^[Yy]$ ]]; then
        log_info "显示实时日志 (Ctrl+C 退出)..."
        docker-compose logs -f telegram-bot
    fi
}

# 主函数
main() {
    # 选择部署方法
    select_deployment_method

    # 根据选择执行相应的部署流程
    case $DEPLOYMENT_METHOD in
        "docker")
            deploy_docker
            ;;
        "traditional")
            deploy_traditional
            ;;
        *)
            log_error "未知的部署方法: $DEPLOYMENT_METHOD"
            exit 1
            ;;
    esac
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查上面的错误信息"' ERR

# 运行主函数
main "$@"
