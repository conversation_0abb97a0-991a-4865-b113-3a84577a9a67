# storage.py - 重构后的存储模块
# 此文件现在作为新架构的兼容层，保持向后兼容性

import json
import os
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from dateutil import parser as p
from telegram import Update
from telegram.ext import ContextTypes
import gspread
from oauth2client.service_account import ServiceAccountCredentials

from Parser import get_current_month_range, parse_datetime
from config import Config
from security_utils import InputValidator
from storage_service import get_storage_service, get_storage_service_sync
from data_models import FIELDS_CHINESE as FIELDS, FIELDS_ENGLISH

logger = logging.getLogger(__name__)

# 保持向后兼容的常量
correction_cache = {}
EXCEL_FILE = Config.EXCEL_FILE
REBATE_FILE = Config.REBATE_FILE
SALARY_FILE = Config.SALARY_FILE
GOOGLE_SHEET_NAME = Config.GOOGLE_SHEET_NAME
REBATE_CONFIG_SHEET_NAME = Config.REBATE_CONFIG_SHEET_NAME
SALARY_CONFIG_SHEET_NAME = Config.SALARY_CONFIG_SHEET_NAME
CREDENTIALS_FILE = Config.CREDENTIALS_FILE
ENV = Config.ENV
FIELDS_eng = FIELDS_ENGLISH


# ==================== 兼容性函数 ====================
# 这些函数保持向后兼容，内部使用新的架构

def load_or_create_excel():
    """初始化 Excel 文件（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()
        # 异步初始化需要在事件循环中调用
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(storage_service.initialize())
        finally:
            loop.close()
    except Exception as e:
        logger.error(f"初始化 Excel 失败: {e}")
        raise

def load_excel_data(start_time=None, end_time=None, full_load=False, exclude_zero=True):
    """加载 Excel 数据（兼容性函数）"""
    from exceptions import FileSystemError, SystemError
    from error_strategy import handle_sync_operation_error
    
    try:
        storage_service = get_storage_service_sync()

        # 使用事件循环调用异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            records = loop.run_until_complete(
                storage_service.load_records(start_time, end_time, full_load, exclude_zero)
            )

            if ENV == "dev":
                logger.info(f"加载 Excel 数据完成，共 {len(records)} 条记录")

            return records
        finally:
            loop.close()

    except FileNotFoundError as e:
        error = FileSystemError(f"Excel文件不存在: {e}", operation="read")
        return handle_sync_operation_error(error, "load_excel_data", return_default=[])
    except PermissionError as e:
        error = FileSystemError(f"Excel文件权限不足: {e}", operation="read")
        return handle_sync_operation_error(error, "load_excel_data", return_default=[])
    except Exception as e:
        error = SystemError(f"加载Excel数据失败: {e}", component="storage")
        return handle_sync_operation_error(error, "load_excel_data", return_default=[])

def write_to_excel(data, msg_time_str):
    """写入 Excel 文件（兼容性函数）"""
    from exceptions import FileSystemError, ValidationError, SystemError
    from error_strategy import handle_sync_operation_error
    
    try:
        storage_service = get_storage_service_sync()

        # 使用事件循环调用异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                storage_service.write_record(data, msg_time_str)
            )

            if not result.success:
                if "验证" in result.message or "格式" in result.message:
                    raise ValidationError(f"数据验证失败: {result.message}")
                else:
                    raise SystemError(f"写入操作失败: {result.message}")

            if ENV == "dev":
                logger.info(f"写入 Excel 成功: {msg_time_str}")

        finally:
            loop.close()

    except ValidationError as e:
        # 验证错误不应该静默失败，需要抛出让调用者处理
        raise e
    except PermissionError as e:
        error = FileSystemError(f"Excel文件权限不足: {e}", operation="write")
        handle_sync_operation_error(error, "write_to_excel")
        raise error
    except FileNotFoundError as e:
        error = FileSystemError(f"Excel文件路径不存在: {e}", operation="write")
        handle_sync_operation_error(error, "write_to_excel")
        raise error
    except Exception as e:
        error = SystemError(f"写入Excel失败: {e}", component="storage")
        handle_sync_operation_error(error, "write_to_excel")
        raise error


def sync_to_google_sheets(data, msg_time_str):
    """同步到 Google Sheets（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()

        # 使用事件循环调用异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # 这个函数主要用于同步单条记录，write_record 已经包含了 Google Sheets 同步
            result = loop.run_until_complete(
                storage_service.write_record(data, msg_time_str)
            )

            if not result.success:
                raise Exception(result.message)

            if ENV == "dev":
                logger.info(f"同步到 Google Sheets 成功: {msg_time_str}")

        finally:
            loop.close()

    except Exception as e:
        logger.error(f"同步到 Google Sheets 失败: {e}")
        raise


def convert_sheet_to_rebate_config(sheet_data):
    rebate_config = {}
    for row in sheet_data[1:]:  # 跳过标题
        casino, person, ratio = row[:3]
        ratio = float(ratio)

        if casino == "默认":
            rebate_config["默认比例"] = ratio
            continue

        if casino not in rebate_config:
            rebate_config[casino] = {}

        if person == "*" or person == "":
            rebate_config[casino]["默认比例"] = ratio
        else:
            rebate_config[casino][person] = ratio

    return rebate_config


def load_rebate_from_google_sheet():
    """从Google Sheet中读取rebate_ratio_config表格数据"""
    from exceptions import GoogleSheetsError, ValidationError, ConfigurationError
    from error_strategy import handle_sync_operation_error
    
    try:
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        
        if not os.path.exists(CREDENTIALS_FILE):
            raise ConfigurationError(
                f"Google Sheets 凭证文件不存在: {CREDENTIALS_FILE}",
                config_file=CREDENTIALS_FILE
            )
        
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)

        # 打开Google Sheet
        sheet = client.open(REBATE_CONFIG_SHEET_NAME)

        # 获取第一个工作表（默认工作表）
        try:
            rebate_sheet = sheet.sheet1
        except Exception as e:
            raise GoogleSheetsError(
                f"无法访问表格 '{REBATE_CONFIG_SHEET_NAME}' 的默认工作表: {e}",
                sheet_name=REBATE_CONFIG_SHEET_NAME,
                operation="access_worksheet"
            )

        # 获取所有数据
        sheet_data = rebate_sheet.get_all_values()

        if not sheet_data:
            raise GoogleSheetsError(
                "rebate_ratio_config 表格为空",
                sheet_name=REBATE_CONFIG_SHEET_NAME,
                operation="read_data"
            )

        # 验证表头
        expected_headers = ["场子", "人员", "比例", "备注"]
        actual_headers = sheet_data[0]

        if len(actual_headers) < 3:
            raise ValidationError(
                f"表格格式错误：至少需要3列（场子、人员、比例），实际只有 {len(actual_headers)} 列"
            )

        # 转换数据格式
        rebate_config = {}
        validation_errors = []
        
        for i, row in enumerate(sheet_data[1:], start=2):  # 跳过标题行，从第2行开始
            if len(row) < 3:
                logger.warning(f"第 {i} 行数据不完整，跳过: {row}")
                continue

            casino = row[0].strip()
            person = row[1].strip()
            ratio_str = row[2].strip()

            if not casino or not ratio_str:
                logger.warning(f"第 {i} 行场子或比例为空，跳过: {row}")
                continue

            try:
                ratio = float(ratio_str)
                if not (0 <= ratio <= 1):
                    validation_errors.append(f"第 {i} 行比例 {ratio} 不在0-1范围内")
                    continue
            except ValueError:
                validation_errors.append(f"第 {i} 行比例格式错误: {ratio_str}")
                continue

            # 处理全局默认配置
            if casino == "默认" and (person == "*" or person == ""):
                rebate_config["默认比例"] = ratio
                continue

            # 跳过空的场子名称或默认场子
            if not casino or casino == "默认":
                continue

            # 初始化场子配置
            if casino not in rebate_config:
                rebate_config[casino] = {}

            # 处理人员配置
            if person == "*" or person == "":
                rebate_config[casino]["默认比例"] = ratio
            else:
                rebate_config[casino][person] = ratio

        # 如果有验证错误，记录但不阻止加载
        if validation_errors:
            logger.warning(f"加载 rebate 配置时发现 {len(validation_errors)} 个数据问题: {validation_errors[:5]}")

        logger.info(f"成功加载 rebate 配置，包含 {len(rebate_config)} 个配置项")
        return rebate_config

    except gspread.exceptions.APIError as e:
        error = GoogleSheetsError(
            f"Google API 错误: {e}",
            sheet_name=REBATE_CONFIG_SHEET_NAME,
            operation="api_call"
        )
        handle_sync_operation_error(error, "load_rebate_from_google_sheet")
        raise error
    except gspread.exceptions.SpreadsheetNotFound as e:
        error = GoogleSheetsError(
            f"找不到 Google 表格 '{REBATE_CONFIG_SHEET_NAME}': {e}",
            sheet_name=REBATE_CONFIG_SHEET_NAME,
            operation="open_sheet"
        )
        handle_sync_operation_error(error, "load_rebate_from_google_sheet")
        raise error
    except (ConfigurationError, ValidationError, GoogleSheetsError) as e:
        # 自定义异常直接抛出
        handle_sync_operation_error(e, "load_rebate_from_google_sheet")
        raise e
    except Exception as e:
        error = GoogleSheetsError(
            f"读取 Google Sheet 失败: {e}",
            sheet_name=REBATE_CONFIG_SHEET_NAME,
            operation="read"
        )
        handle_sync_operation_error(error, "load_rebate_from_google_sheet")
        raise error



def load_rebate_config():
    """加载 rebate 配置（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            config = loop.run_until_complete(storage_service.load_rebate_config())
            return config
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"加载 rebate 配置失败: {e}")
        return {}

def save_rebate_config(config: dict):
    """保存 rebate 配置（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success = loop.run_until_complete(storage_service.save_rebate_config(config))
            if not success:
                raise Exception("保存配置失败")
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"保存 rebate 配置失败: {e}")
        raise


def update_rebate_in_google_sheet(venue: str, person: str = None, ratio: float = None):
    """更新Google Sheet中的rebate配置"""
    try:
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)

        # 打开Google Sheet
        sheet = client.open(REBATE_CONFIG_SHEET_NAME)
        rebate_sheet = sheet.sheet1

        # 获取所有数据
        sheet_data = rebate_sheet.get_all_values()

        if not sheet_data:
            raise Exception("rebate_ratio_config 表格为空")

        # 查找要更新的行
        target_venue = venue
        target_person = person if person else "*"
        found_row = None

        for i, row in enumerate(sheet_data[1:], start=2):  # 跳过标题行，从第2行开始
            if len(row) >= 3:
                row_venue = row[0].strip()
                row_person = row[1].strip()

                if row_venue == target_venue and row_person == target_person:
                    found_row = i
                    break

        if found_row:
            # 更新现有行
            rebate_sheet.update_cell(found_row, 3, str(ratio))  # 第3列是比例
            action = "更新"
        else:
            # 添加新行
            new_row = [target_venue, target_person, str(ratio), "通过机器人设置"]
            rebate_sheet.append_row(new_row)
            action = "添加"

        return True, f"{action}成功"

    except gspread.exceptions.APIError as e:
        return False, f"Google API 错误: {e}"
    except gspread.exceptions.SpreadsheetNotFound as e:
        return False, f"找不到 Google 表格 '{REBATE_CONFIG_SHEET_NAME}': {e}"
    except Exception as e:
        return False, f"更新 Google Sheet 失败: {e}"


def load_rebate_ratio(venue: str, person: str) -> float:
    """加载 rebate 比例（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()
        return storage_service.get_rebate_ratio(venue, person)
    except Exception as e:
        logger.error(f"加载 rebate 比例失败: {e}")
        return 0.1


async def set_rebate_ratio(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """设置 rebate 比例（使用新架构）"""
    args = context.args

    # 输入验证
    is_valid, error_msg = InputValidator.validate_command_args('set_rebate', args)
    if not is_valid:
        await update.message.reply_text(f"❗ {error_msg}")
        return

    if len(args) == 2:
        venue = InputValidator.sanitize_input(args[0])
        person = None
        ratio_str = args[1]
    elif len(args) == 3:
        venue = InputValidator.sanitize_input(args[0])
        person = InputValidator.sanitize_input(args[1])
        ratio_str = args[2]
    else:
        await update.message.reply_text("❗ 格式错误：\n"
                                        "• 设置场子默认比例：/set_rebate 场子 比例\n"
                                        "• 设置人员比例：/set_rebate 场子 人员 比例")
        return

    try:
        ratio = float(ratio_str)
        if not (0 <= ratio <= 1):
            raise ValueError
    except ValueError:
        await update.message.reply_text("❗ 输返比例必须是 0～1 之间的小数（如 0.05 表示 5%）")
        return

    # 发送处理中的消息
    processing_msg = "🔄 正在更新配置..."
    await update.message.reply_text(processing_msg)

    # 使用新的存储服务
    try:
        storage_service = await get_storage_service()
        result = await storage_service.update_rebate_ratio(venue, person, ratio)

        if result.success:
            await update.message.reply_text(result.message)
        else:
            error_msg = (
                f"❌ 更新失败：{result.message}\n\n"
                f"🔧 请检查以下问题：\n"
                f"• 网络连接是否正常\n"
                f"• Google Sheet 权限是否正确\n"
                f"• 表格 '{REBATE_CONFIG_SHEET_NAME}' 是否存在\n\n"
                f"💡 请稍后重试：/set_rebate {venue} {person + ' ' if person else ''}{ratio_str}"
            )
            await update.message.reply_text(error_msg)

    except Exception as e:
        error_msg = (
            f"❌ 系统错误：{str(e)}\n\n"
            f"💡 请稍后重试：/set_rebate {venue} {person + ' ' if person else ''}{ratio_str}"
        )
        await update.message.reply_text(error_msg)


# ==================== Salary Configuration Functions ====================

def load_salary_config():
    """加载 salary 配置（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            config = loop.run_until_complete(storage_service.load_salary_config())
            return config
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"加载 salary 配置失败: {e}")
        return {}

def save_salary_config(config: dict):
    """保存 salary 配置（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success = loop.run_until_complete(storage_service.save_salary_config(config))
            if not success:
                raise Exception("保存配置失败")
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"保存 salary 配置失败: {e}")
        raise


def load_salary_from_google_sheet():
    """从Google Sheet中读取salary配置表格数据"""
    try:
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)

        # 打开Google Sheet
        sheet = client.open(SALARY_CONFIG_SHEET_NAME)

        # 获取第一个工作表（默认工作表）
        try:
            salary_sheet = sheet.sheet1
        except Exception:
            raise Exception(f"无法访问表格 '{SALARY_CONFIG_SHEET_NAME}' 的默认工作表")

        # 获取所有数据
        sheet_data = salary_sheet.get_all_values()

        if not sheet_data:
            raise Exception("salary配置表格为空")

        # 验证表头
        expected_headers = ["启用", "Rebate", "游戏", "游戏子类型", "盈亏下限", "盈亏上限", "工资", "备注"]
        actual_headers = sheet_data[0]

        if len(actual_headers) < 7:
            raise Exception(f"表格格式错误：至少需要7列，实际只有 {len(actual_headers)} 列")

        # 转换数据格式
        salary_config = {}
        for i, row in enumerate(sheet_data[1:], start=2):  # 跳过标题行，从第2行开始
            if len(row) < 7:
                print(f"警告：第 {i} 行数据不完整，跳过: {row}")
                continue

            enabled_str = row[0].strip().upper()
            rebate_str = row[1].strip()
            game = row[2].strip()
            game_subtype = row[3].strip()
            profit_min_str = row[4].strip()
            profit_max_str = row[5].strip()
            salary_str = row[6].strip()
            description = row[7].strip() if len(row) > 7 else ""

            # 跳过空行或无效行
            if not enabled_str or not rebate_str or not game or not salary_str:
                continue

            # 解析启用状态
            enabled = enabled_str == "TRUE"
            if not enabled:
                continue  # 跳过未启用的规则

            try:
                rebate = float(rebate_str)
                salary = int(salary_str)

                # 解析盈亏范围
                profit_min = None
                profit_max = None

                if profit_min_str and profit_min_str != "":
                    if profit_min_str == "-999999":
                        profit_min = None  # 表示负无穷
                    else:
                        profit_min = int(profit_min_str)

                if profit_max_str and profit_max_str != "":
                    profit_max = int(profit_max_str)

            except ValueError as e:
                print(f"警告：第 {i} 行数据格式错误，跳过: {row}, 错误: {e}")
                continue

            # 构建配置结构
            rebate_key = str(rebate)
            if rebate_key not in salary_config:
                salary_config[rebate_key] = {}

            if game not in salary_config[rebate_key]:
                salary_config[rebate_key][game] = []

            # 添加规则
            rule = {
                "enabled": enabled,
                "profit_min": profit_min,
                "profit_max": profit_max,
                "salary": salary,
                "description": description or f"{game} {profit_min_str}~{profit_max_str}"
            }

            salary_config[rebate_key][game].append(rule)

        return salary_config

    except gspread.exceptions.APIError as e:
        raise Exception(f"Google API 错误: {e}")
    except gspread.exceptions.SpreadsheetNotFound as e:
        raise Exception(f"找不到 Google 表格 '{SALARY_CONFIG_SHEET_NAME}': {e}")
    except Exception as e:
        raise Exception(f"读取 Google Sheet 失败: {e}")


def update_salary_in_google_sheet(rebate: float, game: str, profit_min: int = None, profit_max: int = None, salary: int = None):
    """更新Google Sheet中的salary配置"""
    try:
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)

        # 打开Google Sheet
        sheet = client.open(SALARY_CONFIG_SHEET_NAME)
        salary_sheet = sheet.sheet1

        # 获取所有数据
        sheet_data = salary_sheet.get_all_values()

        if not sheet_data:
            raise Exception("salary配置表格为空")

        # 查找要更新的行
        found_row = None

        for i, row in enumerate(sheet_data[1:], start=2):  # 跳过标题行，从第2行开始
            if len(row) >= 7:
                row_rebate = row[1].strip()
                row_game = row[2].strip()
                row_profit_min = row[4].strip()
                row_profit_max = row[5].strip()

                try:
                    # 匹配rebate和游戏
                    if float(row_rebate) == rebate and row_game == game:
                        # 进一步匹配盈亏范围
                        row_min = None if row_profit_min == "" or row_profit_min == "-999999" else int(row_profit_min)
                        row_max = None if row_profit_max == "" else int(row_profit_max)

                        if row_min == profit_min and row_max == profit_max:
                            found_row = i
                            break
                except ValueError:
                    continue

        if found_row:
            # 更新现有行
            salary_sheet.update_cell(found_row, 7, str(salary))  # 第7列是工资
            action = "更新"
        else:
            # 添加新行
            profit_min_str = str(profit_min) if profit_min is not None else ""
            profit_max_str = str(profit_max) if profit_max is not None else ""
            description = f"{game} {profit_min_str}~{profit_max_str} 工资{salary}"

            new_row = ["TRUE", str(rebate), game, "", profit_min_str, profit_max_str, str(salary), description]
            salary_sheet.append_row(new_row)
            action = "添加"

        return True, f"{action}成功"

    except gspread.exceptions.APIError as e:
        return False, f"Google API 错误: {e}"
    except gspread.exceptions.SpreadsheetNotFound as e:
        return False, f"找不到 Google 表格 '{SALARY_CONFIG_SHEET_NAME}': {e}"
    except Exception as e:
        return False, f"更新 Google Sheet 失败: {e}"


async def set_salary_config(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """设置salary配置的命令处理器"""
    args = context.args

    # 输入验证
    is_valid, error_msg = InputValidator.validate_command_args('set_salary', args)
    if not is_valid:
        await update.message.reply_text(f"❗ {error_msg}")
        return

    if len(args) < 5:
        await update.message.reply_text("❗ 格式错误：\n"
                                        "• 设置工资规则：/set_salary rebate 游戏 盈亏下限 盈亏上限 工资\n"
                                        "• 示例：/set_salary 0.2 BJ 200 999 20\n"
                                        "• 示例：/set_salary 0.1 UTH -600 -10 0")
        return

    try:
        rebate = float(args[0])
        game = InputValidator.sanitize_input(args[1])
        profit_min_str = args[2]
        profit_max_str = args[3]
        salary = int(args[4])

        # 验证游戏名称
        game_valid, game_error = InputValidator.validate_field('游戏', game)
        if not game_valid:
            await update.message.reply_text(f"❗ {game_error}")
            return

        # 解析盈亏范围
        profit_min = None if profit_min_str.lower() == "null" or profit_min_str == "" else int(profit_min_str)
        profit_max = None if profit_max_str.lower() == "null" or profit_max_str == "" else int(profit_max_str)

        if not (0 <= rebate <= 1):
            raise ValueError("rebate必须在0-1之间")

        if salary < 0:
            raise ValueError("工资不能为负数")

    except ValueError as e:
        await update.message.reply_text(f"❗ 参数格式错误：{str(e)}\n"
                                        "• rebate: 0-1之间的小数（如0.2）\n"
                                        "• 游戏: BJ, UTH, 俄罗斯, 百家乐等\n"
                                        "• 盈亏范围: 整数（null表示无限制）\n"
                                        "• 工资: 非负整数")
        return

    # 发送处理中的消息
    processing_msg = "🔄 正在更新 Google Sheet..."
    await update.message.reply_text(processing_msg)

    # 第一步：更新 Google Sheet
    success, message = update_salary_in_google_sheet(rebate, game, profit_min, profit_max, salary)

    if not success:
        # Google Sheet 更新失败，提示用户并退出
        error_msg = (
            f"❌ Google Sheet 更新失败：{message}\n\n"
            f"🔧 请检查以下问题：\n"
            f"• 网络连接是否正常\n"
            f"• Google Sheet 权限是否正确\n"
            f"• 表格 '{SALARY_CONFIG_SHEET_NAME}' 是否存在\n\n"
            f"💡 请稍后重试：/set_salary {' '.join(args)}"
        )
        await update.message.reply_text(error_msg)
        return

    # 第二步：Google Sheet 更新成功，现在更新本地配置
    try:
        # 重新从Google Sheet加载完整配置
        new_config = load_salary_from_google_sheet()
        save_salary_config(new_config)

        # 构建描述信息
        range_desc = ""
        if profit_min is not None and profit_max is not None:
            range_desc = f"{profit_min}~{profit_max}"
        elif profit_min is not None:
            range_desc = f"{profit_min} 以上"
        elif profit_max is not None:
            range_desc = f"{profit_max} 以下"
        else:
            range_desc = "无限制"

        # 发送成功消息
        success_msg = (
            f"✅ 工资规则设置成功！\n\n"
            f"📊 规则详情：\n"
            f"• Rebate比例：{rebate:.1%}\n"
            f"• 游戏类型：{game}\n"
            f"• 盈亏范围：{range_desc}\n"
            f"• 工资金额：{salary}\n\n"
            f"☁️ Google Sheet：已更新\n"
            f"💾 本地配置：已同步"
        )
        await update.message.reply_text(success_msg)

    except Exception as e:
        # 本地配置更新失败，但 Google Sheet 已经更新成功
        warning_msg = (
            f"⚠️ 部分更新成功\n\n"
            f"☁️ Google Sheet：✅ 已更新\n"
            f"💾 本地配置：❌ 更新失败 ({str(e)})\n\n"
            f"💡 建议使用 /load_salary_config 命令重新同步本地配置"
        )
        await update.message.reply_text(warning_msg)


async def search_records(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """搜索记录（使用新架构）"""
    if not context.args:
        await update.message.reply_text("请输入关键词，例如：`/find 姓名 赢 日期`")
        return

    keywords = [arg.strip() for arg in context.args if arg.strip()]
    parsed_dates = []
    refined_keywords = []

    for word in keywords:
        try:
            parsed = p.parse(word, fuzzy=True, dayfirst=True)
            parsed_dates.append(parsed.strftime("%Y-%m-%d"))
        except:
            refined_keywords.append(word.lower())

    try:
        # 使用新的存储服务搜索
        storage_service = await get_storage_service()
        matches = await storage_service.search_records(refined_keywords, parsed_dates)

        if not matches:
            await update.message.reply_text("❗ 未找到匹配的记录。")
            return

        # 格式化搜索结果
        result_lines = [f"🔍 找到 {len(matches)} 条匹配记录：\n"]

        for i, record in enumerate(matches[:10], 1):  # 限制显示前10条
            msg_time = record.get("msg_time", "")
            person = record.get("person", "")
            venue = record.get("venue", "")
            game = record.get("game", "")
            profit = record.get("profit", 0)

            result_lines.append(
                f"{i}. {msg_time} | {person} | {venue} | {game} | 盈亏: {profit}"
            )

        if len(matches) > 10:
            result_lines.append(f"\n... 还有 {len(matches) - 10} 条记录")

        result_text = "\n".join(result_lines)
        await update.message.reply_text(result_text)

    except Exception as e:
        logger.error(f"搜索记录失败: {e}")
        await update.message.reply_text(f"❗ 搜索失败：{str(e)}")


# ==================== 兼容性函数保留 ====================
# 以下函数保持原有接口，但内部使用新架构

def load_rebate_from_google_sheet():
    """从 Google Sheet 加载 rebate 配置（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(storage_service.sync_rebate_from_google())
            if result.success:
                return result.data
            else:
                raise Exception(result.message)
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"从 Google Sheet 加载 rebate 配置失败: {e}")
        raise

def load_salary_from_google_sheet():
    """从 Google Sheet 加载 salary 配置（兼容性函数）"""
    try:
        storage_service = get_storage_service_sync()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(storage_service.sync_salary_from_google())
            if result.success:
                return result.data
            else:
                raise Exception(result.message)
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"从 Google Sheet 加载 salary 配置失败: {e}")
        raise



