#!/usr/bin/env python3
"""
Health check endpoint for monitoring
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any
from aiohttp import web, ClientSession
import aiohttp_cors

from monitoring import get_app_monitor
from storage_service import get_storage_service
from config import Config

logger = logging.getLogger(__name__)

class HealthCheckServer:
    """Health check HTTP server"""
    
    def __init__(self, host: str = "127.0.0.1", port: int = 8888):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.setup_routes()
        self.setup_cors()
    
    def setup_routes(self):
        """Setup HTTP routes"""
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/health/detailed', self.detailed_health_check)
        self.app.router.add_get('/metrics', self.metrics_endpoint)
        self.app.router.add_get('/status', self.status_endpoint)
        self.app.router.add_get('/ping', self.ping_endpoint)
    
    def setup_cors(self):
        """Setup CORS"""
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    async def ping_endpoint(self, request):
        """Simple ping endpoint"""
        return web.json_response({"status": "ok", "timestamp": datetime.now().isoformat()})
    
    async def health_check(self, request):
        """Basic health check endpoint"""
        try:
            monitor = get_app_monitor()
            health_status = monitor.health_checker.get_health_status()
            
            status_code = 200 if health_status['healthy'] else 503
            
            response_data = {
                "status": "healthy" if health_status['healthy'] else "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "checks": len(health_status['checks'])
            }
            
            return web.json_response(response_data, status=status_code)
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return web.json_response(
                {"status": "error", "error": str(e)}, 
                status=500
            )
    
    async def detailed_health_check(self, request):
        """Detailed health check with all components"""
        try:
            monitor = get_app_monitor()
            health_status = monitor.health_checker.get_health_status()
            
            # Add additional checks
            additional_checks = await self._perform_additional_checks()
            health_status['additional_checks'] = additional_checks
            
            # Determine overall health
            overall_healthy = health_status['healthy'] and all(
                check['healthy'] for check in additional_checks.values()
            )
            
            status_code = 200 if overall_healthy else 503
            health_status['overall_healthy'] = overall_healthy
            
            return web.json_response(health_status, status=status_code)
            
        except Exception as e:
            logger.error(f"Detailed health check failed: {e}")
            return web.json_response(
                {"status": "error", "error": str(e)}, 
                status=500
            )
    
    async def _perform_additional_checks(self) -> Dict[str, Dict[str, Any]]:
        """Perform additional health checks"""
        checks = {}
        
        # Storage service check
        try:
            storage_service = await get_storage_service()
            stats = storage_service.get_service_stats()
            checks['storage_service'] = {
                'healthy': stats['initialized'],
                'details': stats
            }
        except Exception as e:
            checks['storage_service'] = {
                'healthy': False,
                'error': str(e)
            }
        
        # Configuration check
        try:
            import os
            config_files = [
                Config.EXCEL_FILE,
                Config.REBATE_FILE,
                Config.SALARY_FILE,
                Config.CREDENTIALS_FILE
            ]
            
            missing_files = [f for f in config_files if not os.path.exists(f)]
            
            checks['configuration'] = {
                'healthy': len(missing_files) == 0,
                'missing_files': missing_files
            }
        except Exception as e:
            checks['configuration'] = {
                'healthy': False,
                'error': str(e)
            }
        
        # Bot connectivity check (if possible)
        try:
            # This would require access to the bot instance
            # For now, just check if the token is configured
            bot_token = getattr(Config, 'BOT_TOKEN', None)
            checks['bot_configuration'] = {
                'healthy': bool(bot_token and len(bot_token) > 10),
                'configured': bool(bot_token)
            }
        except Exception as e:
            checks['bot_configuration'] = {
                'healthy': False,
                'error': str(e)
            }
        
        return checks
    
    async def metrics_endpoint(self, request):
        """Metrics endpoint in Prometheus format"""
        try:
            monitor = get_app_monitor()
            metrics = monitor.metrics.get_all_metrics()
            
            # Convert to Prometheus format
            prometheus_metrics = self._convert_to_prometheus(metrics)
            
            return web.Response(
                text=prometheus_metrics,
                content_type='text/plain; version=0.0.4; charset=utf-8'
            )
            
        except Exception as e:
            logger.error(f"Metrics endpoint failed: {e}")
            return web.json_response(
                {"error": str(e)}, 
                status=500
            )
    
    def _convert_to_prometheus(self, metrics: Dict[str, Any]) -> str:
        """Convert metrics to Prometheus format"""
        lines = []
        timestamp = int(datetime.now().timestamp() * 1000)
        
        # Counters
        for name, value in metrics.get('counters', {}).items():
            clean_name = name.replace('.', '_').replace('[', '_').replace(']', '').replace('=', '_').replace(',', '_')
            lines.append(f"# TYPE {clean_name} counter")
            lines.append(f"{clean_name} {value} {timestamp}")
        
        # Gauges
        for name, value in metrics.get('gauges', {}).items():
            clean_name = name.replace('.', '_').replace('[', '_').replace(']', '').replace('=', '_').replace(',', '_')
            lines.append(f"# TYPE {clean_name} gauge")
            lines.append(f"{clean_name} {value} {timestamp}")
        
        # Histograms (simplified)
        for name, stats in metrics.get('histograms', {}).items():
            if stats:
                clean_name = name.replace('.', '_').replace('[', '_').replace(']', '').replace('=', '_').replace(',', '_')
                lines.append(f"# TYPE {clean_name} histogram")
                lines.append(f"{clean_name}_count {stats.get('count', 0)} {timestamp}")
                lines.append(f"{clean_name}_sum {stats.get('mean', 0) * stats.get('count', 0)} {timestamp}")
        
        return '\n'.join(lines) + '\n'
    
    async def status_endpoint(self, request):
        """Comprehensive status endpoint"""
        try:
            monitor = get_app_monitor()
            status = monitor.get_status()
            
            return web.json_response(status)
            
        except Exception as e:
            logger.error(f"Status endpoint failed: {e}")
            return web.json_response(
                {"error": str(e)}, 
                status=500
            )
    
    async def start_server(self):
        """Start the health check server"""
        try:
            runner = web.AppRunner(self.app)
            await runner.setup()
            
            site = web.TCPSite(runner, self.host, self.port)
            await site.start()
            
            logger.info(f"Health check server started on http://{self.host}:{self.port}")
            logger.info("Available endpoints:")
            logger.info(f"  • Health check: http://{self.host}:{self.port}/health")
            logger.info(f"  • Detailed health: http://{self.host}:{self.port}/health/detailed")
            logger.info(f"  • Metrics: http://{self.host}:{self.port}/metrics")
            logger.info(f"  • Status: http://{self.host}:{self.port}/status")
            logger.info(f"  • Ping: http://{self.host}:{self.port}/ping")
            
            return runner
            
        except Exception as e:
            logger.error(f"Failed to start health check server: {e}")
            raise

# Global server instance
_health_server = None

async def start_health_check_server(host: str = "127.0.0.1", port: int = 8888):
    """Start health check server"""
    global _health_server
    
    if _health_server is None:
        _health_server = HealthCheckServer(host, port)
        await _health_server.start_server()
    
    return _health_server

async def stop_health_check_server():
    """Stop health check server"""
    global _health_server
    
    if _health_server:
        # Server cleanup would go here
        _health_server = None

if __name__ == "__main__":
    # Standalone health check server
    async def main():
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        
        # Start monitoring
        monitor = get_app_monitor()
        await monitor.start_monitoring()
        
        # Start health check server
        server = await start_health_check_server()
        
        try:
            # Keep running
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutting down health check server...")
        finally:
            await monitor.stop_monitoring()
    
    asyncio.run(main())
