#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the three fixes for monthly job issues
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_google_sheets_pool():
    """Test Google Sheets connection pool initialization"""
    try:
        print("Testing Google Sheets connection pool...")
        
        from google_sheets_pool import GoogleSheetsConnectionPool
        
        # Test pool creation with explicit int parameters
        pool = GoogleSheetsConnectionPool(max_connections=5, connection_timeout=300)
        
        print(f"Pool created successfully:")
        print(f"  Max connections: {pool.max_connections} (type: {type(pool.max_connections)})")
        print(f"  Connection timeout: {pool.connection_timeout} (type: {type(pool.connection_timeout)})")
        print(f"  Credentials file: {pool.credentials_file}")
        
        return True
        
    except Exception as e:
        print(f"Google Sheets pool test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_job_error_handling():
    """Test monthly job error handling and success determination"""
    try:
        print("\nTesting monthly job error handling...")
        
        from monthlyjob import manual_complete_monthly_job
        
        # Execute monthly job for August 2025
        result = manual_complete_monthly_job(2025, 8)
        
        print(f"Monthly job execution result:")
        print(f"  Success: {result.get('success', False)}")
        
        errors = result.get('errors', [])
        print(f"  Errors count: {len(errors)}")
        
        if errors:
            print(f"  Error details:")
            for i, error in enumerate(errors[:5], 1):  # Show first 5 errors
                print(f"    {i}. {error}")
        
        # Check if success status matches error presence
        has_errors = len(errors) > 0
        success_status = result.get('success', False)
        
        if has_errors and success_status:
            print(f"  WARNING: Has errors but marked as success - this should be fixed")
            return False
        elif not has_errors and not success_status:
            print(f"  WARNING: No errors but marked as failure - unexpected")
            return False
        else:
            print(f"  SUCCESS: Error handling logic is correct")
            return True
        
    except Exception as e:
        print(f"Monthly job error handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_notification_context_handling():
    """Test notification context handling"""
    try:
        print("\nTesting notification context handling...")
        
        from monthlyjob import CompleteMonthlyJobManager
        from monthlyjob import MonthlyReport
        
        # Create a mock context
        class MockContext:
            def __init__(self):
                self.bot = None  # Simulate None bot
                self.job = None
        
        # Create manager and test notification method
        manager = CompleteMonthlyJobManager()
        
        # Create a mock monthly report
        mock_report = MonthlyReport(
            report_month="2025-08",
            total_income=10000,
            total_expense=5000,
            net_profit=5000,
            profit_members_count=3,
            loss_members_count=1,
            total_work_sessions=10,
            main_expense_category="交通",
            remark=""
        )
        
        mock_salary_result = {
            'checked_records': 5,
            'updated_records': 2
        }
        
        # Test with None bot context
        mock_context = MockContext()
        
        # This should not raise an exception
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(
                manager._send_monthly_notification(
                    mock_context, 
                    "Test report text", 
                    mock_report, 
                    mock_salary_result
                )
            )
            print("  SUCCESS: Notification handling with None bot completed without exception")
            return True
        except Exception as e:
            print(f"  FAILED: Notification handling raised exception: {e}")
            return False
        finally:
            loop.close()
        
    except Exception as e:
        print(f"Notification context handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Testing Three Fixes for Monthly Job Issues")
    print("=" * 60)
    
    # Test 1: Google Sheets connection pool
    success1 = test_google_sheets_pool()
    
    # Test 2: Monthly job error handling
    success2 = test_monthly_job_error_handling()
    
    # Test 3: Notification context handling
    success3 = test_notification_context_handling()
    
    print("\nTest Summary:")
    print(f"Google Sheets pool: {'PASS' if success1 else 'FAIL'}")
    print(f"Error handling logic: {'PASS' if success2 else 'FAIL'}")
    print(f"Notification context: {'PASS' if success3 else 'FAIL'}")
    
    if success1 and success2 and success3:
        print("\nAll fixes are working correctly!")
    else:
        print("\nSome fixes need further attention.")

if __name__ == "__main__":
    main()
