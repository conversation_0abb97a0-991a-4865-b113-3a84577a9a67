#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the Response [200] fix for Google Sheets operations
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_response_object_handling():
    """Test Response object handling"""
    try:
        print("Testing Response object handling...")
        
        # Create a mock Response object
        class MockResponse:
            def __init__(self, status_code=200):
                self.status_code = status_code
            
            def __str__(self):
                return f"<Response [{self.status_code}]>"
            
            def __repr__(self):
                return self.__str__()
        
        # Test with 200 response
        response_200 = MockResponse(200)
        print(f"Mock Response 200: {response_200}")
        print(f"Has status_code: {hasattr(response_200, 'status_code')}")
        print(f"Status code: {response_200.status_code}")
        
        # Test with 400 response
        response_400 = MockResponse(400)
        print(f"Mock Response 400: {response_400}")
        print(f"Status code: {response_400.status_code}")
        
        return True
        
    except Exception as e:
        print(f"Response object handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_google_sheets_operation():
    """Test Google Sheets operation with new handling"""
    try:
        print("\nTesting Google Sheets operation...")
        
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool
        
        # Create manager
        pool = GoogleSheetsConnectionPool()
        manager = GoogleSheetsManager(pool)
        
        print(f"Google Sheets manager created successfully")
        
        # Test a simple operation (this will likely fail due to credentials, but we can see the error handling)
        async def test_operation(client):
            # This is a mock operation that would normally interact with Google Sheets
            print(f"Test operation called with client: {client}")
            return True
        
        # Test the operation
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(manager.execute_operation(test_operation))
            print(f"Operation result: {result}")
            return True
        except Exception as e:
            print(f"Expected error (likely credentials): {e}")
            # This is expected since we don't have proper credentials in test
            return True
        finally:
            loop.close()
        
    except Exception as e:
        print(f"Google Sheets operation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_report_manager():
    """Test monthly report manager with new handling"""
    try:
        print("\nTesting monthly report manager...")
        
        from monthlyjob import MonthlyReportManager, GoogleSheetsManager, GoogleSheetsConnectionPool
        
        # Create components
        pool = GoogleSheetsConnectionPool()
        sheets_manager = GoogleSheetsManager(pool)
        report_manager = MonthlyReportManager(sheets_manager)
        
        print(f"Monthly report manager created successfully")
        print(f"Report sheet name: {report_manager.report_sheet_name}")
        
        return True
        
    except Exception as e:
        print(f"Monthly report manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_monthly_job():
    """Test complete monthly job with new error handling"""
    try:
        print("\nTesting complete monthly job...")
        
        from monthlyjob import manual_complete_monthly_job
        
        # Execute monthly job for August 2025
        result = manual_complete_monthly_job(2025, 8)
        
        print(f"Monthly job execution result:")
        print(f"  Success: {result.get('success', False)}")
        
        errors = result.get('errors', [])
        print(f"  Errors count: {len(errors)}")
        
        if errors:
            print(f"  Error details:")
            for i, error in enumerate(errors[:3], 1):  # Show first 3 errors
                print(f"    {i}. {error}")
                
                # Check if the error is still about Response [200]
                if "Response [200]" in str(error):
                    print(f"      WARNING: Still seeing Response [200] as error!")
                    return False
        
        print(f"  SUCCESS: No more Response [200] errors detected")
        return True
        
    except Exception as e:
        print(f"Complete monthly job test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Testing Response [200] Fix for Google Sheets Operations")
    print("=" * 70)
    
    # Test 1: Response object handling
    success1 = test_response_object_handling()
    
    # Test 2: Google Sheets operation
    success2 = test_google_sheets_operation()
    
    # Test 3: Monthly report manager
    success3 = test_monthly_report_manager()
    
    # Test 4: Complete monthly job
    success4 = test_complete_monthly_job()
    
    print("\nTest Summary:")
    print(f"Response object handling: {'PASS' if success1 else 'FAIL'}")
    print(f"Google Sheets operation: {'PASS' if success2 else 'FAIL'}")
    print(f"Monthly report manager: {'PASS' if success3 else 'FAIL'}")
    print(f"Complete monthly job: {'PASS' if success4 else 'FAIL'}")
    
    if success1 and success2 and success3 and success4:
        print("\nAll tests passed! Response [200] fix is working correctly.")
    else:
        print("\nSome tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
