# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Backups
backups/
*.backup
*.bak

# Test files
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/
*.md
!README.md

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Deployment
deploy/
verification_results.json

# Temporary files
*.tmp
*.temp
.tmp/

# Data files (should be mounted as volumes)
data/
*.xlsx
*.json
!requirements.txt
!package.json

# Environment files (should be mounted or passed as env vars)
.env*
!.env.template

# Development tools
run_tests.py
test_*.py
verify_deployment.py
