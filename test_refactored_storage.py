#!/usr/bin/env python3
"""
测试重构后的存储架构
验证所有模块的功能和集成
"""

import asyncio
import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_google_sheets_pool():
    """测试 Google Sheets 连接池"""
    try:
        print("🔗 测试 Google Sheets 连接池...")
        
        from google_sheets_pool import get_sheets_manager
        
        sheets_manager = await get_sheets_manager()
        stats = sheets_manager.pool.get_stats()
        
        print(f"✅ 连接池状态: {stats}")
        return True
        
    except Exception as e:
        print(f"❌ Google Sheets 连接池测试失败: {e}")
        return False

async def test_async_file_operations():
    """测试异步文件操作"""
    try:
        print("\n📁 测试异步文件操作...")
        
        from async_file_ops import get_file_manager, get_excel_processor
        
        file_manager = get_file_manager()
        excel_processor = get_excel_processor()
        
        # 测试 JSON 操作
        test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
        test_file = "test_async.json"
        
        # 写入测试
        write_success = await file_manager.write_json_async(test_file, test_data, is_sensitive=False)
        print(f"📝 JSON 写入测试: {'✅' if write_success else '❌'}")
        
        # 读取测试
        read_data = await file_manager.read_json_async(test_file)
        read_success = read_data.get("test") == "data"
        print(f"📖 JSON 读取测试: {'✅' if read_success else '❌'}")
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        
        # 测试文件统计
        if os.path.exists("config.py"):
            stats = await file_manager.get_file_stats_async("config.py")
            stats_success = stats is not None and 'size' in stats
            print(f"📊 文件统计测试: {'✅' if stats_success else '❌'}")
        
        return write_success and read_success
        
    except Exception as e:
        print(f"❌ 异步文件操作测试失败: {e}")
        return False

async def test_config_management():
    """测试配置管理"""
    try:
        print("\n⚙️ 测试配置管理...")
        
        from config_manager import get_rebate_manager, get_salary_manager
        
        rebate_manager = get_rebate_manager()
        salary_manager = get_salary_manager()
        
        # 测试 rebate 配置
        rebate_config = await rebate_manager.load_local_config()
        rebate_success = isinstance(rebate_config, dict)
        print(f"📋 Rebate 配置加载: {'✅' if rebate_success else '❌'}")
        
        # 测试 salary 配置
        salary_config = await salary_manager.load_local_config()
        salary_success = isinstance(salary_config, dict)
        print(f"💰 Salary 配置加载: {'✅' if salary_success else '❌'}")
        
        # 测试比例获取
        test_ratio = rebate_manager.get_ratio(rebate_config, "TestVenue", "TestPerson")
        ratio_success = isinstance(test_ratio, float) and 0 <= test_ratio <= 1
        print(f"📊 比例计算测试: {'✅' if ratio_success else '❌'}")
        
        return rebate_success and salary_success and ratio_success
        
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False

async def test_error_handling():
    """测试错误处理"""
    try:
        print("\n🚨 测试错误处理...")
        
        from error_handling import (
            get_error_handler, with_retry, with_error_handling,
            StorageError, NetworkError, NETWORK_RETRY_CONFIG
        )
        
        error_handler = get_error_handler()
        
        # 测试错误记录
        test_error = StorageError("测试错误", details={"test": True})
        error_handler.log_error(test_error, "test_context")
        
        stats = error_handler.get_error_stats()
        stats_success = 'error_counts' in stats and stats['recent_errors_count'] > 0
        print(f"📊 错误统计: {'✅' if stats_success else '❌'}")
        
        # 测试重试装饰器
        @with_retry(NETWORK_RETRY_CONFIG)
        async def test_retry_function():
            return "success"
        
        retry_result = await test_retry_function()
        retry_success = retry_result == "success"
        print(f"🔄 重试机制: {'✅' if retry_success else '❌'}")
        
        return stats_success and retry_success
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

async def test_data_models():
    """测试数据模型"""
    try:
        print("\n📋 测试数据模型...")
        
        from data_models import GameRecord, RebateRule, SalaryRule, DataValidator, OperationResult
        
        # 测试游戏记录
        record_data = {
            'msg_time': datetime.now(),
            'work_date': '2025-01-01',
            'person': '张三',
            'venue': 'TestVenue',
            'game': 'BJ',
            'principal': 1000,
            'profit': 500
        }
        
        record = GameRecord.from_dict(record_data)
        record_dict = record.to_dict()
        record_success = record_dict['person'] == '张三' and record_dict['profit'] == 500
        print(f"🎮 游戏记录模型: {'✅' if record_success else '❌'}")
        
        # 测试数据验证
        validation_result = DataValidator.validate_game_record(record)
        validation_success = validation_result.success
        print(f"✅ 数据验证: {'✅' if validation_success else '❌'}")
        
        # 测试 rebate 规则
        rebate_rule = RebateRule(venue="TestVenue", person="张三", ratio=0.15)
        rebate_dict = rebate_rule.to_dict()
        rebate_success = rebate_dict['ratio'] == 0.15
        print(f"💰 Rebate 规则模型: {'✅' if rebate_success else '❌'}")
        
        # 测试 salary 规则
        salary_rule = SalaryRule(rebate_ratio=0.2, game="BJ", profit_min=100, profit_max=999, salary=20)
        salary_match = salary_rule.matches_profit(500)
        salary_success = salary_match is True
        print(f"💵 Salary 规则模型: {'✅' if salary_success else '❌'}")
        
        return record_success and validation_success and rebate_success and salary_success
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False

async def test_storage_service():
    """测试存储服务"""
    try:
        print("\n🗄️ 测试存储服务...")
        
        from storage_service import get_storage_service
        
        storage_service = await get_storage_service()
        
        # 测试服务初始化
        init_success = storage_service._initialized
        print(f"🚀 服务初始化: {'✅' if init_success else '❌'}")
        
        # 测试配置加载
        rebate_config = await storage_service.load_rebate_config()
        config_success = isinstance(rebate_config, dict)
        print(f"⚙️ 配置加载: {'✅' if config_success else '❌'}")
        
        # 测试比例获取
        test_ratio = storage_service.get_rebate_ratio("TestVenue", "TestPerson")
        ratio_success = isinstance(test_ratio, float)
        print(f"📊 比例获取: {'✅' if ratio_success else '❌'}")
        
        # 测试服务统计
        stats = storage_service.get_service_stats()
        stats_success = 'initialized' in stats and 'error_stats' in stats
        print(f"📈 服务统计: {'✅' if stats_success else '❌'}")
        
        return init_success and config_success and ratio_success and stats_success
        
    except Exception as e:
        print(f"❌ 存储服务测试失败: {e}")
        return False

async def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        print("\n🔄 测试向后兼容性...")
        
        # 测试原有函数接口
        from storage import load_rebate_config, load_salary_config, load_rebate_ratio
        
        # 测试配置加载
        rebate_config = load_rebate_config()
        rebate_success = isinstance(rebate_config, dict)
        print(f"📋 Rebate 配置兼容: {'✅' if rebate_success else '❌'}")
        
        salary_config = load_salary_config()
        salary_success = isinstance(salary_config, dict)
        print(f"💰 Salary 配置兼容: {'✅' if salary_success else '❌'}")
        
        # 测试比例加载
        ratio = load_rebate_ratio("TestVenue", "TestPerson")
        ratio_success = isinstance(ratio, float)
        print(f"📊 比例加载兼容: {'✅' if ratio_success else '❌'}")
        
        return rebate_success and salary_success and ratio_success
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False

async def test_performance():
    """测试性能"""
    try:
        print("\n⚡ 测试性能...")
        
        from storage_service import get_storage_service
        import time
        
        storage_service = await get_storage_service()
        
        # 测试配置加载性能
        start_time = time.time()
        for _ in range(10):
            await storage_service.load_rebate_config()
        config_time = time.time() - start_time
        
        config_success = config_time < 5.0  # 10次加载应该在5秒内完成
        print(f"⚡ 配置加载性能: {'✅' if config_success else '❌'} ({config_time:.2f}s)")
        
        # 测试比例获取性能
        start_time = time.time()
        for _ in range(100):
            storage_service.get_rebate_ratio("TestVenue", "TestPerson")
        ratio_time = time.time() - start_time
        
        ratio_success = ratio_time < 2.0  # 100次获取应该在2秒内完成
        print(f"📊 比例获取性能: {'✅' if ratio_success else '❌'} ({ratio_time:.2f}s)")
        
        return config_success and ratio_success
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试重构后的存储架构...\n")
    
    # 执行所有测试
    tests = [
        ("Google Sheets 连接池", test_google_sheets_pool),
        ("异步文件操作", test_async_file_operations),
        ("配置管理", test_config_management),
        ("错误处理", test_error_handling),
        ("数据模型", test_data_models),
        ("存储服务", test_storage_service),
        ("向后兼容性", test_backward_compatibility),
        ("性能测试", test_performance),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 统计结果
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 测试总结:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"• {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构架构工作正常")
        print("\n📋 架构改进总结:")
        print("✅ Google Sheets 连接池 - 提高并发性能")
        print("✅ 异步文件操作 - 避免阻塞")
        print("✅ 模块化配置管理 - 更好的代码组织")
        print("✅ 综合错误处理 - 提高系统稳定性")
        print("✅ 数据模型验证 - 确保数据质量")
        print("✅ 统一存储服务 - 简化接口")
        print("✅ 向后兼容性 - 平滑迁移")
        sys.exit(0)
    else:
        print(f"💥 {total - passed} 个测试失败，请检查相关模块")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
