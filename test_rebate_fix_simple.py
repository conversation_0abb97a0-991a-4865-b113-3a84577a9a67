#!/usr/bin/env python3
"""
简化的 set_rebate 修复测试
"""

import json
import os
import tempfile
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_security_utils_fix():
    """测试 SecurityUtils.read_secure_file 修复"""
    logger.info("测试 SecurityUtils.read_secure_file 修复...")
    
    try:
        # 导入修复后的 SecurityUtils
        import sys
        sys.path.append('.')
        from security_utils import SecurityUtils
        
        # 创建测试文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_file = f.name
            test_content = '{"LesA": {"默认比例": 0.2}, "默认比例": 0.1}'
            f.write(test_content)
        
        try:
            # 测试读取
            content = SecurityUtils.read_secure_file(test_file)
            
            if content == test_content:
                logger.info("✅ SecurityUtils.read_secure_file 修复成功")
                
                # 测试 JSON 解析
                data = json.loads(content)
                if data.get("LesA", {}).get("默认比例") == 0.2:
                    logger.info("✅ JSON 内容验证成功")
                    return True
                else:
                    logger.error("❌ JSON 内容验证失败")
                    return False
            else:
                logger.error(f"❌ 内容不匹配")
                return False
                
        finally:
            os.remove(test_file)
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


def test_file_creation_and_verification():
    """测试文件创建和验证流程"""
    logger.info("测试文件创建和验证流程...")
    
    try:
        import sys
        sys.path.append('.')
        from security_utils import SecurityUtils
        
        # 创建临时文件路径
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            test_file = f.name
        
        # 删除文件，让 create_secure_file 创建
        os.remove(test_file)
        
        try:
            # 测试数据
            test_data = {
                "LesA": {
                    "默认比例": 0.2
                },
                "默认比例": 0.1
            }
            
            # 创建文件
            content = json.dumps(test_data, ensure_ascii=False, indent=2)
            success = SecurityUtils.create_secure_file(test_file, content, is_sensitive=True)
            
            if success:
                logger.info("✅ 文件创建成功")
                
                # 验证文件内容
                read_content = SecurityUtils.read_secure_file(test_file)
                if read_content:
                    read_data = json.loads(read_content)
                    if read_data == test_data:
                        logger.info("✅ 文件创建和验证流程成功")
                        return True
                    else:
                        logger.error("❌ 数据不匹配")
                        return False
                else:
                    logger.error("❌ 无法读取文件内容")
                    return False
            else:
                logger.error("❌ 文件创建失败")
                return False
                
        finally:
            if os.path.exists(test_file):
                os.remove(test_file)
                
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


def test_rebate_config_structure():
    """测试 rebate 配置结构"""
    logger.info("测试 rebate 配置结构...")
    
    try:
        # 模拟 set_rebate LesA 0.2 的配置更新
        original_config = {
            "默认比例": 0.1,
            "Otium": {
                "默认比例": 0.15
            }
        }
        
        # 添加新的 venue
        venue = "LesA"
        ratio = 0.2
        
        if venue not in original_config or not isinstance(original_config[venue], dict):
            original_config[venue] = {}
        
        original_config[venue]["默认比例"] = ratio
        
        # 验证结构
        expected_config = {
            "默认比例": 0.1,
            "Otium": {
                "默认比例": 0.15
            },
            "LesA": {
                "默认比例": 0.2
            }
        }
        
        if original_config == expected_config:
            logger.info("✅ Rebate 配置结构更新正确")
            return True
        else:
            logger.error(f"❌ 配置结构不匹配: {original_config}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    logger.info("开始运行 set_rebate 修复验证测试...\n")
    
    tests = [
        ("SecurityUtils.read_secure_file 修复", test_security_utils_fix),
        ("文件创建和验证流程", test_file_creation_and_verification),
        ("Rebate 配置结构", test_rebate_config_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info('='*50)
        
        try:
            result = test_func()
            results.append(result)
            status = "通过" if result else "失败"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            logger.error(f"{test_name} 异常: {e}")
            results.append(False)
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试结果: {sum(results)}/{len(results)} 通过")
    logger.info('='*50)
    
    if all(results):
        logger.info("\n🎉 所有核心修复测试通过!")
        logger.info("\n修复总结:")
        logger.info("1. ✅ 添加了缺失的 SecurityUtils.read_secure_file 方法")
        logger.info("2. ✅ 改进了文件验证错误处理")
        logger.info("3. ✅ 增强了日志记录和错误信息")
        logger.info("4. ✅ 验证了 rebate 配置结构更新逻辑")
        logger.info("\n/set_rebate 命令现在应该能够正常工作!")
        return True
    else:
        logger.error("\n❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
