# 🐳 Docker 部署说明文档

## Telegram Bot Group Message Grabber - Docker 部署指南

---

## 📋 目录

1. [快速开始](#快速开始)
2. [环境准备](#环境准备)
3. [配置文件设置](#配置文件设置)
4. [Docker 部署方式](#docker-部署方式)
5. [监控和日志](#监控和日志)
6. [故障排除](#故障排除)
7. [维护和更新](#维护和更新)

---

## 🚀 快速开始

### 一键部署命令

```bash
# 1. 克隆项目
git clone https://github.com/wunifeng/tele_bot_grab_group_message.git
cd tele_bot_grab_group_message

# 2. 配置环境变量
cp .env.template .env
# 编辑 .env 文件，填入你的配置

# 3. 添加 Google Sheets 凭据
# 将你的 mysheetapp.json 文件放到项目根目录

# 4. 一键部署
python deploy.py --env production

# 5. 启动 Docker 服务
cd docker
docker-compose up -d
```

**🎉 部署完成！** 你的 Telegram Bot 现在已经在 Docker 容器中运行。

---

## 🛠️ 环境准备

### 系统要求

- **操作系统**: Linux, macOS, Windows
- **Docker**: 20.10+ 
- **Docker Compose**: 2.0+
- **Python**: 3.9+ (用于部署脚本)
- **内存**: 最少 512MB，推荐 1GB+
- **磁盘**: 最少 2GB 可用空间

### 安装 Docker

#### Ubuntu/Debian:
```bash
# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 添加用户到 docker 组
sudo usermod -aG docker $USER
newgrp docker
```

#### CentOS/RHEL:
```bash
# 安装 Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker
sudo systemctl start docker
sudo systemctl enable docker
```

#### Windows:
1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 启动 Docker Desktop
3. 确保启用 WSL 2 后端

#### macOS:
1. 下载并安装 [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)
2. 启动 Docker Desktop

### 验证安装

```bash
docker --version
docker-compose --version
```

---

## ⚙️ 配置文件设置

### 1. 环境变量配置

```bash
# 复制模板文件
cp .env.template .env
```

编辑 `.env` 文件：

```bash
# Telegram Bot 配置
BOT_TOKEN=**********:ABCdefGHIjklMNOpqrsTUVwxyz  # 你的 Bot Token
CHAT_ID=-100**********                            # 你的群组 Chat ID

# 环境设置
ENV=production
DEBUG=false

# Google Sheets 配置
GOOGLE_SHEET_NAME=你的表格名称
REBATE_CONFIG_SHEET_NAME=rebate_ratio_config
SALARY_CONFIG_SHEET_NAME=salary配置
CREDENTIALS_FILE=mysheetapp.json

# 日志配置
LOG_LEVEL=INFO
CONSOLE_LOG_LEVEL=INFO
FILE_LOG_LEVEL=DEBUG

# 监控配置
ENABLE_MONITORING=true
METRICS_PORT=8080
HEALTH_CHECK_PORT=8081

# 时区设置
TIME_ZONE=Asia/Shanghai
```

### 2. Google Sheets 凭据

将你的 Google Sheets API 凭据文件重命名为 `mysheetapp.json` 并放到项目根目录：

```bash
# 确保凭据文件存在
ls -la mysheetapp.json
```

### 3. 创建必要目录

```bash
# 创建数据目录
mkdir -p data logs backups

# 设置权限
chmod 755 data logs backups
```

---

## 🐳 Docker 部署方式

### 方式一：基础部署（推荐）

```bash
# 进入 Docker 目录
cd docker

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps
```

### 方式二：带监控的完整部署

```bash
# 启动完整监控栈
docker-compose --profile monitoring up -d

# 服务包括：
# - telegram-bot: 主应用
# - prometheus: 指标收集
# - grafana: 监控面板
```

### 方式三：自定义部署

创建自定义的 `docker-compose.override.yml`：

```yaml
version: '3.8'

services:
  telegram-bot:
    environment:
      - LOG_LEVEL=DEBUG  # 自定义日志级别
    ports:
      - "8080:8080"      # 暴露健康检查端口
    volumes:
      - ./custom-config:/app/config:ro  # 挂载自定义配置
```

然后部署：

```bash
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d
```

### Docker 命令详解

#### 启动服务
```bash
# 后台启动
docker-compose up -d

# 前台启动（查看日志）
docker-compose up

# 启动特定服务
docker-compose up -d telegram-bot

# 启动监控服务
docker-compose --profile monitoring up -d
```

#### 查看状态
```bash
# 查看所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs telegram-bot

# 实时查看日志
docker-compose logs -f telegram-bot

# 查看最近 100 行日志
docker-compose logs --tail=100 telegram-bot
```

#### 管理服务
```bash
# 停止服务
docker-compose stop

# 重启服务
docker-compose restart telegram-bot

# 停止并删除容器
docker-compose down

# 停止并删除容器、网络、卷
docker-compose down -v
```

#### 更新服务
```bash
# 重新构建镜像
docker-compose build

# 重新构建并启动
docker-compose up -d --build

# 拉取最新镜像
docker-compose pull
```

---

## 📊 监控和日志

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:8080/health

# 详细健康检查
curl http://localhost:8080/health/detailed

# 获取指标
curl http://localhost:8080/metrics

# 简单连通性测试
curl http://localhost:8080/ping
```

### 监控面板

如果启用了监控服务：

1. **Grafana 面板**: http://localhost:3000
   - 用户名: `admin`
   - 密码: `admin123`

2. **Prometheus**: http://localhost:9090

### 日志管理

```bash
# 查看应用日志
docker-compose logs telegram-bot

# 查看系统日志
docker-compose logs prometheus grafana

# 导出日志到文件
docker-compose logs telegram-bot > telegram-bot.log

# 查看容器内的日志文件
docker-compose exec telegram-bot ls -la /app/logs/
```

### 数据持久化

数据卷映射：
- `./data:/app/data` - 应用数据（Excel 文件、配置等）
- `./logs:/app/logs` - 日志文件
- `./backups:/app/backups` - 备份文件

---

## 🔧 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看详细错误信息
docker-compose logs telegram-bot

# 检查配置文件
docker-compose config

# 验证环境变量
docker-compose exec telegram-bot env | grep BOT_TOKEN
```

#### 2. 无法连接到 Telegram

```bash
# 检查网络连接
docker-compose exec telegram-bot ping api.telegram.org

# 验证 Bot Token
docker-compose exec telegram-bot python -c "
import os
print('Bot Token:', os.getenv('BOT_TOKEN', 'NOT_SET'))
"
```

#### 3. Google Sheets 连接失败

```bash
# 检查凭据文件
docker-compose exec telegram-bot ls -la /app/mysheetapp.json

# 验证凭据文件格式
docker-compose exec telegram-bot python -c "
import json
with open('/app/mysheetapp.json') as f:
    data = json.load(f)
    print('Service account email:', data.get('client_email'))
"
```

#### 4. 权限问题

```bash
# 检查文件权限
ls -la data/ logs/ backups/

# 修复权限
sudo chown -R $USER:$USER data logs backups
chmod -R 755 data logs backups
```

#### 5. 端口冲突

```bash
# 检查端口占用
netstat -tlnp | grep :8080

# 修改端口映射
# 编辑 docker-compose.yml 中的 ports 配置
```

### 调试模式

启用调试模式：

```bash
# 修改 .env 文件
DEBUG=true
LOG_LEVEL=DEBUG

# 重启服务
docker-compose restart telegram-bot

# 查看详细日志
docker-compose logs -f telegram-bot
```

### 进入容器调试

```bash
# 进入运行中的容器
docker-compose exec telegram-bot bash

# 在容器内执行 Python 命令
docker-compose exec telegram-bot python -c "
from config import Config
print('Excel file:', Config.EXCEL_FILE)
"

# 查看进程状态
docker-compose exec telegram-bot ps aux
```

---

## 🔄 维护和更新

### 定期维护

#### 1. 日志清理

```bash
# 清理 Docker 日志
docker system prune -f

# 清理应用日志（保留最近 30 天）
find logs/ -name "*.log" -mtime +30 -delete

# 清理备份文件（保留最近 7 天）
find backups/ -name "backup_*" -mtime +7 -delete
```

#### 2. 数据备份

```bash
# 创建完整备份
tar -czf telegram-bot-backup-$(date +%Y%m%d).tar.gz data/ logs/ .env mysheetapp.json

# 备份到远程服务器
rsync -avz data/ user@backup-server:/backups/telegram-bot/
```

#### 3. 健康检查

```bash
# 自动健康检查脚本
#!/bin/bash
HEALTH_URL="http://localhost:8080/health"
if curl -f $HEALTH_URL > /dev/null 2>&1; then
    echo "✅ Service is healthy"
else
    echo "❌ Service is unhealthy, restarting..."
    docker-compose restart telegram-bot
fi
```

### 应用更新

#### 1. 代码更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build

# 滚动更新（零停机）
docker-compose up -d --no-deps telegram-bot
```

#### 2. 配置更新

```bash
# 更新环境变量
vim .env

# 重启服务应用新配置
docker-compose restart telegram-bot
```

#### 3. 依赖更新

```bash
# 更新 requirements.txt
vim requirements.txt

# 重新构建镜像
docker-compose build --no-cache telegram-bot

# 部署更新
docker-compose up -d telegram-bot
```

### 版本回滚

```bash
# 查看镜像历史
docker images telegram-bot

# 回滚到指定版本
docker tag telegram-bot:old telegram-bot:latest
docker-compose up -d telegram-bot

# 或者从备份恢复
tar -xzf telegram-bot-backup-20250101.tar.gz
docker-compose restart telegram-bot
```

---

## 📚 高级配置

### 自定义 Dockerfile

如果需要自定义镜像，可以修改 `docker/Dockerfile`：

```dockerfile
# 添加额外的系统包
RUN apt-get update && apt-get install -y \
    your-additional-package \
    && rm -rf /var/lib/apt/lists/*

# 添加自定义配置
COPY custom-config/ /app/config/

# 设置自定义环境变量
ENV CUSTOM_VAR=value
```

### 多环境部署

创建不同环境的配置文件：

```bash
# 开发环境
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 测试环境
docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d

# 生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 集群部署

使用 Docker Swarm 进行集群部署：

```bash
# 初始化 Swarm
docker swarm init

# 部署服务栈
docker stack deploy -c docker-compose.yml telegram-bot-stack

# 扩展服务
docker service scale telegram-bot-stack_telegram-bot=3
```

---

## 🎯 最佳实践

### 1. 安全配置

```bash
# 使用非 root 用户运行
# 已在 Dockerfile 中配置

# 限制容器资源
# 在 docker-compose.yml 中添加：
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '0.5'
```

### 2. 性能优化

```bash
# 使用多阶段构建减小镜像大小
# 启用 Docker BuildKit
export DOCKER_BUILDKIT=1

# 使用 .dockerignore 排除不必要文件
echo "*.pyc" >> .dockerignore
echo "__pycache__" >> .dockerignore
echo ".git" >> .dockerignore
```

### 3. 监控告警

```bash
# 设置健康检查告警
# 在 docker-compose.yml 中配置：
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

---

## 📞 支持和帮助

### 获取帮助

```bash
# 查看部署脚本帮助
python deploy.py --help

# 查看 Docker Compose 帮助
docker-compose --help

# 运行部署验证
python verify_deployment.py
```

### 常用命令速查

```bash
# 快速重启
docker-compose restart telegram-bot

# 查看实时日志
docker-compose logs -f telegram-bot

# 检查健康状态
curl http://localhost:8080/health

# 进入容器调试
docker-compose exec telegram-bot bash

# 完全重新部署
docker-compose down && docker-compose up -d --build
```

---

**🎉 恭喜！你已经成功掌握了 Telegram Bot 的 Docker 部署。**

如有问题，请查看日志文件或联系技术支持。
