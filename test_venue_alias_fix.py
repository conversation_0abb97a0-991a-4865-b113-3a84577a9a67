#!/usr/bin/env python3
"""
测试场子别名转换修复
验证月度工资检查模块中的场子别名转换是否正常工作
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_aliases():
    """加载别名数据"""
    try:
        with open('aliases.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('venues', {}), data.get('games', {})
    except Exception as e:
        print(f"加载别名文件失败: {e}")
        return {}, {}

def load_rebate_config():
    """加载返佣配置"""
    try:
        with open('rebate_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载返佣配置失败: {e}")
        return {}

def resolve_with_aliases_simple(value, alias_dict, fallback=None):
    """简化版别名解析函数"""
    if not value:
        return fallback
    
    value_clean = value.strip().lower()
    if not value_clean:
        return fallback
    
    # 构建映射表
    mapping = {std.lower(): std for std in alias_dict}
    
    for std, aliases in alias_dict.items():
        for alias in aliases:
            clean_alias = alias.strip().lower()
            if clean_alias:
                mapping[clean_alias] = std
    
    if value_clean in mapping:
        return mapping[value_clean]
    
    return value_clean

def get_rebate_ratio_simple(rebate_config, venue, person):
    """简化版获取返佣比例"""
    # 先查找场子特定配置
    if venue in rebate_config:
        venue_config = rebate_config[venue]
        if isinstance(venue_config, dict):
            # 查找人员特定比例
            if person in venue_config:
                return venue_config[person]
            # 查找场子默认比例
            if "默认比例" in venue_config:
                return venue_config["默认比例"]
        else:
            # 直接是比例值
            return venue_config
    
    # 使用全局默认比例
    return rebate_config.get("默认比例", 0.1)

def test_venue_alias_resolution():
    """测试场子别名解析"""
    print("🏢 测试场子别名解析")
    print("=" * 50)
    
    venue_aliases, _ = load_aliases()
    
    # 测试用例：各种场子别名
    test_cases = [
        ("gb", "GB"),
        ("GB", "GB"),
        ("otium", "Otium"),
        ("Otium", "Otium"),
        ("ot", "Otium"),
        ("iveria", "Iveria"),
        ("merit1", "Merit北塞"),
        ("merit 北塞", "Merit北塞"),
        ("unknown_venue", "unknown_venue")  # 未知场子应该返回原值
    ]
    
    for input_venue, expected_output in test_cases:
        resolved_venue = resolve_with_aliases_simple(input_venue, venue_aliases, input_venue)
        status = "✅" if resolved_venue == expected_output else "❌"
        print(f"{status} {input_venue:20} -> {resolved_venue:20} (期望: {expected_output})")
    
    print()

def test_rebate_ratio_with_venue_alias():
    """测试场子别名转换对返佣比例获取的影响"""
    print("💰 测试场子别名转换对返佣比例的影响")
    print("=" * 50)
    
    venue_aliases, _ = load_aliases()
    rebate_config = load_rebate_config()
    
    # 测试用例：使用别名的场子名称
    test_cases = [
        ("gb", "吴风"),      # gb -> GB
        ("GB", "吴风"),      # 标准名称
        ("otium", "敏"),     # otium -> Otium
        ("Otium", "敏"),     # 标准名称
        ("iveria", "俊"),    # iveria -> Iveria
        ("Iveria", "俊"),    # 标准名称
        ("merit1", "吴风"),  # merit1 -> Merit北塞
        ("Merit北塞", "吴风"), # 标准名称
    ]
    
    for venue_input, person in test_cases:
        # 转换前：直接使用原始场子名称
        ratio_before = get_rebate_ratio_simple(rebate_config, venue_input, person)
        
        # 转换后：使用别名转换后的场子名称
        resolved_venue = resolve_with_aliases_simple(venue_input, venue_aliases, venue_input)
        ratio_after = get_rebate_ratio_simple(rebate_config, resolved_venue, person)
        
        print(f"场子: '{venue_input}' -> '{resolved_venue}', 人员: {person}")
        print(f"  转换前返佣比例: {ratio_before}")
        print(f"  转换后返佣比例: {ratio_after}")
        
        if ratio_before != ratio_after:
            print(f"  ⚠️ 别名转换影响了返佣比例获取")
        else:
            print(f"  ✅ 返佣比例一致")
        print()

def test_process_excel_row_simulation():
    """模拟process_excel_row函数中的场子和游戏类型处理"""
    print("📊 模拟Excel行处理中的场子和游戏类型转换")
    print("=" * 50)
    
    venue_aliases, game_aliases = load_aliases()
    rebate_config = load_rebate_config()
    
    # 模拟从Excel读取的数据（使用别名）
    excel_data = [
        {"person": "吴风", "venue": "merit1", "game": "uth", "profit": -1600},
        {"person": "俊", "venue": "iveria", "game": "bj", "profit": 500},
        {"person": "敏", "venue": "otium", "game": "俄", "profit": 200},
        {"person": "吴风", "venue": "gb", "game": "21点", "profit": 1000},
    ]
    
    for data in excel_data:
        original_venue = data["venue"]
        original_game = data["game"]
        
        # 应用别名转换（这是我们添加的修复）
        resolved_venue = resolve_with_aliases_simple(original_venue, venue_aliases, original_venue)
        resolved_game = resolve_with_aliases_simple(original_game, game_aliases, original_game)
        
        # 获取返佣比例（使用转换后的场子名称）
        rebate_ratio = get_rebate_ratio_simple(rebate_config, resolved_venue, data["person"])
        
        print(f"人员: {data['person']}")
        print(f"  原始场子: '{original_venue}' -> 转换后: '{resolved_venue}'")
        print(f"  原始游戏: '{original_game}' -> 转换后: '{resolved_game}'")
        print(f"  返佣比例: {rebate_ratio}")
        print(f"  盈利: {data['profit']}")
        print()

def main():
    """主测试函数"""
    print("🔧 场子别名转换修复测试")
    print("=" * 60)
    print()
    
    try:
        # 测试场子别名解析
        test_venue_alias_resolution()
        
        # 测试返佣比例获取
        test_rebate_ratio_with_venue_alias()
        
        # 模拟Excel行处理
        test_process_excel_row_simulation()
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
