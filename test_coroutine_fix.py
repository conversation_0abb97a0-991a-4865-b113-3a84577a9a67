#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the coroutine fix for Google Sheets operations
"""

import sys
import os
import asyncio
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_function_types():
    """Test function types to ensure they are correct"""
    try:
        print("Testing function types...")
        
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool
        
        # Create manager
        pool = GoogleSheetsConnectionPool()
        manager = GoogleSheetsManager(pool)
        
        # Test internal operation functions
        print("Checking internal operation functions:")
        
        # Create a mock operation function
        def mock_sync_operation(client, *args, **kwargs):
            return "sync_result"
        
        async def mock_async_operation(client, *args, **kwargs):
            return "async_result"
        
        # Check if functions are correctly identified
        print(f"mock_sync_operation is coroutine function: {asyncio.iscoroutinefunction(mock_sync_operation)}")
        print(f"mock_async_operation is coroutine function: {asyncio.iscoroutinefunction(mock_async_operation)}")
        
        return True
        
    except Exception as e:
        print(f"Function types test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_google_sheets_operations():
    """Test Google Sheets operations with new handling"""
    try:
        print("\nTesting Google Sheets operations...")
        
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool
        
        # Create manager
        pool = GoogleSheetsConnectionPool()
        manager = GoogleSheetsManager(pool)
        
        print(f"Google Sheets manager created successfully")
        
        # Test a simple sync operation
        def test_sync_operation(client):
            print(f"Sync operation called with client: {client}")
            return {"status": "success", "data": "test_data"}
        
        # Test a simple async operation
        async def test_async_operation(client):
            print(f"Async operation called with client: {client}")
            await asyncio.sleep(0.1)  # Simulate async work
            return {"status": "success", "data": "async_test_data"}
        
        # Test both operations
        async def run_tests():
            try:
                # This will likely fail due to credentials, but we can see the error handling
                print("Testing sync operation...")
                result1 = await manager.execute_operation(test_sync_operation)
                print(f"Sync operation result: {result1}")
            except Exception as e:
                print(f"Sync operation error (expected): {e}")
            
            try:
                print("Testing async operation...")
                result2 = await manager.execute_operation(test_async_operation)
                print(f"Async operation result: {result2}")
            except Exception as e:
                print(f"Async operation error (expected): {e}")
        
        # Run the tests
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(run_tests())
            return True
        except Exception as e:
            print(f"Expected error (likely credentials): {e}")
            # This is expected since we don't have proper credentials in test
            return True
        finally:
            loop.close()
        
    except Exception as e:
        print(f"Google Sheets operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """Test config loading to see if coroutine errors are fixed"""
    try:
        print("\nTesting config loading...")
        
        from config_cache import config_cache
        
        # Try to load rebate config
        print("Loading rebate config...")
        rebate_config = config_cache.get_rebate_config()
        print(f"Rebate config loaded: {type(rebate_config)}")
        
        # Try to load salary config
        print("Loading salary config...")
        salary_config = config_cache.get_salary_config()
        print(f"Salary config loaded: {type(salary_config)}")
        
        return True
        
    except Exception as e:
        print(f"Config loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_job_execution():
    """Test monthly job execution to see if coroutine errors are fixed"""
    try:
        print("\nTesting monthly job execution...")
        
        from monthlyjob import manual_complete_monthly_job
        
        # Execute monthly job for August 2025
        result = manual_complete_monthly_job(2025, 8)
        
        print(f"Monthly job execution result:")
        print(f"  Success: {result.get('success', False)}")
        
        errors = result.get('errors', [])
        print(f"  Errors count: {len(errors)}")
        
        if errors:
            print(f"  Error details:")
            for i, error in enumerate(errors[:3], 1):  # Show first 3 errors
                print(f"    {i}. {error}")
                
                # Check if the error is still about coroutines
                if "'coroutine' object is not subscriptable" in str(error):
                    print(f"      WARNING: Still seeing coroutine errors!")
                    return False
                if "Attempt to overwrite 'message' in LogRecord" in str(error):
                    print(f"      WARNING: Still seeing LogRecord errors!")
                    return False
        
        print(f"  SUCCESS: No more coroutine or LogRecord errors detected")
        return True
        
    except Exception as e:
        print(f"Monthly job execution test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Testing Coroutine Fix for Google Sheets Operations")
    print("=" * 70)
    
    # Test 1: Function types
    success1 = test_function_types()
    
    # Test 2: Google Sheets operations
    success2 = test_google_sheets_operations()
    
    # Test 3: Config loading
    success3 = test_config_loading()
    
    # Test 4: Monthly job execution
    success4 = test_monthly_job_execution()
    
    print("\nTest Summary:")
    print(f"Function types: {'PASS' if success1 else 'FAIL'}")
    print(f"Google Sheets operations: {'PASS' if success2 else 'FAIL'}")
    print(f"Config loading: {'PASS' if success3 else 'FAIL'}")
    print(f"Monthly job execution: {'PASS' if success4 else 'FAIL'}")
    
    if success1 and success2 and success3 and success4:
        print("\nAll tests passed! Coroutine fix is working correctly.")
    else:
        print("\nSome tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
