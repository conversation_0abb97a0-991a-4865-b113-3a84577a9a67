#!/usr/bin/env python3
"""
Monitoring and metrics module
Provides system monitoring, performance metrics, and health checks
"""

import asyncio
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json
import logging

from config import Config

logger = logging.getLogger(__name__)

@dataclass
class MetricPoint:
    """Single metric data point"""
    timestamp: datetime
    value: float
    tags: Dict[str, str] = field(default_factory=dict)

@dataclass
class HealthCheck:
    """Health check definition"""
    name: str
    check_function: Callable
    interval: int = 60  # seconds
    timeout: int = 30   # seconds
    critical: bool = False
    last_check: Optional[datetime] = None
    last_result: Optional[bool] = None
    last_error: Optional[str] = None

class MetricsCollector:
    """Collects and stores metrics"""
    
    def __init__(self, max_points: int = 1000):
        self.max_points = max_points
        self.metrics = defaultdict(lambda: deque(maxlen=max_points))
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.histograms = defaultdict(list)
        self.lock = threading.Lock()
    
    def record_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """Record a counter metric"""
        with self.lock:
            key = self._make_key(name, tags)
            self.counters[key] += value
            
            # Also store as time series
            point = MetricPoint(datetime.now(), self.counters[key], tags or {})
            self.metrics[name].append(point)
    
    def record_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record a gauge metric"""
        with self.lock:
            key = self._make_key(name, tags)
            self.gauges[key] = value
            
            # Store as time series
            point = MetricPoint(datetime.now(), value, tags or {})
            self.metrics[name].append(point)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record a histogram metric"""
        with self.lock:
            key = self._make_key(name, tags)
            self.histograms[key].append(value)
            
            # Keep only recent values
            if len(self.histograms[key]) > 1000:
                self.histograms[key] = self.histograms[key][-1000:]
            
            # Store as time series
            point = MetricPoint(datetime.now(), value, tags or {})
            self.metrics[name].append(point)
    
    def record_timing(self, name: str, duration: float, tags: Dict[str, str] = None):
        """Record a timing metric (in seconds)"""
        self.record_histogram(f"{name}.duration", duration, tags)
        self.record_counter(f"{name}.count", 1, tags)
    
    def get_counter(self, name: str, tags: Dict[str, str] = None) -> int:
        """Get counter value"""
        key = self._make_key(name, tags)
        return self.counters.get(key, 0)
    
    def get_gauge(self, name: str, tags: Dict[str, str] = None) -> float:
        """Get gauge value"""
        key = self._make_key(name, tags)
        return self.gauges.get(key, 0.0)
    
    def get_histogram_stats(self, name: str, tags: Dict[str, str] = None) -> Dict[str, float]:
        """Get histogram statistics"""
        key = self._make_key(name, tags)
        values = self.histograms.get(key, [])
        
        if not values:
            return {}
        
        sorted_values = sorted(values)
        count = len(sorted_values)
        
        return {
            'count': count,
            'min': min(sorted_values),
            'max': max(sorted_values),
            'mean': sum(sorted_values) / count,
            'p50': sorted_values[int(count * 0.5)],
            'p90': sorted_values[int(count * 0.9)],
            'p95': sorted_values[int(count * 0.95)],
            'p99': sorted_values[int(count * 0.99)]
        }
    
    def get_time_series(self, name: str, since: datetime = None) -> List[MetricPoint]:
        """Get time series data"""
        points = list(self.metrics[name])
        
        if since:
            points = [p for p in points if p.timestamp >= since]
        
        return points
    
    def _make_key(self, name: str, tags: Dict[str, str] = None) -> str:
        """Make a unique key for metric with tags"""
        if not tags:
            return name
        
        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all metrics summary"""
        with self.lock:
            return {
                'counters': dict(self.counters),
                'gauges': dict(self.gauges),
                'histograms': {k: self.get_histogram_stats(k.split('[')[0], {}) 
                              for k in self.histograms.keys()},
                'timestamp': datetime.now().isoformat()
            }

class SystemMonitor:
    """System resource monitor"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.monitoring = False
        self.monitor_task = None
    
    async def start_monitoring(self, interval: int = 30):
        """Start system monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("System monitoring started")
    
    async def stop_monitoring(self):
        """Stop system monitoring"""
        if not self.monitoring:
            return
        
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("System monitoring stopped")
    
    async def _monitor_loop(self, interval: int):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(interval)
    
    async def _collect_system_metrics(self):
        """Collect system metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics.record_gauge("system.cpu.usage_percent", cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.metrics.record_gauge("system.memory.usage_percent", memory.percent)
            self.metrics.record_gauge("system.memory.available_mb", memory.available / 1024 / 1024)
            self.metrics.record_gauge("system.memory.used_mb", memory.used / 1024 / 1024)
            
            # Disk metrics
            try:
                disk = psutil.disk_usage('.')  # Use current directory instead of root
                self.metrics.record_gauge("system.disk.usage_percent",
                                        (disk.used / disk.total) * 100)
                self.metrics.record_gauge("system.disk.free_gb", disk.free / 1024 / 1024 / 1024)
            except Exception:
                pass  # Disk stats might not be available
            
            # Network metrics (if available)
            try:
                network = psutil.net_io_counters()
                self.metrics.record_counter("system.network.bytes_sent", network.bytes_sent)
                self.metrics.record_counter("system.network.bytes_recv", network.bytes_recv)
            except Exception:
                pass  # Network stats might not be available
            
            # Process metrics
            process = psutil.Process()
            self.metrics.record_gauge("process.memory.rss_mb", 
                                    process.memory_info().rss / 1024 / 1024)
            self.metrics.record_gauge("process.cpu.percent", process.cpu_percent())
            self.metrics.record_gauge("process.threads.count", process.num_threads())
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")

class HealthChecker:
    """Health check manager"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics = metrics_collector
        self.health_checks = {}
        self.checking = False
        self.check_task = None
    
    def register_health_check(self, health_check: HealthCheck):
        """Register a health check"""
        self.health_checks[health_check.name] = health_check
        logger.info(f"Registered health check: {health_check.name}")
    
    async def start_health_checks(self):
        """Start health check loop"""
        if self.checking:
            return
        
        self.checking = True
        self.check_task = asyncio.create_task(self._health_check_loop())
        logger.info("Health checks started")
    
    async def stop_health_checks(self):
        """Stop health check loop"""
        if not self.checking:
            return
        
        self.checking = False
        if self.check_task:
            self.check_task.cancel()
            try:
                await self.check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Health checks stopped")
    
    async def _health_check_loop(self):
        """Main health check loop"""
        while self.checking:
            try:
                await self._run_health_checks()
                await asyncio.sleep(10)  # Check every 10 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(10)
    
    async def _run_health_checks(self):
        """Run all health checks"""
        now = datetime.now()
        
        for name, check in self.health_checks.items():
            # Check if it's time to run this check
            if (check.last_check is None or 
                (now - check.last_check).total_seconds() >= check.interval):
                
                await self._run_single_check(check)
    
    async def _run_single_check(self, check: HealthCheck):
        """Run a single health check"""
        start_time = time.time()
        
        try:
            # Run the check with timeout
            if asyncio.iscoroutinefunction(check.check_function):
                result = await asyncio.wait_for(
                    check.check_function(), 
                    timeout=check.timeout
                )
            else:
                result = await asyncio.wait_for(
                    asyncio.get_event_loop().run_in_executor(
                        None, check.check_function
                    ),
                    timeout=check.timeout
                )
            
            check.last_result = bool(result)
            check.last_error = None
            
            # Record metrics
            self.metrics.record_gauge(f"health.{check.name}.status", 1.0 if result else 0.0)
            self.metrics.record_timing(f"health.{check.name}.check", time.time() - start_time)
            
            if not result and check.critical:
                logger.critical(f"Critical health check failed: {check.name}")
            elif not result:
                logger.warning(f"Health check failed: {check.name}")
            
        except asyncio.TimeoutError:
            check.last_result = False
            check.last_error = f"Timeout after {check.timeout}s"
            self.metrics.record_gauge(f"health.{check.name}.status", 0.0)
            logger.error(f"Health check timeout: {check.name}")
            
        except Exception as e:
            check.last_result = False
            check.last_error = str(e)
            self.metrics.record_gauge(f"health.{check.name}.status", 0.0)
            logger.error(f"Health check error {check.name}: {e}")
        
        finally:
            check.last_check = datetime.now()
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status"""
        status = {
            'healthy': True,
            'checks': {},
            'timestamp': datetime.now().isoformat()
        }
        
        for name, check in self.health_checks.items():
            check_status = {
                'healthy': check.last_result,
                'last_check': check.last_check.isoformat() if check.last_check else None,
                'error': check.last_error,
                'critical': check.critical
            }
            
            status['checks'][name] = check_status
            
            # If any critical check fails, overall status is unhealthy
            if check.critical and not check.last_result:
                status['healthy'] = False
        
        return status

class ApplicationMonitor:
    """Main application monitoring class"""
    
    def __init__(self):
        self.metrics = MetricsCollector()
        self.system_monitor = SystemMonitor(self.metrics)
        self.health_checker = HealthChecker(self.metrics)
        self.start_time = datetime.now()
        
        # Register default health checks
        self._register_default_health_checks()
    
    def _register_default_health_checks(self):
        """Register default health checks"""
        
        # Database/Excel file check
        def check_excel_file():
            import os
            return os.path.exists(Config.EXCEL_FILE)
        
        self.health_checker.register_health_check(
            HealthCheck("excel_file", check_excel_file, interval=60, critical=True)
        )
        
        # Configuration files check
        def check_config_files():
            import os
            return (os.path.exists(Config.REBATE_FILE) and 
                   os.path.exists(Config.SALARY_FILE))
        
        self.health_checker.register_health_check(
            HealthCheck("config_files", check_config_files, interval=300)
        )
        
        # Memory usage check
        def check_memory_usage():
            memory = psutil.virtual_memory()
            return memory.percent < 90  # Alert if memory usage > 90%
        
        self.health_checker.register_health_check(
            HealthCheck("memory_usage", check_memory_usage, interval=30, critical=True)
        )
        
        # Disk space check
        def check_disk_space():
            try:
                disk = psutil.disk_usage('.')  # Use current directory
                usage_percent = (disk.used / disk.total) * 100
                return usage_percent < 95  # Alert if disk usage > 95%
            except Exception:
                return True  # If can't check, assume OK
        
        self.health_checker.register_health_check(
            HealthCheck("disk_space", check_disk_space, interval=300, critical=True)
        )
    
    async def start_monitoring(self):
        """Start all monitoring"""
        await self.system_monitor.start_monitoring()
        await self.health_checker.start_health_checks()
        logger.info("Application monitoring started")
    
    async def stop_monitoring(self):
        """Stop all monitoring"""
        await self.system_monitor.stop_monitoring()
        await self.health_checker.stop_health_checks()
        logger.info("Application monitoring stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status"""
        uptime = datetime.now() - self.start_time
        
        return {
            'application': {
                'name': 'Telegram Bot',
                'version': getattr(Config, 'VERSION', '1.0.0'),
                'uptime_seconds': uptime.total_seconds(),
                'start_time': self.start_time.isoformat()
            },
            'health': self.health_checker.get_health_status(),
            'metrics': self.metrics.get_all_metrics()
        }
    
    def export_metrics(self, format: str = 'json') -> str:
        """Export metrics in specified format"""
        if format.lower() == 'json':
            return json.dumps(self.get_status(), indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"Unsupported format: {format}")

# Global monitor instance
_app_monitor = None

def get_app_monitor() -> ApplicationMonitor:
    """Get global application monitor"""
    global _app_monitor
    
    if _app_monitor is None:
        _app_monitor = ApplicationMonitor()
    
    return _app_monitor

# Convenience functions
def record_counter(name: str, value: int = 1, tags: Dict[str, str] = None):
    """Record counter metric"""
    get_app_monitor().metrics.record_counter(name, value, tags)

def record_gauge(name: str, value: float, tags: Dict[str, str] = None):
    """Record gauge metric"""
    get_app_monitor().metrics.record_gauge(name, value, tags)

def record_timing(name: str, duration: float, tags: Dict[str, str] = None):
    """Record timing metric"""
    get_app_monitor().metrics.record_timing(name, duration, tags)

# Timing decorator
def monitor_performance(metric_name: str = None, tags: Dict[str, str] = None):
    """Decorator to monitor function performance"""
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            name = metric_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                record_timing(name, time.time() - start_time, tags)
                record_counter(f"{name}.success", 1, tags)
                return result
            except Exception as e:
                record_timing(name, time.time() - start_time, tags)
                record_counter(f"{name}.error", 1, tags)
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            name = metric_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                record_timing(name, time.time() - start_time, tags)
                record_counter(f"{name}.success", 1, tags)
                return result
            except Exception as e:
                record_timing(name, time.time() - start_time, tags)
                record_counter(f"{name}.error", 1, tags)
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
