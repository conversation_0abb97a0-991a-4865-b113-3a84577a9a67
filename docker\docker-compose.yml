version: '3.8'

services:
  telegram-bot:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: telegram-bot
    restart: unless-stopped
    environment:
      - ENV=production
      - LOG_LEVEL=INFO
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
      - ../backups:/app/backups
      - ../.env:/app/.env:ro
      - ../mysheetapp.json:/app/mysheetapp.json:ro
    networks:
      - telegram-bot-network
    healthcheck:
      test: ["CMD", "python", "-c", "import psutil; exit(0 if any('Group_record.py' in p.cmdline() for p in psutil.process_iter()) else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Monitoring with Prometheus and Grafana
  prometheus:
    image: prom/prometheus:latest
    container_name: telegram-bot-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - telegram-bot-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: telegram-bot-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - telegram-bot-network
    profiles:
      - monitoring

networks:
  telegram-bot-network:
    driver: bridge

volumes:
  prometheus-data:
  grafana-data:
