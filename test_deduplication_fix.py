#!/usr/bin/env python3
"""
Test the deduplication fix for duplicate message writing issue
"""

import asyncio
import logging
import sys
import os
import json
import tempfile
from unittest.mock import patch, MagicMock, AsyncMock


def setup_test_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


async def test_duplicate_detection():
    """Test that duplicate messages are detected and prevented"""
    logger = setup_test_logging()
    logger.info("Testing duplicate message detection...")
    
    # Test data - the exact message that was duplicated
    test_message = """日期：8月25日
人员：加林
场子：COSMOS
游戏：BJ
卡号:  13424
本金：1500
点码：20
工资：0
输反：150
赢亏: -1330
备注："""
    
    parsed_data = {
        "人员": "加林",
        "场子": "COSMOS",
        "游戏": "BJ",
        "本金": 1500,
        "输反": 150,
        "赢亏": -1330
    }
    msg_time = "2025-08-26 07:20:16"
    
    # Create temporary cache file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_cache_file = f.name
    
    try:
        # Import and test message cache with write status tracking
        sys.path.append('.')
        from message_cache import MessageCache
        
        cache = MessageCache()
        cache.cache_file = temp_cache_file
        
        # Test 1: Add message to cache
        logger.info("Test 1: Adding message to cache...")
        cache.add_message(parsed_data, msg_time, test_message, "pending")
        
        # Test 2: Check message status
        logger.info("Test 2: Checking message status...")
        status = cache.is_message_written(parsed_data, msg_time)
        logger.info(f"Initial status: {status}")
        
        assert status["write_status"] == "pending"
        assert status["excel_written"] == False
        assert status["sheets_written"] == False
        
        # Test 3: Update Excel write status
        logger.info("Test 3: Updating Excel write status...")
        cache.update_write_status(parsed_data, msg_time, excel_success=True)
        
        status = cache.is_message_written(parsed_data, msg_time)
        logger.info(f"After Excel write: {status}")
        
        assert status["write_status"] == "partial"
        assert status["excel_written"] == True
        assert status["sheets_written"] == False
        
        # Test 4: Update Sheets write status
        logger.info("Test 4: Updating Sheets write status...")
        cache.update_write_status(parsed_data, msg_time, sheets_success=True)
        
        status = cache.is_message_written(parsed_data, msg_time)
        logger.info(f"After Sheets write: {status}")
        
        assert status["write_status"] == "complete"
        assert status["excel_written"] == True
        assert status["sheets_written"] == True
        
        # Test 5: Check pending messages (should be empty for complete messages)
        logger.info("Test 5: Checking pending messages...")
        pending = cache.get_pending_messages()
        logger.info(f"Pending messages: {len(pending)}")
        
        # Complete messages should not be in pending list
        assert len(pending) == 0
        
        logger.info("✅ Duplicate detection test passed")
        return True
        
    except Exception as e:
        logger.error(f"Duplicate detection test failed: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists(temp_cache_file):
            os.remove(temp_cache_file)


async def test_process_interruption_scenario():
    """Test the exact scenario that caused duplicates"""
    logger = setup_test_logging()
    logger.info("Testing process interruption scenario...")
    
    parsed_data = {
        "人员": "加林",
        "场子": "COSMOS",
        "游戏": "BJ",
        "本金": 1500,
        "输反": 150,
        "赢亏": -1330
    }
    msg_time = "2025-08-26 07:20:16"
    
    # Create temporary cache file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_cache_file = f.name
    
    try:
        sys.path.append('.')
        from message_cache import MessageCache
        
        cache = MessageCache()
        cache.cache_file = temp_cache_file
        
        logger.info("Scenario: Message processing interrupted after successful write")
        
        # Step 1: Message cached (early caching)
        logger.info("Step 1: Message cached immediately after parsing")
        cache.add_message(parsed_data, msg_time, "", "pending")
        
        # Step 2: Simulate successful write (but process interrupted before cleanup)
        logger.info("Step 2: Simulating successful write operations")
        cache.update_write_status(parsed_data, msg_time, True, True)
        
        # Step 3: Process restart - check if message would be retried
        logger.info("Step 3: Process restart - checking retry behavior")
        pending = cache.get_pending_messages()
        
        logger.info(f"Messages that would be retried: {len(pending)}")
        
        # With the fix, complete messages should NOT be retried
        assert len(pending) == 0, f"Expected 0 pending messages, got {len(pending)}"
        
        logger.info("✅ Process interruption scenario test passed")
        return True
        
    except Exception as e:
        logger.error(f"Process interruption scenario test failed: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists(temp_cache_file):
            os.remove(temp_cache_file)


async def test_partial_write_retry():
    """Test retry behavior for partial writes"""
    logger = setup_test_logging()
    logger.info("Testing partial write retry behavior...")
    
    parsed_data = {
        "人员": "测试用户",
        "场子": "测试场子",
        "游戏": "BJ",
        "本金": 1000,
        "输反": 100,
        "赢亏": -500
    }
    msg_time = "2025-08-26 10:00:00"
    
    # Create temporary cache file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_cache_file = f.name
    
    try:
        sys.path.append('.')
        from message_cache import MessageCache
        
        cache = MessageCache()
        cache.cache_file = temp_cache_file
        
        logger.info("Scenario: Excel succeeds, Google Sheets fails")
        
        # Step 1: Message cached
        cache.add_message(parsed_data, msg_time, "", "pending")
        
        # Step 2: Excel succeeds, Sheets fails
        cache.update_write_status(parsed_data, msg_time, excel_success=True, sheets_success=False)
        
        # Step 3: Check if message is in retry queue
        pending = cache.get_pending_messages()
        logger.info(f"Messages pending retry: {len(pending)}")
        
        # Should have 1 message for Sheets retry
        assert len(pending) == 1, f"Expected 1 pending message, got {len(pending)}"
        
        # Step 4: Simulate retry - only Sheets should be attempted
        msg = pending[0]
        assert msg["excel_written"] == True
        assert msg["sheets_written"] == False
        assert msg["write_status"] == "partial"
        
        logger.info("✅ Partial write retry test passed")
        return True
        
    except Exception as e:
        logger.error(f"Partial write retry test failed: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists(temp_cache_file):
            os.remove(temp_cache_file)


async def test_deduplication_integration():
    """Test integration with Group_record.py deduplication logic"""
    logger = setup_test_logging()
    logger.info("Testing deduplication integration...")
    
    # This test simulates the new logic in Group_record.py
    parsed_data = {
        "人员": "集成测试",
        "场子": "测试场子",
        "游戏": "BJ",
        "本金": 2000,
        "输反": 200,
        "赢亏": 800
    }
    msg_time = "2025-08-26 11:00:00"
    
    # Create temporary cache file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_cache_file = f.name
    
    try:
        sys.path.append('.')
        from message_cache import MessageCache
        
        cache = MessageCache()
        cache.cache_file = temp_cache_file
        
        # Simulate the new Group_record.py logic
        logger.info("Simulating Group_record.py deduplication logic...")
        
        # Step 1: Check if message already processed (first time)
        write_status = cache.is_message_written(parsed_data, msg_time)
        logger.info(f"First check - Status: {write_status['write_status']}")
        
        assert write_status["write_status"] == "not_cached"
        
        # Step 2: Add to cache and process
        cache.add_message(parsed_data, msg_time, "", "pending")
        cache.update_write_status(parsed_data, msg_time, True, True)
        
        # Step 3: Simulate duplicate message (should be rejected)
        write_status = cache.is_message_written(parsed_data, msg_time)
        logger.info(f"Second check - Status: {write_status['write_status']}")
        
        assert write_status["write_status"] == "complete"
        
        # This would trigger the duplicate prevention in Group_record.py
        logger.info("Duplicate message would be rejected with 'complete' status")
        
        logger.info("✅ Deduplication integration test passed")
        return True
        
    except Exception as e:
        logger.error(f"Deduplication integration test failed: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists(temp_cache_file):
            os.remove(temp_cache_file)


async def run_all_tests():
    """Run all deduplication fix tests"""
    logger = setup_test_logging()
    logger.info("Running deduplication fix tests...\n")
    
    tests = [
        ("Duplicate Detection", test_duplicate_detection),
        ("Process Interruption Scenario", test_process_interruption_scenario),
        ("Partial Write Retry", test_partial_write_retry),
        ("Deduplication Integration", test_deduplication_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing: {test_name}")
            logger.info('='*60)
            
            result = await test_func()
            results.append(result)
            
            status = "PASSED" if result else "FAILED"
            logger.info(f"\n{test_name}: {status}")
            
        except Exception as e:
            logger.error(f"{test_name} failed with exception: {e}")
            results.append(False)
    
    logger.info(f"\n{'='*60}")
    logger.info(f"DEDUPLICATION FIX RESULTS: {sum(results)}/{len(results)} tests passed")
    logger.info('='*60)
    
    if all(results):
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("\nThe deduplication fix successfully prevents:")
        logger.info("  ✅ Duplicate message processing")
        logger.info("  ✅ Double writes to Excel and Google Sheets")
        logger.info("  ✅ Unnecessary retries of completed messages")
        logger.info("  ✅ Race conditions during process interruptions")
        
        logger.info("\nDeployment ready! This fix will prevent duplicate messages.")
        return True
    else:
        logger.error("\n❌ Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
