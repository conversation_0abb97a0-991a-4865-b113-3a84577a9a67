#!/usr/bin/env python3
"""
异步文件操作模块
提供异步的 Excel 和 JSON 文件操作功能
"""

import asyncio
import json
import logging
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import aiofiles
import openpyxl
from openpyxl import Workbook
from concurrent.futures import ThreadPoolExecutor
import threading

from config import Config
from security_utils import SecurityUtils

logger = logging.getLogger(__name__)

ENV = Config.ENV
TIME_ZONE = Config.TIME_ZONE

class AsyncFileManager:
    """异步文件管理器"""
    
    def __init__(self, max_workers: int = 4):
        """
        初始化异步文件管理器
        
        Args:
            max_workers: 线程池最大工作线程数
        """
        self.max_workers = max_workers
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._file_locks = {}
        self._lock = threading.Lock()
    
    def _get_file_lock(self, filepath: str) -> threading.Lock:
        """获取文件锁"""
        with self._lock:
            if filepath not in self._file_locks:
                self._file_locks[filepath] = threading.Lock()
            return self._file_locks[filepath]
    
    async def read_json_async(self, filepath: str) -> Dict[str, Any]:
        """异步读取 JSON 文件"""
        try:
            if not os.path.exists(filepath):
                logger.warning(f"JSON 文件不存在: {filepath}")
                return {}
            
            async with aiofiles.open(filepath, 'r', encoding='utf-8') as f:
                content = await f.read()
                return json.loads(content) if content.strip() else {}
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON 文件格式错误 {filepath}: {e}")
            return {}
        except Exception as e:
            logger.error(f"读取 JSON 文件失败 {filepath}: {e}")
            return {}
    
    async def write_json_async(self, filepath: str, data: Dict[str, Any], is_sensitive: bool = True) -> bool:
        """异步写入 JSON 文件"""
        try:
            file_lock = self._get_file_lock(filepath)

            def _write_json():
                with file_lock:
                    # Windows 兼容性检查：确保文件可写
                    if os.name == 'nt' and os.path.exists(filepath):
                        try:
                            # 移除可能的只读和隐藏属性
                            os.system(f'attrib -R -H "{filepath}"')
                        except Exception as e:
                            logger.warning(f"Windows 文件属性处理警告 {filepath}: {e}")

                    content = json.dumps(data, ensure_ascii=False, indent=2)
                    success = SecurityUtils.create_secure_file(filepath, content, is_sensitive=is_sensitive)

                    # 验证文件写入成功
                    if success and os.path.exists(filepath):
                        try:
                            # 验证文件内容 - 使用 SecurityUtils 安全打开
                            content = SecurityUtils.read_secure_file(filepath)
                            if content:
                                json.loads(content)  # 验证 JSON 格式
                                logger.debug(f"JSON 文件写入和验证成功: {filepath}")
                            else:
                                logger.error(f"JSON 文件验证失败 - 无法读取内容: {filepath}")
                                # 即使验证失败，如果文件创建成功，也认为写入成功
                                logger.warning(f"文件创建成功但验证失败，继续执行: {filepath}")
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON 文件格式验证失败 {filepath}: {e}")
                            # JSON 格式错误是严重问题，返回失败
                            return False
                        except Exception as e:
                            logger.error(f"JSON 文件验证失败 {filepath}: {e}")
                            # 其他验证错误，如果文件存在就认为成功
                            logger.warning(f"文件验证异常但文件存在，继续执行: {filepath}")
                    elif not success:
                        logger.error(f"文件创建失败: {filepath}")
                        return False
                    elif not os.path.exists(filepath):
                        logger.error(f"文件创建后不存在: {filepath}")
                        return False

                    return success

            # 在线程池中执行文件写入
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(self._executor, _write_json)

            if success:
                logger.debug(f"JSON 文件写入成功: {filepath}")
            else:
                logger.error(f"JSON 文件写入失败: {filepath}")

            return success

        except Exception as e:
            logger.error(f"异步写入 JSON 文件失败 {filepath}: {e}")
            return False
    
    async def read_excel_async(self, filepath: str, start_row: int = 2) -> List[List[Any]]:
        """异步读取 Excel 文件"""
        try:
            if not os.path.exists(filepath):
                logger.warning(f"Excel 文件不存在: {filepath}")
                return []
            
            def _read_excel():
                file_lock = self._get_file_lock(filepath)
                with file_lock:
                    workbook = openpyxl.load_workbook(filepath, read_only=True)
                    sheet = workbook.active
                    rows = list(sheet.iter_rows(min_row=start_row, values_only=True))
                    workbook.close()
                    return rows
            
            # 在线程池中执行文件读取
            loop = asyncio.get_event_loop()
            rows = await loop.run_in_executor(self._executor, _read_excel)
            
            logger.debug(f"Excel 文件读取成功: {filepath}, 行数: {len(rows)}")
            return rows
            
        except Exception as e:
            logger.error(f"异步读取 Excel 文件失败 {filepath}: {e}")
            return []
    
    async def write_excel_row_async(self, filepath: str, row_data: List[Any]) -> bool:
        """异步向 Excel 文件添加行"""
        try:
            def _write_excel_row():
                file_lock = self._get_file_lock(filepath)
                with file_lock:
                    # 确保文件存在
                    if not os.path.exists(filepath):
                        self._create_excel_file(filepath)
                    
                    workbook = openpyxl.load_workbook(filepath)
                    sheet = workbook.active
                    sheet.append(row_data)
                    workbook.save(filepath)
                    workbook.close()
                    return True
            
            # 在线程池中执行文件写入
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(self._executor, _write_excel_row)
            
            if success:
                logger.debug(f"Excel 行写入成功: {filepath}")
            else:
                logger.error(f"Excel 行写入失败: {filepath}")
            
            return success
            
        except Exception as e:
            logger.error(f"异步写入 Excel 行失败 {filepath}: {e}")
            return False
    
    def _create_excel_file(self, filepath: str, headers: Optional[List[str]] = None):
        """创建 Excel 文件"""
        try:
            workbook = Workbook()
            sheet = workbook.active
            
            if headers:
                sheet.append(headers)
            
            workbook.save(filepath)
            workbook.close()
            
            logger.info(f"Excel 文件创建成功: {filepath}")
            
        except Exception as e:
            logger.error(f"创建 Excel 文件失败 {filepath}: {e}")
            raise
    
    async def create_excel_async(self, filepath: str, headers: Optional[List[str]] = None) -> bool:
        """异步创建 Excel 文件"""
        try:
            def _create_excel():
                self._create_excel_file(filepath, headers)
                return True
            
            # 在线程池中执行文件创建
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(self._executor, _create_excel)
            
            return success
            
        except Exception as e:
            logger.error(f"异步创建 Excel 文件失败 {filepath}: {e}")
            return False
    
    async def backup_file_async(self, filepath: str) -> Optional[str]:
        """异步备份文件"""
        try:
            if not os.path.exists(filepath):
                logger.warning(f"备份文件不存在: {filepath}")
                return None
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{filepath}.backup_{timestamp}"
            
            def _backup_file():
                import shutil
                shutil.copy2(filepath, backup_path)
                return backup_path
            
            # 在线程池中执行文件备份
            loop = asyncio.get_event_loop()
            result_path = await loop.run_in_executor(self._executor, _backup_file)
            
            logger.info(f"文件备份成功: {filepath} -> {result_path}")
            return result_path
            
        except Exception as e:
            logger.error(f"异步备份文件失败 {filepath}: {e}")
            return None
    
    async def get_file_stats_async(self, filepath: str) -> Optional[Dict[str, Any]]:
        """异步获取文件统计信息"""
        try:
            if not os.path.exists(filepath):
                return None
            
            def _get_stats():
                stat = os.stat(filepath)
                return {
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'exists': True
                }
            
            # 在线程池中执行文件统计
            loop = asyncio.get_event_loop()
            stats = await loop.run_in_executor(self._executor, _get_stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"获取文件统计信息失败 {filepath}: {e}")
            return None
    
    async def cleanup_old_backups_async(self, filepath: str, keep_count: int = 5) -> int:
        """异步清理旧备份文件"""
        try:
            def _cleanup_backups():
                directory = os.path.dirname(filepath)
                filename = os.path.basename(filepath)
                
                # 查找备份文件
                backup_files = []
                for file in os.listdir(directory):
                    if file.startswith(f"{filename}.backup_"):
                        backup_path = os.path.join(directory, file)
                        backup_files.append((backup_path, os.path.getmtime(backup_path)))
                
                # 按修改时间排序，保留最新的几个
                backup_files.sort(key=lambda x: x[1], reverse=True)
                
                deleted_count = 0
                for backup_path, _ in backup_files[keep_count:]:
                    try:
                        os.remove(backup_path)
                        deleted_count += 1
                    except Exception as e:
                        logger.warning(f"删除备份文件失败 {backup_path}: {e}")
                
                return deleted_count
            
            # 在线程池中执行清理
            loop = asyncio.get_event_loop()
            deleted_count = await loop.run_in_executor(self._executor, _cleanup_backups)
            
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 个旧备份文件")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理备份文件失败 {filepath}: {e}")
            return 0
    
    def close(self):
        """关闭文件管理器"""
        try:
            self._executor.shutdown(wait=True)
            logger.info("异步文件管理器已关闭")
        except Exception as e:
            logger.error(f"关闭异步文件管理器失败: {e}")


class ExcelDataProcessor:
    """Excel 数据处理器"""
    
    def __init__(self, file_manager: AsyncFileManager):
        self.file_manager = file_manager
        try:
            from alias_manager import get_all_field_names
            all_fields = get_all_field_names()
            # 使用工作日期而不是日期，保持其他字段顺序
            self.fields = ["工作日期" if field == "日期" else field for field in all_fields]
        except Exception:
            # fallback
            self.fields = ["工作日期", "人员", "场子", "游戏", "卡号", "起始本金", "点码", "工资", "输反", "盈利", "备注"]
        self.fields_eng = ["msg_time", "work_date", "person", "venue", "game", "card_no",
                          "principal", "code", "salary", "rebate", "profit", "remark"]
    
    async def load_data_async(self, filepath: str, start_time: Optional[datetime] = None, 
                             end_time: Optional[datetime] = None, full_load: bool = False, 
                             exclude_zero: bool = True) -> List[Dict[str, Any]]:
        """异步加载 Excel 数据"""
        try:
            rows = await self.file_manager.read_excel_async(filepath)
            
            results = []
            for row in rows:
                try:
                    # 获得Excel填报时间，并进行时区智能纠偏
                    raw_time_cell = row[0]
                    if isinstance(raw_time_cell, str):
                        parsed_time = datetime.strptime(raw_time_cell, "%Y-%m-%d %H:%M:%S")
                    elif isinstance(raw_time_cell, datetime):
                        parsed_time = raw_time_cell
                    msg_time = parsed_time.replace(tzinfo=TIME_ZONE)

                    # if ENV == "dev":
                    #     print(f"[DEV][excel_load] 获得填报时间={msg_time} 数据={row} ")
                    
                    # 时间过滤
                    if not full_load and start_time and end_time:
                        if not (start_time <= msg_time <= end_time):
                            if ENV == "dev":
                                print(f"[DEV][excel_load] 时间过滤 skip_time msg_time={msg_time} window=({start_time},{end_time}) row={row}")
                            continue
                    
                    # 解析数值
                    profit = float(row[10]) if row[10] not in [None, ""] else 0.0
                    rebate = float(row[9]) if row[9] not in [None, ""] else 0.0
                    
                    # 排除零盈亏
                    if exclude_zero and profit == 0:
                        continue
                    
                    # 构建记录
                    record = dict(zip(self.fields_eng, row))
                    record["msg_time"] = msg_time
                    record["profit"] = profit
                    record["rebate"] = rebate
                    
                    results.append(record)
                    
                except Exception as e:
                    logger.warning(f"处理行数据失败 {row}: {e}")
                    continue
            
            # logger.info(f"Excel 数据加载完成，共 {len(results)} 条记录")

            return results
            
        except Exception as e:
            logger.error(f"异步加载 Excel 数据失败: {e}")
            return []
    
    async def write_record_async(self, filepath: str, data: Dict[str, Any], msg_time_str: str) -> bool:
        """异步写入记录到 Excel"""
        try:
            # row = [msg_time_str] + [data.get(field, "") for field in self.fields_eng]
            row = [data.get(field, "") for field in self.fields_eng]
            success = await self.file_manager.write_excel_row_async(filepath, row)
            
            if success:
                logger.debug(f"异步记录写入ECEL 成功: {msg_time_str}")
            
            return success
            
        except Exception as e:
            logger.error(f"异步写入记录失败: {e}")
            return False


# 全局文件管理器实例
_file_manager = None
_excel_processor = None

def get_file_manager() -> AsyncFileManager:
    """获取全局文件管理器实例"""
    global _file_manager
    
    if _file_manager is None:
        _file_manager = AsyncFileManager()
    
    return _file_manager

def get_excel_processor() -> ExcelDataProcessor:
    """获取全局 Excel 处理器实例"""
    global _excel_processor, _file_manager
    
    if _file_manager is None:
        _file_manager = AsyncFileManager()
    
    if _excel_processor is None:
        _excel_processor = ExcelDataProcessor(_file_manager)
    
    return _excel_processor

def close_file_manager():
    """关闭全局文件管理器"""
    global _file_manager, _excel_processor
    
    if _file_manager:
        _file_manager.close()
        _file_manager = None
    
    _excel_processor = None
