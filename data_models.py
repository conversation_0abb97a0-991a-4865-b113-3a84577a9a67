#!/usr/bin/env python3
"""
数据模型模块
定义数据结构、常量和数据验证
"""

import logging
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

# ==================== 动态字段定义（从aliases.json加载） ====================

class FieldConstants:
    """字段常量管理器，支持延迟初始化和错误回退"""

    def __init__(self):
        self._fields_chinese = None
        self._fields_english = None
        self._field_mapping = None
        self._reverse_field_mapping = None

    @property
    def FIELDS_CHINESE(self):
        """获取中文字段列表"""
        if self._fields_chinese is None:
            try:
                from alias_manager import get_all_field_names
                self._fields_chinese = get_all_field_names()
                logger.debug(f"从aliases.json加载中文字段: {self._fields_chinese}")
            except Exception as e:
                logger.warning(f"无法从aliases.json加载字段，使用fallback: {e}")
                # Fallback到硬编码列表
                self._fields_chinese = ["工作日期", "人员", "场子", "游戏", "卡号", "起始本金", "点码", "工资", "输返", "盈利", "备注"]
        return self._fields_chinese

    @property
    def FIELDS_ENGLISH(self):
        """获取英文字段列表"""
        if self._fields_english is None:
            try:
                # 基于中文字段生成对应的英文字段
                chinese_fields = self.FIELDS_CHINESE
                self._fields_english = self._convert_to_english(chinese_fields)
                logger.debug(f"生成英文字段: {self._fields_english}")
            except Exception as e:
                logger.warning(f"无法生成英文字段，使用fallback: {e}")
                # Fallback到硬编码列表
                self._fields_english = ["work_date", "person", "venue", "game", "card_no", "principal",
                                      "code", "salary", "rebate", "profit", "remark"]
        return self._fields_english

    @property
    def FIELD_MAPPING(self):
        """获取中文到英文字段映射"""
        if self._field_mapping is None:
            self._field_mapping = dict(zip(self.FIELDS_CHINESE, self.FIELDS_ENGLISH))
        return self._field_mapping

    @property
    def REVERSE_FIELD_MAPPING(self):
        """获取英文到中文字段映射"""
        if self._reverse_field_mapping is None:
            self._reverse_field_mapping = dict(zip(self.FIELDS_ENGLISH, self.FIELDS_CHINESE))
        return self._reverse_field_mapping

    def _convert_to_english(self, chinese_fields):
        """将中文字段名转换为英文字段名"""
        # 字段名映射规则
        conversion_map = {
            "工作日期": "work_date",
            "人员": "person",
            "场子": "venue",
            "游戏": "game",
            "卡号": "card_no",
            "起始本金": "principal",
            "点码": "code",
            "工资": "salary",
            "输返": "rebate",
            "输反": "rebate",  # 兼容旧的字段名
            "盈利": "profit",
            "备注": "remark"
        }

        english_fields = []
        for chinese_field in chinese_fields:
            english_field = conversion_map.get(chinese_field)
            if english_field:
                english_fields.append(english_field)
            else:
                # 如果没有找到映射，使用默认转换规则
                english_field = chinese_field.lower().replace(" ", "_")
                english_fields.append(english_field)
                logger.warning(f"未找到字段 '{chinese_field}' 的英文映射，使用默认转换: '{english_field}'")

        return english_fields

    @property
    def NUMERIC_FIELDS(self):
        """获取数字字段列表"""
        if not hasattr(self, '_numeric_fields') or self._numeric_fields is None:
            try:
                from alias_manager import get_numeric_field_names
                self._numeric_fields = get_numeric_field_names()
                logger.debug(f"从aliases.json加载数字字段: {self._numeric_fields}")
            except Exception as e:
                logger.warning(f"无法从aliases.json加载数字字段，使用fallback: {e}")
                self._numeric_fields = ["起始本金", "点码", "工资", "输反", "盈利"]
        return self._numeric_fields

    @property
    def REQUIRED_FIELDS(self):
        """获取必需字段列表"""
        if not hasattr(self, '_required_fields') or self._required_fields is None:
            try:
                from alias_manager import get_required_field_names
                self._required_fields = get_required_field_names()
                logger.debug(f"从aliases.json加载必需字段: {self._required_fields}")
            except Exception as e:
                logger.warning(f"无法从aliases.json加载必需字段，使用fallback: {e}")
                all_fields = self.FIELDS_CHINESE
                self._required_fields = [field for field in all_fields if field != "备注"]
        return self._required_fields

    def refresh(self):
        """刷新所有缓存的字段数据"""
        self._fields_chinese = None
        self._fields_english = None
        self._field_mapping = None
        self._reverse_field_mapping = None
        if hasattr(self, '_numeric_fields'):
            self._numeric_fields = None
        if hasattr(self, '_required_fields'):
            self._required_fields = None
        logger.info("字段常量缓存已刷新")

# 创建全局实例
_field_constants = FieldConstants()

# 保持向后兼容的常量访问方式
FIELDS_CHINESE = _field_constants.FIELDS_CHINESE
FIELDS_ENGLISH = _field_constants.FIELDS_ENGLISH
FIELD_MAPPING = _field_constants.FIELD_MAPPING
REVERSE_FIELD_MAPPING = _field_constants.REVERSE_FIELD_MAPPING
NUMERIC_FIELDS = _field_constants.NUMERIC_FIELDS
REQUIRED_FIELDS = _field_constants.REQUIRED_FIELDS

# 提供刷新函数
def refresh_field_constants():
    """刷新字段常量（当aliases.json更新时调用）"""
    _field_constants.refresh()

class GameType(Enum):
    """游戏类型枚举"""
    BJ = "BJ"
    UTH = "UTH"
    BACCARAT = "百家乐"
    RUSSIAN = "俄罗斯"
    POKER = "扑克"
    OTHER = "其他"

class RecordStatus(Enum):
    """记录状态枚举"""
    PENDING = "pending"
    SYNCED = "synced"
    FAILED = "failed"
    CORRECTED = "corrected"

@dataclass
class GameRecord:
    """游戏记录数据模型"""
    msg_time: datetime
    work_date: str
    person: str
    venue: str
    game: str
    card_no: str = ""
    principal: int = 0
    code: int = 0
    salary: int = 0
    rebate: int = 0
    profit: int = 0
    remark: str = ""
    status: RecordStatus = RecordStatus.PENDING
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'msg_time': self.msg_time,
            'work_date': self.work_date,
            'person': self.person,
            'venue': self.venue,
            'game': self.game,
            'card_no': self.card_no,
            'principal': self.principal,
            'code': self.code,
            'salary': self.salary,
            'rebate': self.rebate,
            'profit': self.profit,
            'remark': self.remark,
            'status': self.status.value
        }
    
    def to_excel_row(self, include_msg_time: bool = True) -> List[Any]:
        """转换为 Excel 行数据"""
        row = []
        if include_msg_time:
            row.append(self.msg_time.strftime("%Y-%m-%d %H:%M:%S"))
        
        row.extend([
            self.work_date,
            self.person,
            self.venue,
            self.game,
            self.card_no,
            self.principal,
            self.code,
            self.salary,
            self.rebate,
            self.profit,
            self.remark
        ])
        
        return row
    def to_excel_row_dict(self, include_msg_time: bool = True) -> dict:
        """转换为 Excel 行数据（以字典形式返回）"""
        row = {}

        if include_msg_time:
            row["msg_time"] = self.msg_time.strftime("%Y-%m-%d %H:%M:%S")

        row.update({
            "work_date": self.work_date,
            "person": self.person,
            "venue": self.venue,
            "game": self.game,
            "card_no": self.card_no,
            "principal": self.principal,
            "code": self.code,
            "salary": self.salary,
            "rebate": self.rebate,
            "profit": self.profit,
            "remark": self.remark
        })

        return row
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GameRecord':
        """从字典创建记录"""
        # 处理字段映射：如果输入是中文字段名，转换为英文字段名
        mapped_data = {}
        for key, value in data.items():
            if key in FIELD_MAPPING:
                # 中文字段名映射到英文字段名
                english_key = FIELD_MAPPING[key]
                mapped_data[english_key] = value
            else:
                # 已经是英文字段名或其他字段
                mapped_data[key] = value

        # 处理状态
        status = RecordStatus.PENDING
        if 'status' in mapped_data:
            try:
                status = RecordStatus(mapped_data['status'])
            except ValueError:
                status = RecordStatus.PENDING

        # 处理日期字段映射
        work_date = mapped_data.get('work_date', '')
        if not work_date and '日期' in data:
            work_date = str(data['日期'])

        # 安全的整数转换函数
        def safe_int(value, default=0):
            if value is None or value == '':
                return default
            try:
                return int(value)
            except (ValueError, TypeError):
                return default

        return cls(
            msg_time=mapped_data.get('msg_time', datetime.now()),
            work_date=work_date,
            person=mapped_data.get('person', ''),
            venue=mapped_data.get('venue', ''),
            game=mapped_data.get('game', ''),
            card_no=mapped_data.get('card_no', ''),
            principal=safe_int(mapped_data.get('principal', 0)),
            code=safe_int(mapped_data.get('code', 0)),
            salary=safe_int(mapped_data.get('salary', 0)),
            rebate=safe_int(mapped_data.get('rebate', 0)),
            profit=safe_int(mapped_data.get('profit', 0)),
            remark=mapped_data.get('remark', ''),
            status=status
        )
    
    @classmethod
    def from_excel_row(cls, row: List[Any], msg_time: datetime) -> 'GameRecord':
        """从 Excel 行数据创建记录"""
        return cls(
            msg_time=msg_time,
            work_date=str(row[0]) if len(row) > 0 else '',
            person=str(row[1]) if len(row) > 1 else '',
            venue=str(row[2]) if len(row) > 2 else '',
            game=str(row[3]) if len(row) > 3 else '',
            card_no=str(row[4]) if len(row) > 4 else '',
            principal=int(row[5]) if len(row) > 5 and row[5] not in [None, ""] else 0,
            code=int(row[6]) if len(row) > 6 and row[6] not in [None, ""] else 0,
            salary=int(row[7]) if len(row) > 7 and row[7] not in [None, ""] else 0,
            rebate=int(row[8]) if len(row) > 8 and row[8] not in [None, ""] else 0,
            profit=int(row[9]) if len(row) > 9 and row[9] not in [None, ""] else 0,
            remark=str(row[10]) if len(row) > 10 else ''
        )

@dataclass
class RebateRule:
    """输返规则数据模型"""
    venue: str
    person: Optional[str] = None
    ratio: float = 0.1
    is_default: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'venue': self.venue,
            'person': self.person,
            'ratio': self.ratio,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RebateRule':
        """从字典创建规则"""
        created_at = datetime.now()
        updated_at = datetime.now()
        
        if 'created_at' in data:
            try:
                created_at = datetime.fromisoformat(data['created_at'])
            except ValueError:
                pass
        
        if 'updated_at' in data:
            try:
                updated_at = datetime.fromisoformat(data['updated_at'])
            except ValueError:
                pass
        
        return cls(
            venue=data.get('venue', ''),
            person=data.get('person'),
            ratio=float(data.get('ratio', 0.1)),
            is_default=bool(data.get('is_default', False)),
            created_at=created_at,
            updated_at=updated_at
        )

@dataclass
class SalaryRule:
    """工资规则数据模型"""
    rebate_ratio: float
    game: str
    profit_min: Optional[int] = None
    profit_max: Optional[int] = None
    salary: int = 0
    enabled: bool = True
    description: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def matches_profit(self, profit: int) -> bool:
        """检查盈利是否匹配此规则"""
        if not self.enabled:
            return False
        
        if self.profit_min is not None and profit < self.profit_min:
            return False
        
        if self.profit_max is not None and profit > self.profit_max:
            return False
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'rebate_ratio': self.rebate_ratio,
            'game': self.game,
            'profit_min': self.profit_min,
            'profit_max': self.profit_max,
            'salary': self.salary,
            'enabled': self.enabled,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SalaryRule':
        """从字典创建规则"""
        created_at = datetime.now()
        updated_at = datetime.now()
        
        if 'created_at' in data:
            try:
                created_at = datetime.fromisoformat(data['created_at'])
            except ValueError:
                pass
        
        if 'updated_at' in data:
            try:
                updated_at = datetime.fromisoformat(data['updated_at'])
            except ValueError:
                pass
        
        return cls(
            rebate_ratio=float(data.get('rebate_ratio', 0.1)),
            game=data.get('game', ''),
            profit_min=data.get('profit_min'),
            profit_max=data.get('profit_max'),
            salary=int(data.get('salary', 0)),
            enabled=bool(data.get('enabled', True)),
            description=data.get('description', ''),
            created_at=created_at,
            updated_at=updated_at
        )

@dataclass
class OperationResult:
    """操作结果数据模型"""
    success: bool
    message: str = ""
    data: Any = None
    error_code: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'success': self.success,
            'message': self.message,
            'data': self.data,
            'error_code': self.error_code,
            'timestamp': self.timestamp.isoformat()
        }

@dataclass
class SyncStatus:
    """同步状态数据模型"""
    record_id: str
    status: RecordStatus
    last_attempt: datetime
    attempt_count: int = 0
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'record_id': self.record_id,
            'status': self.status.value,
            'last_attempt': self.last_attempt.isoformat(),
            'attempt_count': self.attempt_count,
            'error_message': self.error_message
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncStatus':
        """从字典创建状态"""
        status = RecordStatus.PENDING
        if 'status' in data:
            try:
                status = RecordStatus(data['status'])
            except ValueError:
                status = RecordStatus.PENDING
        
        last_attempt = datetime.now()
        if 'last_attempt' in data:
            try:
                last_attempt = datetime.fromisoformat(data['last_attempt'])
            except ValueError:
                pass
        
        return cls(
            record_id=data.get('record_id', ''),
            status=status,
            last_attempt=last_attempt,
            attempt_count=int(data.get('attempt_count', 0)),
            error_message=data.get('error_message', '')
        )

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_game_record(record: GameRecord) -> OperationResult:
        """验证游戏记录"""
        errors = []
        
        # 必填字段检查
        if not record.person.strip():
            errors.append("人员名称不能为空")
        
        if not record.venue.strip():
            errors.append("场子名称不能为空")
        
        if not record.game.strip():
            errors.append("游戏类型不能为空")
        
        # 数值范围检查
        if record.principal < 0:
            errors.append("本金不能为负数")
        
        if record.code < 0:
            errors.append("点码不能为负数")
        
        if record.salary < 0:
            errors.append("工资不能为负数")
        
        if record.rebate < 0:
            errors.append("输反不能为负数")
        
        # 逻辑检查
        if record.rebate > record.principal:
            errors.append("输反不能大于本金")
        
        if errors:
            return OperationResult(
                success=False,
                message="; ".join(errors),
                error_code="VALIDATION_ERROR"
            )
        
        return OperationResult(success=True, message="验证通过")
    
    @staticmethod
    def validate_rebate_rule(rule: RebateRule) -> OperationResult:
        """验证输返规则"""
        errors = []
        
        if not rule.venue.strip():
            errors.append("场子名称不能为空")
        
        if not (0 <= rule.ratio <= 1):
            errors.append("输返比例必须在 0-1 之间")
        
        if errors:
            return OperationResult(
                success=False,
                message="; ".join(errors),
                error_code="VALIDATION_ERROR"
            )
        
        return OperationResult(success=True, message="验证通过")
    
    @staticmethod
    def validate_salary_rule(rule: SalaryRule) -> OperationResult:
        """验证工资规则"""
        errors = []
        
        if not (0 <= rule.rebate_ratio <= 1):
            errors.append("rebate 比例必须在 0-1 之间")
        
        if not rule.game.strip():
            errors.append("游戏类型不能为空")
        
        if rule.salary < 0:
            errors.append("工资不能为负数")
        
        if (rule.profit_min is not None and rule.profit_max is not None and 
            rule.profit_min > rule.profit_max):
            errors.append("盈亏下限不能大于上限")
        
        if errors:
            return OperationResult(
                success=False,
                message="; ".join(errors),
                error_code="VALIDATION_ERROR"
            )
        
        return OperationResult(success=True, message="验证通过")
