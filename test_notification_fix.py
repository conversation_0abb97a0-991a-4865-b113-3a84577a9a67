#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test notification fix for monthly job
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_bot_creation():
    """Test bot creation in manual execution"""
    try:
        print("🤖 测试Bot实例创建...")
        
        from config import Config
        from telegram import Bot
        
        print(f"Bot Token配置: {'已配置' if Config.BOT_TOKEN else '未配置'}")
        
        if Config.BOT_TOKEN:
            bot = Bot(token=Config.BOT_TOKEN)
            print("✅ Bot实例创建成功")
            
            # 测试bot是否可用
            try:
                # 这个调用需要网络连接，可能会失败
                bot_info = bot.get_me()
                print(f"✅ Bot信息获取成功: {bot_info.username}")
                return True
            except Exception as e:
                print(f"⚠️ Bot创建成功但无法获取信息（可能是网络问题）: {e}")
                return True  # Bot创建成功就算通过
        else:
            print("❌ Bot Token未配置")
            return False
        
    except Exception as e:
        print(f"❌ Bot实例创建失败: {e}")
        return False

def test_chat_id_configuration():
    """Test chat ID configuration"""
    try:
        print("\n💬 测试Chat ID配置...")
        
        from config import Config
        
        print(f"环境: {Config.ENV}")
        print(f"群组Chat ID: {Config.GROUP_CHAT_ID}")
        
        if Config.GROUP_CHAT_ID:
            print("✅ Chat ID配置正确")
            return True
        else:
            print("❌ Chat ID未配置")
            return False
        
    except Exception as e:
        print(f"❌ Chat ID配置检查失败: {e}")
        return False

def test_mock_context():
    """Test mock context creation"""
    try:
        print("\n🎭 测试Mock Context创建...")
        
        # 模拟manual_complete_monthly_job中的MockContext创建
        class MockContext:
            def __init__(self):
                # 尝试获取真实的bot实例
                try:
                    from config import Config
                    from telegram import Bot
                    self.bot = Bot(token=Config.BOT_TOKEN)
                    print("✅ Mock Context中成功创建bot实例")
                except Exception as e:
                    print(f"⚠️ Mock Context中无法创建bot实例: {e}")
                    self.bot = None
                self.job = MockJob()

        class MockJob:
            def __init__(self):
                # 手动执行时也发送通知到默认群组
                from config import Config
                self.data = {'chat_id': Config.GROUP_CHAT_ID}

        context = MockContext()
        
        print(f"Mock Context创建结果:")
        print(f"  - bot: {context.bot is not None}")
        print(f"  - chat_id: {context.job.data.get('chat_id')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock Context创建失败: {e}")
        return False

def test_notification_content():
    """Test notification content generation"""
    try:
        print("\n📝 测试通知内容生成...")
        
        # 模拟通知内容生成
        from monthlyjob import MonthlyReport
        from datetime import datetime
        
        # 创建模拟数据
        summary = MonthlyReport(
            report_month="2025-08",
            total_income=10000,
            total_expense=5000,
            net_profit=5000,
            profit_members_count=3,
            loss_members_count=1,
            total_work_sessions=10,
            main_expense_category="交通",
            remark="测试报告"
        )
        
        salary_result = {
            'checked_records': 15,
            'updated_records': 3
        }
        
        # 生成通知内容
        notification = f"""✅ 月度工作流执行完成

📊 工资检查结果：
  - 检查记录：{salary_result.get('checked_records', 0)} 条
  - 更新记录：{salary_result.get('updated_records', 0)} 条

📈 月度汇总：
  - 净利润：{summary.net_profit:+,.0f}
  - 盈利成员：{summary.profit_members_count} 人
  - 开工次数：{summary.total_work_sessions} 次

📄 详细报表将在下一条消息发送"""
        
        print("✅ 通知内容生成成功:")
        print("=" * 50)
        print(notification)
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ 通知内容生成失败: {e}")
        return False

def test_manual_monthly_job():
    """Test manual monthly job execution"""
    try:
        print("\n🚀 测试手动月度任务执行...")
        
        from monthlyjob import manual_complete_monthly_job
        
        # 执行手动月度任务
        result = manual_complete_monthly_job(2025, 8)
        
        print(f"执行结果:")
        print(f"  - 成功: {result.get('success', False)}")
        print(f"  - 手动执行: {result.get('manual_execution', False)}")
        
        errors = result.get('errors', [])
        print(f"  - 错误数量: {len(errors)}")
        
        if errors:
            print("  - 错误详情:")
            for i, error in enumerate(errors[:3], 1):
                print(f"    {i}. {error}")
                
                # 检查是否还有通知发送失败的错误
                if "无法发送通知" in str(error):
                    print(f"      ⚠️ 仍然存在通知发送问题")
                    return False
        
        print("✅ 手动月度任务测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 手动月度任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("月度通知修复测试")
    print("=" * 50)
    
    # 测试1: Bot创建
    success1 = test_bot_creation()
    
    # 测试2: Chat ID配置
    success2 = test_chat_id_configuration()
    
    # 测试3: Mock Context
    success3 = test_mock_context()
    
    # 测试4: 通知内容
    success4 = test_notification_content()
    
    # 测试5: 手动月度任务
    success5 = test_manual_monthly_job()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"Bot创建: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"Chat ID配置: {'✅ 通过' if success2 else '❌ 失败'}")
    print(f"Mock Context: {'✅ 通过' if success3 else '❌ 失败'}")
    print(f"通知内容: {'✅ 通过' if success4 else '❌ 失败'}")
    print(f"手动月度任务: {'✅ 通过' if success5 else '❌ 失败'}")
    
    if success1 and success2 and success3 and success4 and success5:
        print("\n🎉 所有测试通过！通知功能应该正常工作。")
    else:
        print("\n❌ 部分测试失败，请检查上述错误信息。")

if __name__ == "__main__":
    main()
