[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests that take more than 1 second
    network: Tests that require network access
    google_api: Tests that require Google API access

# Test output + Coverage + Parallel
addopts = 
    -v 
    --tb=short 
    --strict-markers 
    --disable-warnings 
    --color=yes 
    --durations=10 
    --cov=. 
    --cov-report=html:htmlcov 
    --cov-report=term-missing 
    --cov-report=xml 
    --cov-fail-under=80 
    -n auto

# Async settings
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Timeout
timeout = 300

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:openpyxl.*
