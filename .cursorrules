# Cursor Rules for Telegram Bot Monthly Job Feature Project

## 项目概述
这是一个基于Python的Telegram机器人项目，用于管理月度工作记录、薪资计算、返利配置等功能。项目采用异步编程模式，集成了Google Sheets、Excel文件操作、数据缓存等特性。

## 代码风格和规范

### Python代码规范
- 使用Python 3.8+语法特性
- 遵循PEP 8代码风格规范
- 使用类型注解（Type Hints）
- 优先使用异步编程（async/await）
- 使用dataclass和Enum定义数据结构
- 函数和类使用中文文档字符串

### 文件组织
- 主入口文件：`main_with_monitoring.py`
- 核心业务逻辑：`commander.py`, `Group_record.py`
- 数据模型：`data_models.py`
- 配置管理：`config.py`, `config_manager.py`
- 错误处理：`error_handling.py`, `error_strategy.py`
- 日志系统：`logging_config.py`
- 存储服务：`storage.py`, `storage_service.py`
- 工具模块：`utils.py`, `Parser.py`

### 导入顺序
1. 标准库导入
2. 第三方库导入
3. 本地模块导入
4. 配置导入

### 错误处理
- 使用自定义异常类（继承自StorageError）
- 实现重试机制和熔断器模式
- 使用装饰器进行错误处理：`@with_error_handling`
- 记录详细的错误日志和上下文信息

### 日志规范
- 使用结构化日志记录
- 支持控制台、文件、JSON多种输出格式
- 不同模块使用不同的日志级别
- 关键操作记录性能指标

### 数据验证
- 使用DataValidator类进行数据验证
- 实现输入安全验证（SecurityValidator）
- 对用户输入进行清理和验证

## 开发指导原则

### 异步编程
- 优先使用async/await语法
- 正确处理异步上下文和资源管理
- 使用asyncio.gather()进行并发操作
- 实现优雅的关闭机制

### 配置管理
- 使用环境变量和配置文件
- 支持开发/生产环境切换
- 实现配置缓存和热重载
- 敏感信息使用环境变量

### 数据存储
- 支持Excel文件和Google Sheets双存储
- 实现数据同步和缓存机制
- 使用连接池管理资源
- 实现数据备份和恢复

### 安全考虑
- 输入验证和清理
- 权限控制和用户授权
- 敏感数据加密存储
- 防止注入攻击

### 性能优化
- 使用缓存减少重复计算
- 实现连接池和资源复用
- 异步I/O操作
- 内存使用监控

## 代码示例

### 异步函数定义
```python
@with_error_handling(operation_name="example_operation", notify_user=True)
async def example_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """示例异步处理器"""
    try:
        # 业务逻辑
        result = await some_async_operation()
        await update.message.reply_text(f"操作成功: {result}")
    except ValidationError as e:
        await update.message.reply_text(f"输入错误: {e}")
    except Exception as e:
        logger.error(f"操作失败: {e}", exc_info=True)
        await update.message.reply_text("操作失败，请稍后重试")
```

### 数据模型定义
```python
@dataclass
class ExampleModel:
    """示例数据模型"""
    field1: str
    field2: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'field1': self.field1,
            'field2': self.field2,
            'created_at': self.created_at.isoformat()
        }
```

### 错误处理
```python
try:
    result = await risky_operation()
except NetworkError as e:
    logger.warning(f"网络错误，将重试: {e}")
    # 重试逻辑
except ValidationError as e:
    logger.info(f"验证失败: {e}")
    # 用户友好的错误消息
except Exception as e:
    logger.error(f"未知错误: {e}", exc_info=True)
    # 通用错误处理
```

## 测试规范
- 使用pytest进行单元测试
- 测试文件放在tests/目录下
- 使用fixture进行测试数据准备
- 实现集成测试和单元测试

## 部署和运维
- 支持Docker容器化部署
- 实现健康检查和监控
- 使用日志轮转和清理
- 支持优雅关闭和重启

## 注意事项
- 所有用户输入必须经过验证和清理
- 敏感操作需要权限检查
- 网络操作需要重试机制
- 文件操作需要异常处理
- 配置更改需要缓存刷新
- 长时间运行的任务需要进度反馈

## 常用命令和功能
- `/help` - 显示帮助信息
- `/daily_report` - 生成日报
- `/monthly_report` - 生成月报
- `/set_rebate` - 设置返利比例
- `/add_venue` - 添加新场所
- `/find` - 查找记录
- `/export_file` - 导出数据

## 开发工具推荐
- 使用black进行代码格式化
- 使用flake8进行代码检查
- 使用mypy进行类型检查
- 使用pytest进行测试
- 使用pre-commit进行代码质量检查
