#!/usr/bin/env python3
"""
Debug the specific failed message from 2025-08-16 04:38:19
"""

def analyze_failed_message():
    """Analyze the specific message that failed to be stored"""
    print("Analyzing the failed message from 2025-08-16 04:38:19...")
    
    # The exact message content from the log
    message_text = """日期：8月15日
人员：俊
场子： Billionaire 
游戏：UTH
卡号:  02892
本金：1500
点码:  100
工资：0
输反：280
赢亏:  -1120
备注：ANTE25；口..."""
    
    print(f"Original message:\n{message_text}")
    print("\n" + "="*50)
    
    try:
        # Test parsing
        print("1. Testing message parsing...")
        from Parser import parse_message
        
        parsed, missing = parse_message(message_text)
        print(f"Parsed data: {parsed}")
        print(f"Missing fields: {missing}")
        
        # Test validation
        print("\n2. Testing message validation...")
        from security_utils import InputValidator
        
        is_valid, validation_errors = InputValidator.validate_message_data(parsed)
        print(f"Validation result: {is_valid}")
        if not is_valid:
            print(f"Validation errors: {validation_errors}")
        
        # Test data model creation
        print("\n3. Testing data model creation...")
        from data_models import GameRecord, DataValidator
        
        record = GameRecord.from_dict(parsed)
        print(f"GameRecord created: {record}")
        
        # Test data model validation
        print("\n4. Testing data model validation...")
        validation_result = DataValidator.validate_game_record(record)
        print(f"Data model validation: {validation_result.success}")
        if not validation_result.success:
            print(f"Data model errors: {validation_result.message}")
        
        # Check for potential issues
        print("\n5. Checking for potential issues...")
        
        # Check field values
        issues = []
        
        # Check for whitespace issues
        for key, value in parsed.items():
            if isinstance(value, str) and value != value.strip():
                issues.append(f"Field '{key}' has leading/trailing whitespace: '{value}'")
        
        # Check for special characters
        venue = parsed.get('场子', '')
        if venue and venue.strip() != venue:
            issues.append(f"Venue has whitespace: '{venue}' -> '{venue.strip()}'")
        
        # Check numeric values
        try:
            principal = int(parsed.get('起始本金', 0))
            rebate = int(parsed.get('输反', 0))
            profit = int(parsed.get('盈利', 0))
            
            if rebate > principal:
                issues.append(f"Rebate ({rebate}) > Principal ({principal})")
            
            print(f"Numeric values: Principal={principal}, Rebate={rebate}, Profit={profit}")
            
        except ValueError as e:
            issues.append(f"Numeric conversion error: {e}")
        
        if issues:
            print("Potential issues found:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("No obvious issues found with the message data")
        
        # Test the exact scenario that might have caused the crash
        print("\n6. Testing potential crash scenarios...")
        
        # Check if there might be encoding issues
        try:
            encoded = message_text.encode('utf-8')
            decoded = encoded.decode('utf-8')
            if decoded != message_text:
                print("Encoding/decoding issue detected")
        except Exception as e:
            print(f"Encoding error: {e}")
        
        # Check for None values
        none_fields = [k for k, v in parsed.items() if v is None]
        if none_fields:
            print(f"Fields with None values: {none_fields}")
        
        print("\n7. Summary:")
        print("The message appears to be valid and should have been processed successfully.")
        print("The failure was likely due to:")
        print("  1. Network interruption during processing")
        print("  2. Bot process crash/restart")
        print("  3. System resource issues")
        print("  4. Unhandled exception in storage processing")
        
        return True
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_log_patterns():
    """Check for patterns in the log that might explain the failure"""
    print("\nChecking log patterns...")
    
    # The key observations from the log analysis
    observations = [
        "Message received and parsed at 04:38:19",
        "No 'record write success' log entry for this message",
        "50-minute gap until next log entry at 05:28:45",
        "Network errors (httpx.ReadError) occurring around the same time",
        "Message was never cached (cache file is empty)",
        "No validation errors logged"
    ]
    
    print("Key observations from log analysis:")
    for i, obs in enumerate(observations, 1):
        print(f"  {i}. {obs}")
    
    print("\nMost likely cause:")
    print("  The bot process was interrupted or crashed during message processing,")
    print("  likely due to network connectivity issues affecting the entire system.")
    print("  The message processing started but never completed, so it was neither")
    print("  saved to storage nor cached for retry.")


def recommend_solutions():
    """Recommend solutions to prevent this issue"""
    print("\nRecommended solutions:")
    
    solutions = [
        "1. Implement process monitoring and auto-restart",
        "2. Add more comprehensive error handling in message processing",
        "3. Implement message acknowledgment system",
        "4. Add heartbeat logging to detect process interruptions",
        "5. Implement graceful shutdown handling",
        "6. Add message queuing for reliability",
        "7. Monitor system resources (memory, CPU)",
        "8. Implement network connectivity monitoring"
    ]
    
    for solution in solutions:
        print(f"  {solution}")
    
    print("\nImmediate actions:")
    print("  1. Deploy the improved message caching integration we implemented")
    print("  2. Monitor logs more closely for process interruptions")
    print("  3. Set up alerts for gaps in log entries")
    print("  4. Consider implementing a message acknowledgment system")


if __name__ == "__main__":
    print("Debugging failed message from 2025-08-16 04:38:19")
    print("="*60)
    
    success = analyze_failed_message()
    check_log_patterns()
    recommend_solutions()
    
    if success:
        print("\nDebugging completed successfully.")
    else:
        print("\nDebugging encountered errors.")
