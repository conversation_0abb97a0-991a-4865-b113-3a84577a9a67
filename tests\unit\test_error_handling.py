#!/usr/bin/env python3
"""
Unit tests for error handling
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from error_handling import (
    <PERSON>rrorHandler, StorageError, NetworkError, FileOperationError,
    GoogleAPIError, ValidationError, ErrorType, ErrorSeverity,
    RetryConfig, with_retry, with_error_handling, CircuitBreaker,
    NETWORK_RETRY_CONFIG, FILE_RETRY_CONFIG
)

class TestStorageError:
    """Test StorageError and its subclasses"""
    
    def test_storage_error_creation(self):
        """Test creating StorageError"""
        error = StorageError(
            "测试错误", 
            ErrorType.VALIDATION_ERROR, 
            ErrorSeverity.MEDIUM,
            {"detail": "test"}
        )
        
        assert str(error) == "测试错误"
        assert error.error_type == ErrorType.VALIDATION_ERROR
        assert error.severity == ErrorSeverity.MEDIUM
        assert error.details == {"detail": "test"}
        assert isinstance(error.timestamp, datetime)
    
    def test_network_error(self):
        """Test NetworkError"""
        error = NetworkError("网络连接失败")
        
        assert error.error_type == ErrorType.NETWORK_ERROR
        assert error.severity == ErrorSeverity.HIGH
        assert str(error) == "网络连接失败"
    
    def test_file_operation_error(self):
        """Test FileOperationError"""
        error = FileOperationError("文件读取失败")
        
        assert error.error_type == ErrorType.FILE_ERROR
        assert error.severity == ErrorSeverity.MEDIUM
        assert str(error) == "文件读取失败"
    
    def test_google_api_error(self):
        """Test GoogleAPIError"""
        error = GoogleAPIError("API调用失败")
        
        assert error.error_type == ErrorType.GOOGLE_API_ERROR
        assert error.severity == ErrorSeverity.HIGH
        assert str(error) == "API调用失败"
    
    def test_validation_error(self):
        """Test ValidationError"""
        error = ValidationError("数据验证失败")
        
        assert error.error_type == ErrorType.VALIDATION_ERROR
        assert error.severity == ErrorSeverity.LOW
        assert str(error) == "数据验证失败"

class TestErrorHandler:
    """Test ErrorHandler"""
    
    def test_error_handler_creation(self):
        """Test creating ErrorHandler"""
        handler = ErrorHandler()
        
        assert len(handler.error_stats) == len(ErrorType)
        assert handler.recent_errors == []
        assert handler.max_recent_errors == 100
    
    def test_log_error(self):
        """Test logging errors"""
        handler = ErrorHandler()
        error = StorageError("测试错误", ErrorType.VALIDATION_ERROR)
        
        handler.log_error(error, "test_context", {"extra": "data"})
        
        # Check statistics
        stats = handler.get_error_stats()
        assert stats['error_counts']['validation_error'] == 1
        assert stats['recent_errors_count'] == 1
        
        # Check recent errors
        recent = handler.get_recent_errors(1)
        assert len(recent) == 1
        assert recent[0]['context'] == "test_context"
        assert recent[0]['extra_data'] == {"extra": "data"}
    
    def test_log_multiple_errors(self):
        """Test logging multiple errors"""
        handler = ErrorHandler()
        
        # Log different types of errors
        handler.log_error(NetworkError("网络错误"), "network_context")
        handler.log_error(FileOperationError("文件错误"), "file_context")
        handler.log_error(ValidationError("验证错误"), "validation_context")
        
        stats = handler.get_error_stats()
        assert stats['error_counts']['network_error'] == 1
        assert stats['error_counts']['file_error'] == 1
        assert stats['error_counts']['validation_error'] == 1
        assert stats['recent_errors_count'] == 3
    
    def test_max_recent_errors_limit(self):
        """Test recent errors limit"""
        handler = ErrorHandler()
        handler.max_recent_errors = 3
        
        # Log more errors than the limit
        for i in range(5):
            handler.log_error(StorageError(f"错误{i}"), f"context{i}")
        
        recent = handler.get_recent_errors(10)
        assert len(recent) == 3  # Should be limited to max_recent_errors
        assert recent[-1]['message'] == "错误4"  # Should keep the most recent

class TestRetryConfig:
    """Test RetryConfig"""
    
    def test_retry_config_creation(self):
        """Test creating RetryConfig"""
        config = RetryConfig(
            max_attempts=5,
            base_delay=2.0,
            max_delay=30.0,
            exponential_base=2.0
        )
        
        assert config.max_attempts == 5
        assert config.base_delay == 2.0
        assert config.max_delay == 30.0
        assert config.exponential_base == 2.0
    
    def test_should_retry(self):
        """Test retry decision logic"""
        config = RetryConfig(max_attempts=3, retryable_exceptions=[NetworkError])
        
        # Should retry network errors within attempt limit
        assert config.should_retry(NetworkError("网络错误"), 1)
        assert config.should_retry(NetworkError("网络错误"), 2)
        assert not config.should_retry(NetworkError("网络错误"), 3)  # At max attempts
        
        # Should not retry non-retryable errors
        assert not config.should_retry(ValidationError("验证错误"), 1)
    
    def test_get_delay(self):
        """Test delay calculation"""
        config = RetryConfig(base_delay=1.0, exponential_base=2.0, max_delay=10.0)
        
        assert config.get_delay(1) == 1.0   # base_delay * 2^0
        assert config.get_delay(2) == 2.0   # base_delay * 2^1
        assert config.get_delay(3) == 4.0   # base_delay * 2^2
        assert config.get_delay(10) == 10.0 # Should be capped at max_delay

class TestRetryDecorator:
    """Test retry decorator"""
    
    @pytest.mark.asyncio
    async def test_successful_function(self):
        """Test retry decorator with successful function"""
        call_count = 0
        
        @with_retry(RetryConfig(max_attempts=3))
        async def test_function():
            nonlocal call_count
            call_count += 1
            return "success"
        
        result = await test_function()
        assert result == "success"
        assert call_count == 1  # Should only be called once
    
    @pytest.mark.asyncio
    async def test_retry_on_failure(self):
        """Test retry decorator with failing function"""
        call_count = 0
        
        @with_retry(RetryConfig(max_attempts=3, base_delay=0.01, retryable_exceptions=[NetworkError]))
        async def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise NetworkError("网络错误")
            return "success"
        
        result = await test_function()
        assert result == "success"
        assert call_count == 3  # Should be called 3 times
    
    @pytest.mark.asyncio
    async def test_max_attempts_exceeded(self):
        """Test retry decorator when max attempts exceeded"""
        call_count = 0
        
        @with_retry(RetryConfig(max_attempts=2, base_delay=0.01, retryable_exceptions=[NetworkError]))
        async def test_function():
            nonlocal call_count
            call_count += 1
            raise NetworkError("网络错误")
        
        with pytest.raises(NetworkError):
            await test_function()
        
        assert call_count == 2  # Should be called max_attempts times
    
    @pytest.mark.asyncio
    async def test_non_retryable_error(self):
        """Test retry decorator with non-retryable error"""
        call_count = 0
        
        @with_retry(RetryConfig(max_attempts=3, retryable_exceptions=[NetworkError]))
        async def test_function():
            nonlocal call_count
            call_count += 1
            raise ValidationError("验证错误")
        
        with pytest.raises(ValidationError):
            await test_function()
        
        assert call_count == 1  # Should only be called once

class TestErrorHandlingDecorator:
    """Test error handling decorator"""
    
    @pytest.mark.asyncio
    async def test_error_handling_decorator(self):
        """Test error handling decorator"""
        handler = ErrorHandler()
        
        @with_error_handling(handler, "test_context")
        async def test_function():
            raise StorageError("测试错误")
        
        with pytest.raises(StorageError):
            await test_function()
        
        # Check that error was logged
        stats = handler.get_error_stats()
        assert stats['recent_errors_count'] == 1
        
        recent = handler.get_recent_errors(1)
        assert recent[0]['context'] == "test_context"

class TestCircuitBreaker:
    """Test CircuitBreaker"""
    
    def test_circuit_breaker_creation(self):
        """Test creating CircuitBreaker"""
        breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=60)
        
        assert breaker.failure_threshold == 3
        assert breaker.recovery_timeout == 60
        assert breaker.failure_count == 0
        assert breaker.state == "CLOSED"
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_success(self):
        """Test circuit breaker with successful function"""
        breaker = CircuitBreaker(failure_threshold=3)
        
        @breaker
        async def test_function():
            return "success"
        
        result = await test_function()
        assert result == "success"
        assert breaker.state == "CLOSED"
        assert breaker.failure_count == 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_failure_threshold(self):
        """Test circuit breaker opening after failure threshold"""
        breaker = CircuitBreaker(failure_threshold=2, expected_exception=NetworkError)
        
        @breaker
        async def test_function():
            raise NetworkError("网络错误")
        
        # First failure
        with pytest.raises(NetworkError):
            await test_function()
        assert breaker.state == "CLOSED"
        assert breaker.failure_count == 1
        
        # Second failure - should open circuit
        with pytest.raises(NetworkError):
            await test_function()
        assert breaker.state == "OPEN"
        assert breaker.failure_count == 2
        
        # Third call - should be blocked by circuit breaker
        with pytest.raises(StorageError) as exc_info:
            await test_function()
        assert "熔断器开启" in str(exc_info.value)

class TestPredefinedConfigs:
    """Test predefined retry configurations"""
    
    def test_network_retry_config(self):
        """Test NETWORK_RETRY_CONFIG"""
        config = NETWORK_RETRY_CONFIG
        
        assert config.max_attempts == 3
        assert config.base_delay == 2.0
        assert config.max_delay == 30.0
        assert NetworkError in config.retryable_exceptions
        assert GoogleAPIError in config.retryable_exceptions
    
    def test_file_retry_config(self):
        """Test FILE_RETRY_CONFIG"""
        config = FILE_RETRY_CONFIG
        
        assert config.max_attempts == 2
        assert config.base_delay == 0.5
        assert config.max_delay == 5.0
        assert FileOperationError in config.retryable_exceptions
