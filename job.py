from datetime import datetime

from telegram.ext import ContextTypes

from Parser import get_last_month_range, get_yesterday_range, get_current_week_range, get_current_month_range, \
    get_last_week_range
from charts import send_venue_profit_bar_chart, send_person_profit_bar_chart, send_profit_trend_chart
from report import generate_and_send_report
from config import Config

TIME_ZONE = Config.TIME_ZONE  # 使用 zoneinfo 模块处理时区

# --- Job functions would call this ---
async def send_daily_report_job(context: ContextTypes.DEFAULT_TYPE):
    result = get_yesterday_range()
    if result is None:
        raise ValueError("无法获取昨天的起止时间")
    start, end = result
    # DEV 调试日志：打印本次日报时间窗口
    if Config.ENV == "dev":
        try:
            print(f"[DEV][daily_job] start={start.isoformat()} end={end.isoformat()} tz={TIME_ZONE}")
        except Exception:
            pass
    await generate_and_send_report(context, start, end, "daily")


async def send_weekly_report_job(context: ContextTypes.DEFAULT_TYPE):
    # 如果今天是周一
    now = datetime.now(TIME_ZONE)
    if now.weekday() == 0:
        # 上周的报告
        result = get_last_week_range()
    else:
        # 本周的报告
        result = get_current_week_range()
    if result is None:
        raise ValueError("无法获取本周的起止时间")
    start, end = result
    await generate_and_send_report(context, start, end, "weekly")


async def send_monthly_report_job(context: ContextTypes.DEFAULT_TYPE):
    now = datetime.now(TIME_ZONE)
    if now.day == 1:
        # 如果今天是1号，则上个月的报告
        result = get_last_month_range()
    else:
        # 否则本月的报告
        result = get_current_month_range()
    if result is None:
        raise ValueError("无法获取本月的起止时间")
    start, end = result
    await generate_and_send_report(context, start, end, "monthly")


async def send_profit_trend_chart_job(context: ContextTypes.DEFAULT_TYPE):
    # 把起始时间设置为 2025-4-14 12:00:00,并把start变量的时区设置为 TIME_ZONE
    start = "2025-4-14 12:00:00"
    start = datetime.strptime(start, "%Y-%m-%d %H:%M:%S").replace(tzinfo=TIME_ZONE)
    now = datetime.now(TIME_ZONE)
    end = now.replace(hour=11, minute=59, second=59, microsecond=999999)
    await send_profit_trend_chart(context, start, end)


async def send_venue_report_job(context: ContextTypes.DEFAULT_TYPE):
    result = get_last_month_range()
    if result is None:
        raise ValueError("无法获取上月的起止日期")
    start, end = result
    await send_venue_profit_bar_chart(context, start, end)


async def send_person_report_job(context: ContextTypes.DEFAULT_TYPE):
    result = get_last_month_range()
    if result is None:
        raise ValueError("无法获取上月的起止日期")
    start, end = result
    await send_person_profit_bar_chart(context, start, end)
