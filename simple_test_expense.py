#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for expense data functionality
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set UTF-8 encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

def test_basic_functionality():
    """Test basic functionality"""
    try:
        print("Testing expense data manager basic functionality")
        print("=" * 50)
        
        # 导入模块
        from monthlyjob import ExpenseDataManager, MonthlyJobConfig
        from Parser import parse_datetime
        
        # 创建管理器
        manager = ExpenseDataManager()
        
        print(f"Excel文件: {manager.excel_file}")
        print(f"Google Sheet: {manager.google_sheet_name}")
        print(f"切换时间: {manager.switch_time}")
        print()
        
        # 检查Excel文件是否存在
        if os.path.exists(manager.excel_file):
            print(f"Excel文件存在: {manager.excel_file}")
        else:
            print(f"Excel文件不存在: {manager.excel_file}")
        
        # 测试时间解析
        test_time = parse_datetime("2025-08-15 12:00:00")
        print(f"测试时间解析: {test_time}")
        
        # 测试配置
        print("Excel字段映射:")
        for key, value in MonthlyJobConfig.EXCEL_FIELD_MAPPING.items():
            print(f"  {key}: {value}")
        
        print("\nGoogle Sheet字段映射:")
        for key, value in MonthlyJobConfig.GOOGLE_SHEET_FIELD_MAPPING.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_reading():
    """测试Excel读取功能"""
    try:
        print("\n测试Excel读取功能")
        print("=" * 50)
        
        from monthlyjob import ExpenseDataManager
        from Parser import parse_datetime
        
        manager = ExpenseDataManager()
        
        # 测试2025年8月数据
        start_date = parse_datetime("2025-08-01 00:00:00")
        end_date = parse_datetime("2025-08-31 23:59:59")
        
        print(f"查询时间范围: {start_date} 到 {end_date}")
        
        records = manager.get_monthly_expenses(start_date, end_date)
        print(f"获取到 {len(records)} 条记录")
        
        if records:
            print("前3条记录:")
            for i, record in enumerate(records[:3], 1):
                print(f"  {i}. {record.expense_person} - {record.expense_category} - ${record.expense_amount}")
        
        return True
        
    except Exception as e:
        print(f"Excel读取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_job():
    """测试月度工作流"""
    try:
        print("\n测试月度工作流")
        print("=" * 50)
        
        from monthlyjob import manual_complete_monthly_job
        
        result = manual_complete_monthly_job()
        
        print(f"执行结果: {result.get('success', False)}")
        
        if result.get('errors'):
            print("错误信息:")
            for error in result['errors']:
                print(f"  - {error}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"月度工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Expense Data Functionality Test")
    print("=" * 60)
    
    # 测试1：基本功能
    success1 = test_basic_functionality()
    
    # 测试2：Excel读取
    success2 = test_excel_reading()
    
    # 测试3：月度工作流
    success3 = test_monthly_job()
    
    print("\nTest Summary:")
    print(f"Basic functionality: {'PASS' if success1 else 'FAIL'}")
    print(f"Excel reading: {'PASS' if success2 else 'FAIL'}")
    print(f"Monthly workflow: {'PASS' if success3 else 'FAIL'}")

    if success1 and success2 and success3:
        print("\nAll tests passed!")
    else:
        print("\nSome tests failed")

if __name__ == "__main__":
    main()
