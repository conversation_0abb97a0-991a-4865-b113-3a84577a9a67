#!/usr/bin/env python3
"""
Unit tests for data models
"""

import pytest
from datetime import datetime
from data_models import (
    GameRecord, RebateRule, SalaryRule, DataValidator,
    OperationResult, RecordStatus,
    FIELDS_CHINESE, FIELDS_ENGLISH, FIELD_MAPPING
)

class TestGameRecord:
    """Test GameRecord data model"""
    
    def test_create_from_dict(self, sample_game_record):
        """Test creating GameRecord from dictionary"""
        record = GameRecord.from_dict(sample_game_record)
        
        assert record.person == '张三'
        assert record.venue == 'TestVenue'
        assert record.game == 'BJ'
        assert record.principal == 1000
        assert record.profit == 500
        assert record.status == RecordStatus.PENDING
    
    def test_to_dict(self, sample_game_record):
        """Test converting GameRecord to dictionary"""
        record = GameRecord.from_dict(sample_game_record)
        record_dict = record.to_dict()
        
        assert record_dict['person'] == '张三'
        assert record_dict['venue'] == 'TestVenue'
        assert record_dict['profit'] == 500
        assert record_dict['status'] == 'pending'
    
    def test_to_excel_row(self, sample_game_record):
        """Test converting GameRecord to Excel row"""
        record = GameRecord.from_dict(sample_game_record)
        row = record.to_excel_row(include_msg_time=True)
        
        assert len(row) == 12  # msg_time + 11 fields
        assert row[1] == '2025-01-01'  # work_date
        assert row[2] == '张三'  # person
        assert row[3] == 'TestVenue'  # venue
        assert row[11] == '测试记录'  # remark
    
    def test_from_excel_row(self):
        """Test creating GameRecord from Excel row"""
        row = ['2025-01-01', '张三', 'TestVenue', 'BJ', 'TEST001', 1000, 100, 20, 50, 500, '测试']
        msg_time = datetime(2025, 1, 1, 12, 0, 0)
        
        record = GameRecord.from_excel_row(row, msg_time)
        
        assert record.work_date == '2025-01-01'
        assert record.person == '张三'
        assert record.venue == 'TestVenue'
        assert record.principal == 1000
        assert record.profit == 500

class TestRebateRule:
    """Test RebateRule data model"""
    
    def test_create_rebate_rule(self):
        """Test creating RebateRule"""
        rule = RebateRule(venue="TestVenue", person="张三", ratio=0.15)
        
        assert rule.venue == "TestVenue"
        assert rule.person == "张三"
        assert rule.ratio == 0.15
        assert not rule.is_default
    
    def test_to_dict(self):
        """Test converting RebateRule to dictionary"""
        rule = RebateRule(venue="TestVenue", ratio=0.1, is_default=True)
        rule_dict = rule.to_dict()
        
        assert rule_dict['venue'] == "TestVenue"
        assert rule_dict['ratio'] == 0.1
        assert rule_dict['is_default'] is True
        assert 'created_at' in rule_dict
    
    def test_from_dict(self):
        """Test creating RebateRule from dictionary"""
        data = {
            'venue': 'TestVenue',
            'person': '张三',
            'ratio': 0.15,
            'is_default': False
        }
        
        rule = RebateRule.from_dict(data)
        
        assert rule.venue == 'TestVenue'
        assert rule.person == '张三'
        assert rule.ratio == 0.15
        assert not rule.is_default

class TestSalaryRule:
    """Test SalaryRule data model"""
    
    def test_create_salary_rule(self):
        """Test creating SalaryRule"""
        rule = SalaryRule(
            rebate_ratio=0.2,
            game="BJ",
            profit_min=100,
            profit_max=999,
            salary=20
        )
        
        assert rule.rebate_ratio == 0.2
        assert rule.game == "BJ"
        assert rule.profit_min == 100
        assert rule.profit_max == 999
        assert rule.salary == 20
        assert rule.enabled
    
    def test_matches_profit(self):
        """Test profit matching logic"""
        rule = SalaryRule(
            rebate_ratio=0.2,
            game="BJ",
            profit_min=100,
            profit_max=999,
            salary=20
        )
        
        # Test within range
        assert rule.matches_profit(500)
        assert rule.matches_profit(100)  # Min boundary
        assert rule.matches_profit(999)  # Max boundary
        
        # Test outside range
        assert not rule.matches_profit(50)   # Below min
        assert not rule.matches_profit(1000) # Above max
    
    def test_matches_profit_no_limits(self):
        """Test profit matching with no limits"""
        rule = SalaryRule(
            rebate_ratio=0.2,
            game="BJ",
            salary=20
        )
        
        # Should match any profit
        assert rule.matches_profit(-1000)
        assert rule.matches_profit(0)
        assert rule.matches_profit(10000)
    
    def test_matches_profit_disabled(self):
        """Test disabled rule doesn't match"""
        rule = SalaryRule(
            rebate_ratio=0.2,
            game="BJ",
            profit_min=100,
            profit_max=999,
            salary=20,
            enabled=False
        )
        
        assert not rule.matches_profit(500)

class TestDataValidator:
    """Test DataValidator"""
    
    def test_validate_valid_record(self, sample_game_record):
        """Test validating valid game record"""
        record = GameRecord.from_dict(sample_game_record)
        result = DataValidator.validate_game_record(record)
        
        assert result.success
        assert result.message == "验证通过"
    
    def test_validate_invalid_record(self):
        """Test validating invalid game record"""
        invalid_data = {
            'msg_time': datetime.now(),
            'person': '',  # Empty person
            'venue': 'TestVenue',
            'game': '',    # Empty game
            'principal': -100,  # Negative principal
            'profit': 0
        }
        
        record = GameRecord.from_dict(invalid_data)
        result = DataValidator.validate_game_record(record)
        
        assert not result.success
        assert "人员名称不能为空" in result.message
        assert "游戏类型不能为空" in result.message
        assert "本金不能为负数" in result.message
    
    def test_validate_rebate_rule(self):
        """Test validating rebate rule"""
        # Valid rule
        valid_rule = RebateRule(venue="TestVenue", ratio=0.15)
        result = DataValidator.validate_rebate_rule(valid_rule)
        assert result.success
        
        # Invalid rule - empty venue
        invalid_rule = RebateRule(venue="", ratio=0.15)
        result = DataValidator.validate_rebate_rule(invalid_rule)
        assert not result.success
        assert "场子名称不能为空" in result.message
        
        # Invalid rule - ratio out of range
        invalid_rule = RebateRule(venue="TestVenue", ratio=1.5)
        result = DataValidator.validate_rebate_rule(invalid_rule)
        assert not result.success
        assert "输返比例必须在 0-1 之间" in result.message
    
    def test_validate_salary_rule(self):
        """Test validating salary rule"""
        # Valid rule
        valid_rule = SalaryRule(rebate_ratio=0.2, game="BJ", salary=20)
        result = DataValidator.validate_salary_rule(valid_rule)
        assert result.success
        
        # Invalid rule - empty game
        invalid_rule = SalaryRule(rebate_ratio=0.2, game="", salary=20)
        result = DataValidator.validate_salary_rule(invalid_rule)
        assert not result.success
        assert "游戏类型不能为空" in result.message
        
        # Invalid rule - negative salary
        invalid_rule = SalaryRule(rebate_ratio=0.2, game="BJ", salary=-10)
        result = DataValidator.validate_salary_rule(invalid_rule)
        assert not result.success
        assert "工资不能为负数" in result.message

class TestOperationResult:
    """Test OperationResult"""
    
    def test_success_result(self):
        """Test successful operation result"""
        result = OperationResult(success=True, message="操作成功", data={"key": "value"})
        
        assert result.success
        assert result.message == "操作成功"
        assert result.data == {"key": "value"}
        assert result.error_code is None
    
    def test_error_result(self):
        """Test error operation result"""
        result = OperationResult(
            success=False, 
            message="操作失败", 
            error_code="TEST_ERROR"
        )
        
        assert not result.success
        assert result.message == "操作失败"
        assert result.error_code == "TEST_ERROR"
        assert result.data is None
    
    def test_to_dict(self):
        """Test converting result to dictionary"""
        result = OperationResult(success=True, message="测试", data={"test": True})
        result_dict = result.to_dict()
        
        assert result_dict['success'] is True
        assert result_dict['message'] == "测试"
        assert result_dict['data'] == {"test": True}
        assert 'timestamp' in result_dict

class TestConstants:
    """Test constants and mappings"""
    
    def test_field_constants(self):
        """Test field constants"""
        assert len(FIELDS_CHINESE) == len(FIELDS_ENGLISH)
        assert len(FIELDS_CHINESE) == 11
        
        # Check some key fields
        assert "人员" in FIELDS_CHINESE
        assert "场子" in FIELDS_CHINESE
        assert "游戏" in FIELDS_CHINESE
        assert "赢亏" in FIELDS_CHINESE
        
        assert "person" in FIELDS_ENGLISH
        assert "venue" in FIELDS_ENGLISH
        assert "game" in FIELDS_ENGLISH
        assert "profit" in FIELDS_ENGLISH
    
    def test_field_mapping(self):
        """Test field mapping"""
        assert FIELD_MAPPING["人员"] == "person"
        assert FIELD_MAPPING["场子"] == "venue"
        assert FIELD_MAPPING["游戏"] == "game"
        assert FIELD_MAPPING["赢亏"] == "profit"
        
        # Test reverse mapping exists
        from data_models import REVERSE_FIELD_MAPPING
        assert REVERSE_FIELD_MAPPING["person"] == "人员"
        assert REVERSE_FIELD_MAPPING["venue"] == "场子"
