#!/usr/bin/env python3
"""
测试 set_rebate 命令修复
"""

import asyncio
import logging
import sys
import os
import json
import tempfile
from unittest.mock import patch, MagicMock, AsyncMock


def setup_test_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


async def test_security_utils_read_secure_file():
    """测试 SecurityUtils.read_secure_file 方法"""
    logger = setup_test_logging()
    logger.info("测试 SecurityUtils.read_secure_file 方法...")
    
    try:
        sys.path.append('.')
        from security_utils import SecurityUtils
        
        # 创建临时测试文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_file = f.name
            test_content = '{"test": "data", "venue": "LesA", "ratio": 0.2}'
            f.write(test_content)
        
        try:
            # 测试读取文件
            content = SecurityUtils.read_secure_file(test_file)
            logger.info(f"读取内容: {content[:50]}...")
            
            # 验证内容正确
            if content == test_content:
                logger.info("✅ SecurityUtils.read_secure_file 工作正常")
                return True
            else:
                logger.error(f"❌ 内容不匹配: 期望 {test_content}, 实际 {content}")
                return False
                
        finally:
            # 清理临时文件
            if os.path.exists(test_file):
                os.remove(test_file)
                
    except Exception as e:
        logger.error(f"❌ SecurityUtils.read_secure_file 测试失败: {e}")
        return False


async def test_async_file_ops_write_json():
    """测试 AsyncFileManager.write_json_async 方法"""
    logger = setup_test_logging()
    logger.info("测试 AsyncFileManager.write_json_async 方法...")
    
    try:
        sys.path.append('.')
        from async_file_ops import get_file_manager
        
        file_manager = get_file_manager()
        
        # 创建临时文件路径
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            test_file = f.name
        
        # 删除临时文件，让 write_json_async 创建
        os.remove(test_file)
        
        try:
            # 测试数据
            test_data = {
                "LesA": {
                    "默认比例": 0.2
                },
                "默认比例": 0.1
            }
            
            # 测试写入
            logger.info(f"测试写入文件: {test_file}")
            success = await file_manager.write_json_async(test_file, test_data, is_sensitive=True)
            
            if success:
                logger.info("✅ write_json_async 执行成功")
                
                # 验证文件内容
                if os.path.exists(test_file):
                    with open(test_file, 'r', encoding='utf-8') as f:
                        saved_data = json.load(f)
                    
                    if saved_data == test_data:
                        logger.info("✅ 文件内容验证成功")
                        return True
                    else:
                        logger.error(f"❌ 文件内容不匹配: {saved_data}")
                        return False
                else:
                    logger.error("❌ 文件未创建")
                    return False
            else:
                logger.error("❌ write_json_async 执行失败")
                return False
                
        finally:
            # 清理临时文件
            if os.path.exists(test_file):
                os.remove(test_file)
                
    except Exception as e:
        logger.error(f"❌ write_json_async 测试失败: {e}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return False


async def test_rebate_config_manager():
    """测试 RebateConfigManager"""
    logger = setup_test_logging()
    logger.info("测试 RebateConfigManager...")
    
    try:
        sys.path.append('.')
        from config_manager import RebateConfigManager
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_config_file = f.name
        
        # 删除临时文件，让管理器创建
        os.remove(temp_config_file)
        
        try:
            # 创建管理器实例
            manager = RebateConfigManager()
            # 临时替换配置文件路径
            original_config_file = manager.config_file
            manager.config_file = temp_config_file
            
            # 测试数据
            test_config = {
                "LesA": {
                    "默认比例": 0.2
                },
                "默认比例": 0.1
            }
            
            # 测试保存配置
            logger.info("测试保存配置...")
            success = await manager.save_local_config(test_config)
            
            if success:
                logger.info("✅ save_local_config 执行成功")
                
                # 测试加载配置
                logger.info("测试加载配置...")
                loaded_config = await manager.load_local_config()
                
                if loaded_config == test_config:
                    logger.info("✅ 配置保存和加载成功")
                    return True
                else:
                    logger.error(f"❌ 配置不匹配: {loaded_config}")
                    return False
            else:
                logger.error("❌ save_local_config 执行失败")
                return False
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_config_file):
                os.remove(temp_config_file)
                
    except Exception as e:
        logger.error(f"❌ RebateConfigManager 测试失败: {e}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return False


async def test_storage_service_update_rebate():
    """测试 StorageService.update_rebate_ratio 方法"""
    logger = setup_test_logging()
    logger.info("测试 StorageService.update_rebate_ratio 方法...")
    
    try:
        sys.path.append('.')
        from storage_service import StorageService
        from config_manager import RebateConfigManager
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_config_file = f.name
            # 写入初始配置
            initial_config = {"默认比例": 0.1}
            json.dump(initial_config, f, ensure_ascii=False, indent=2)
        
        try:
            # 模拟 Google Sheets 更新成功
            with patch.object(RebateConfigManager, 'update_google_sheet') as mock_sheets:
                mock_sheets.return_value = (True, "更新成功")
                
                # 创建存储服务实例
                storage_service = StorageService()
                # 临时替换配置文件路径
                storage_service.rebate_manager.config_file = temp_config_file
                
                # 测试更新 rebate 比例
                logger.info("测试更新 rebate 比例: LesA = 0.2")
                result = await storage_service.update_rebate_ratio("LesA", None, 0.2)
                
                if result.success:
                    logger.info(f"✅ update_rebate_ratio 执行成功: {result.message}")
                    
                    # 验证配置文件内容
                    with open(temp_config_file, 'r', encoding='utf-8') as f:
                        saved_config = json.load(f)
                    
                    expected_config = {
                        "默认比例": 0.1,
                        "LesA": {
                            "默认比例": 0.2
                        }
                    }
                    
                    if saved_config == expected_config:
                        logger.info("✅ 配置文件内容验证成功")
                        return True
                    else:
                        logger.error(f"❌ 配置文件内容不匹配: {saved_config}")
                        return False
                else:
                    logger.error(f"❌ update_rebate_ratio 执行失败: {result.message}")
                    return False
                    
        finally:
            # 清理临时文件
            if os.path.exists(temp_config_file):
                os.remove(temp_config_file)
                
    except Exception as e:
        logger.error(f"❌ StorageService 测试失败: {e}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return False


async def run_all_tests():
    """运行所有测试"""
    logger = setup_test_logging()
    logger.info("开始运行 set_rebate 修复测试...\n")
    
    tests = [
        ("SecurityUtils.read_secure_file", test_security_utils_read_secure_file),
        ("AsyncFileManager.write_json_async", test_async_file_ops_write_json),
        ("RebateConfigManager", test_rebate_config_manager),
        ("StorageService.update_rebate_ratio", test_storage_service_update_rebate)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            logger.info(f"\n{'='*60}")
            logger.info(f"测试: {test_name}")
            logger.info('='*60)
            
            result = await test_func()
            results.append(result)
            
            status = "通过" if result else "失败"
            logger.info(f"\n{test_name}: {status}")
            
        except Exception as e:
            logger.error(f"{test_name} 测试异常: {e}")
            results.append(False)
    
    logger.info(f"\n{'='*60}")
    logger.info(f"测试结果: {sum(results)}/{len(results)} 通过")
    logger.info('='*60)
    
    if all(results):
        logger.info("\n🎉 所有测试通过!")
        logger.info("\nset_rebate 命令修复成功，现在应该能够正常工作。")
        return True
    else:
        logger.error("\n❌ 部分测试失败，请检查修复。")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
