#!/usr/bin/env python3
"""
Unit tests for message caching functionality
"""

import pytest
import os
import json
import tempfile
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Import the message cache module
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from message_cache import MessageCache, retry_sync_cached_messages


class TestMessageCache:
    """Test message caching functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        # Create temporary files for testing
        self.temp_dir = tempfile.mkdtemp()
        self.cache_file = os.path.join(self.temp_dir, "test_cache.txt")
        self.retry_log_file = os.path.join(self.temp_dir, "test_retry_log.txt")
        
        # Create test cache instance
        self.cache = MessageCache()
        self.cache.cache_file = self.cache_file
        self.cache.retry_log_file = self.retry_log_file
    
    def teardown_method(self):
        """Cleanup test environment"""
        # Clean up temporary files
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)
        if os.path.exists(self.retry_log_file):
            os.remove(self.retry_log_file)
        os.rmdir(self.temp_dir)
    
    def test_add_message(self):
        """Test adding a message to cache"""
        parsed_data = {
            "人员": "测试用户",
            "场子": "测试场所",
            "本金": 1000,
            "输反": 100
        }
        msg_time = "2025-08-08 21:38:59"
        original_message = "测试消息"
        
        # Add message to cache
        self.cache.add_message(parsed_data, msg_time, original_message)
        
        # Verify cache file exists and contains the message
        assert os.path.exists(self.cache_file)
        
        with open(self.cache_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            assert len(lines) == 1
            
            cached_entry = json.loads(lines[0].strip())
            assert cached_entry["msg_time"] == msg_time
            assert cached_entry["parsed_data"] == parsed_data
            assert cached_entry["original_message"] == original_message
            assert cached_entry["retry_count"] == 0
    
    def test_get_pending_messages(self):
        """Test getting pending messages for retry"""
        # Add a message that should be ready for retry
        parsed_data = {"人员": "测试用户", "场子": "测试场所"}
        msg_time = "2025-08-08 21:38:59"
        
        self.cache.add_message(parsed_data, msg_time)
        
        # Get pending messages (should include the message we just added)
        pending = self.cache.get_pending_messages()
        assert len(pending) == 1
        assert pending[0]["parsed_data"] == parsed_data
    
    def test_remove_message(self):
        """Test removing a message from cache"""
        parsed_data = {"人员": "测试用户", "场子": "测试场所"}
        msg_time = "2025-08-08 21:38:59"
        
        # Add message
        self.cache.add_message(parsed_data, msg_time)
        
        # Get the cached message
        cached_messages = self.cache._load_cache()
        assert len(cached_messages) == 1
        
        # Remove the message
        self.cache.remove_message(cached_messages[0])
        
        # Verify it's removed
        remaining_messages = self.cache._load_cache()
        assert len(remaining_messages) == 0
    
    def test_update_retry_info(self):
        """Test updating retry information"""
        parsed_data = {"人员": "测试用户", "场子": "测试场所"}
        msg_time = "2025-08-08 21:38:59"
        
        # Add message
        self.cache.add_message(parsed_data, msg_time)
        
        # Get the cached message
        cached_messages = self.cache._load_cache()
        original_entry = cached_messages[0]
        assert original_entry["retry_count"] == 0
        
        # Update retry info
        self.cache.update_retry_info(original_entry)
        
        # Verify retry count increased
        updated_messages = self.cache._load_cache()
        assert len(updated_messages) == 1
        assert updated_messages[0]["retry_count"] == 1
        assert updated_messages[0]["last_retry"] is not None
    
    def test_get_cache_stats(self):
        """Test getting cache statistics"""
        # Initially should have no cached messages
        stats = self.cache.get_cache_stats()
        assert stats["total_cached"] == 0
        assert stats["pending_retry"] == 0
        
        # Add some messages
        for i in range(3):
            parsed_data = {"人员": f"用户{i}", "场子": "测试场所"}
            msg_time = f"2025-08-08 21:38:{59+i}"
            self.cache.add_message(parsed_data, msg_time)
        
        # Check stats
        stats = self.cache.get_cache_stats()
        assert stats["total_cached"] == 3
        assert stats["pending_retry"] == 3  # All should be pending since they're new


class TestRetrySync:
    """Test retry sync functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.cache_file = os.path.join(self.temp_dir, "test_cache.txt")
        self.retry_log_file = os.path.join(self.temp_dir, "test_retry_log.txt")
    
    def teardown_method(self):
        """Cleanup test environment"""
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)
        if os.path.exists(self.retry_log_file):
            os.remove(self.retry_log_file)
        os.rmdir(self.temp_dir)
    
    @patch('message_cache.message_cache')
    @patch('message_cache.sync_to_google_sheets')
    @patch('message_cache.write_to_excel')
    async def test_retry_sync_success(self, mock_write_excel, mock_sync_sheets, mock_cache):
        """Test successful retry sync"""
        # Mock pending messages
        mock_pending_message = {
            "timestamp": datetime.now().isoformat(),
            "msg_time": "2025-08-08 21:38:59",
            "parsed_data": {"人员": "测试用户", "场子": "测试场所"},
            "original_message": "测试消息",
            "retry_count": 0
        }
        
        mock_cache.get_pending_messages.return_value = [mock_pending_message]
        mock_sync_sheets.return_value = None  # Success (no exception)
        mock_write_excel.return_value = None  # Success (no exception)
        
        # Run retry sync
        result = await retry_sync_cached_messages()
        
        # Verify results
        assert result["success"] == 1
        assert result["failed"] == 0
        assert result["total"] == 1
        
        # Verify methods were called
        mock_sync_sheets.assert_called_once()
        mock_write_excel.assert_called_once()
        mock_cache.remove_message.assert_called_once_with(mock_pending_message)
    
    @patch('message_cache.message_cache')
    @patch('message_cache.sync_to_google_sheets')
    async def test_retry_sync_failure(self, mock_sync_sheets, mock_cache):
        """Test retry sync failure"""
        # Mock pending messages
        mock_pending_message = {
            "timestamp": datetime.now().isoformat(),
            "msg_time": "2025-08-08 21:38:59",
            "parsed_data": {"人员": "测试用户", "场子": "测试场所"},
            "original_message": "测试消息",
            "retry_count": 0
        }
        
        mock_cache.get_pending_messages.return_value = [mock_pending_message]
        mock_sync_sheets.side_effect = Exception("Network error")  # Simulate failure
        
        # Run retry sync
        result = await retry_sync_cached_messages()
        
        # Verify results
        assert result["success"] == 0
        assert result["failed"] == 1
        assert result["total"] == 1
        
        # Verify retry info was updated
        mock_cache.update_retry_info.assert_called_once_with(mock_pending_message)
    
    @patch('message_cache.message_cache')
    async def test_retry_sync_no_pending(self, mock_cache):
        """Test retry sync with no pending messages"""
        mock_cache.get_pending_messages.return_value = []
        
        result = await retry_sync_cached_messages()
        
        assert result["success"] == 0
        assert result["failed"] == 0
        assert result["total"] == 0


if __name__ == "__main__":
    pytest.main([__file__])
