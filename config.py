import os
from zoneinfo import ZoneInfo
from dotenv import load_dotenv
import math
import pytz
# 加载 .env 文件中的变量
load_dotenv()


class Config:
    # 运行环境：可设置为 "prod" 或 "dev"
    ENV = os.getenv("ENV", "dev")   # 可通过环境变量覆盖

    # Telegram Bot Token
    BOT_TOKEN = os.getenv("BOT_TOKEN_PROD") if ENV == "prod" else os.getenv("BOT_TOKEN_DEV")

    # Google Sheet 名称配置
    # 明细记录表格名称
    RECORD_SHEET_NAME = os.getenv("RECORD_SHEET_NAME_PROD") if ENV == "prod" else os.getenv("RECORD_SHEET_NAME_DEV")

    # 工资配置表格名称
    SALARY_CONFIG_SHEET_NAME = os.getenv("SALARY_CONFIG_SHEET_NAME_PROD") if ENV == "prod" else os.getenv("SALARY_CONFIG_SHEET_NAME_DEV")

    # Rebate配置表格名称
    REBATE_CONFIG_SHEET_NAME = os.getenv("REBATE_CONFIG_SHEET_NAME_PROD") if ENV == "prod" else os.getenv("REBATE_CONFIG_SHEET_NAME_DEV")

    # 保持向后兼容性
    GOOGLE_SHEET_NAME = RECORD_SHEET_NAME

    # 群组 Chat ID（转换为整数）
    GROUP_CHAT_ID = int(os.getenv("GROUP_CHAT_ID_PROD")) if ENV == "prod" else int(os.getenv("GROUP_CHAT_ID_DEV"))

    CREDENTIALS_FILE = os.getenv("CREDENTIALS_FILE_PROD") if ENV == "prod" else os.getenv("CREDENTIALS_FILE_DEV")

    # Excel 文件名
    EXCEL_FILE = "group_records_dev.xlsx" if ENV == "dev" else "group_records.xlsx"

    # 别名词典文件路径
    ALIAS_FILE = "aliases.json"
    # 别名词典文件路径（如果有）
    ALIAS_FILE_PATH = os.path.join(os.path.dirname(__file__), ALIAS_FILE)

    # 时区设置
    TIME_ZONE = pytz.timezone(os.getenv("TIME_ZONE"))  # 可通过环境变量覆盖
    print(f"当前时区设置为: {TIME_ZONE}, TIME_ZONE 类型是：{type(TIME_ZONE)}")

    REBATE_FILE = "rebate_config.json"
    SALARY_FILE = "salary_config.json"

    BASE_WAGER = 200

    REBATE_RATIO_1 = 0.1 # 10% 输返

    REBATE_RATIO_2 = 0.2 # 20% 输返

    EXPECTED_WIN_UNIT_REBATE_RATIO_1 = 0.25 #输返10%的情况下，期望值0.26个下注单位

    EXPECTED_WIN_UNIT_REBATE_RATIO_2 = 0.7 #输返20%的情况下，期望值0.7个下注单位

    SALARY_RATIO_WITH_WIN_AT_REBATE_1 = 0.4 # 10% 输返并且赢的情况下，薪水比例

    SALARY_RATIO_WITH_WIN_AT_REBATE_2 = 0.35 # 20% 输返并且赢的情况下，薪水比例

    SALARY_RATIO_WITH_LOSE_AT_REBATE_1 = 0.2 # 10% 输返并且输的情况下，薪水比例

    SALARY_RATIO_WITH_LOSE_AT_REBATE_2 = 0.14 # 20% 输返并且输的情况下，薪水比例

    # 10% 输返并且赢的情况下，工资为
    SALARY_AT_WIN_WITH_REBATE_1 = BASE_WAGER * EXPECTED_WIN_UNIT_REBATE_RATIO_1 * SALARY_RATIO_WITH_WIN_AT_REBATE_1 # 200 * 0.25 * 0.4 = 20
    # 20% 输返并且赢的情况下，工资为
    SALARY_AT_WIN_WITH_REBATE_2 = BASE_WAGER * EXPECTED_WIN_UNIT_REBATE_RATIO_2 * SALARY_RATIO_WITH_WIN_AT_REBATE_2 # 200 * 0.7 * 0.35 = 49
    # 把SALARY_AT_WIN_WITH_REBATE_2取整
    SALARY_AT_WIN_WITH_REBATE_2 = math.ceil(SALARY_AT_WIN_WITH_REBATE_2 / 10) * 10
    # 10% 输返并且输的情况下，工资为
    SALARY_AT_LOSE_WITH_REBATE_1 = BASE_WAGER * EXPECTED_WIN_UNIT_REBATE_RATIO_1 * SALARY_RATIO_WITH_LOSE_AT_REBATE_1 # 200 * 0.25 * 0.2 = 10
    # 20% 输返并且输的情况下，工资为
    SALARY_AT_LOSE_WITH_REBATE_2 = BASE_WAGER * EXPECTED_WIN_UNIT_REBATE_RATIO_2 * SALARY_RATIO_WITH_LOSE_AT_REBATE_2 # 200 * 0.7 * 0.14 = 19.6
    # 把SALARY_AT_LOSE_WITH_REBATE_2取整
    SALARY_AT_LOSE_WITH_REBATE_2 = math.ceil(SALARY_AT_LOSE_WITH_REBATE_2 / 10) * 10
