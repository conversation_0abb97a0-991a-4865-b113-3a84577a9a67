#!/usr/bin/env python3
"""
Main application entry point with monitoring, logging, and health checks
Enhanced version of Group_record.py with production features
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path

# Setup project path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import configuration and setup
from config import Config
from logging_config import setup_logging, get_logger
from monitoring import get_app_monitor, record_counter, record_gauge
from health_check import start_health_check_server
from startup_checker import StartupChecker

# Import main application modules
from Group_record import main as group_record_main
from bot_setup import create_application

logger = get_logger(__name__)

class TelegramBotApplication:
    """Main application class with monitoring and health checks"""
    
    def __init__(self):
        self.running = False
        self.monitor = None
        self.health_server = None
        self.startup_checker = None
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
        
        # 尝试终止主事件循环
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.call_soon_threadsafe(loop.stop)
        except Exception as e:
            logger.error(f"停止事件循环失败: {e}")
    
    async def initialize(self) -> bool:
        """Initialize all application components"""
        try:
            logger.info("🚀 Initializing Telegram Bot Application...")
            
            # Record startup
            record_counter("app.startup.attempts", 1)
            start_time = datetime.now()
            
            # Run startup checks
            logger.info("🔍 Running startup checks...")
            self.startup_checker = StartupChecker()
            startup_success = await self.startup_checker.run_all_checks()
            
            if not startup_success:
                logger.error("❌ Startup checks failed")
                record_counter("app.startup.failures", 1)
                return False
            
            logger.info("✅ Startup checks completed successfully")
            
            # Initialize monitoring
            logger.info("📊 Starting monitoring...")
            self.monitor = get_app_monitor()
            await self.monitor.start_monitoring()
            
            # Start health check server
            health_port = getattr(Config, 'HEALTH_CHECK_PORT', 8888)
            logger.info(f"🏥 Starting health check server on port {health_port}...")
            self.health_server = await start_health_check_server("127.0.0.1", health_port)
            
            # Record successful initialization
            init_duration = (datetime.now() - start_time).total_seconds()
            record_gauge("app.initialization.duration_seconds", init_duration)
            record_counter("app.startup.successes", 1)
            
            logger.info("🎉 Application initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Application initialization failed: {e}")
            record_counter("app.startup.failures", 1)
            return False
    
    async def run(self):
        """Run the main application"""
        try:
            # Initialize application
            if not await self.initialize():
                logger.error("Failed to initialize application")
                return False
            
            self.running = True
            record_gauge("app.status", 1.0)  # Running
            
            logger.info("▶️ Starting main application loop...")
            
            # Start the main Group_record functionality
            # Note: This would need to be adapted to work with the async context
            main_task = asyncio.create_task(self._run_main_application())
            
            # Monitor application health
            monitor_task = asyncio.create_task(self._monitor_application())
            
            # Wait for shutdown signal or task completion
            try:
                await asyncio.gather(main_task, monitor_task)
            except asyncio.CancelledError:
                logger.info("Application tasks cancelled")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Application runtime error: {e}")
            record_counter("app.runtime.errors", 1)
            return False
        finally:
            await self.shutdown()
    
    async def _run_main_application(self):
        """Run the main application logic"""
        try:
            # Import necessary modules
            from Group_record import load_or_create_excel
            from commander import monthly_salary_check_command_handler
            from telegram.ext import CommandHandler

            # Load or create excel file
            load_or_create_excel()

            # Setup bot using shared module
            app = create_application()

            logger.info("🤖 Telegram Bot started successfully")
            print("✅ Bot 已启动，监听中...")

            # Run the bot
            # 创建一个任务来运行轮询，以便我们可以取消它
            polling_task = asyncio.create_task(
                app.run_polling(
                    drop_pending_updates=True,
                    allowed_updates=None
                )
            )
            
            # 等待终止信号或轮询任务完成
            while self.running and not polling_task.done():
                await asyncio.sleep(0.5)
                
            # 如果收到终止信号，取消轮询任务
            if not self.running and not polling_task.done():
                logger.info("收到终止信号，正在停止 Telegram Bot...")
                polling_task.cancel()
                try:
                    await polling_task
                except asyncio.CancelledError:
                    logger.info("Telegram Bot 轮询已取消")

        except Exception as e:
            logger.error(f"Main application error: {e}")
            record_counter("app.main.errors", 1)
            raise
    
    async def _monitor_application(self):
        """Monitor application health and performance"""
        try:
            while self.running:
                # 检查事件循环是否仍在运行
                try:
                    loop = asyncio.get_event_loop()
                    if not loop.is_running():
                        logger.info("事件循环已停止，监控退出")
                        break
                except Exception:
                    pass
                # Update application metrics
                record_gauge("app.threads.count", len(asyncio.all_tasks()))
                
                # Check memory usage
                import psutil
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                record_gauge("app.memory.usage_mb", memory_mb)
                
                # Log periodic status
                if datetime.now().minute % 10 == 0:  # Every 10 minutes
                    logger.info(f"📊 App Status: Memory={memory_mb:.1f}MB, Tasks={len(asyncio.all_tasks())}")
                
                await asyncio.sleep(60)  # Monitor every minute
                
        except Exception as e:
            logger.error(f"Monitoring error: {e}")
    
    async def shutdown(self):
        """Graceful shutdown"""
        try:
            logger.info("🛑 Starting graceful shutdown...")
            record_counter("app.shutdown.attempts", 1)
            
            self.running = False
            record_gauge("app.status", 0.0)  # Stopped
            
            # Stop monitoring
            if self.monitor:
                await self.monitor.stop_monitoring()
                logger.info("✅ Monitoring stopped")
            
            # Stop health check server
            if self.health_server:
                # Health server cleanup would go here
                logger.info("✅ Health check server stopped")
            
            # Cancel all remaining tasks
            current_task = asyncio.current_task()
            tasks = [task for task in asyncio.all_tasks() 
                    if not task.done() and task is not current_task]
            if tasks:
                logger.info(f"🔄 Cancelling {len(tasks)} remaining tasks...")
                for task in tasks:
                    task.cancel()
                
                # Wait for tasks to complete with timeout
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*tasks, return_exceptions=True),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    logger.warning("⚠️ Some tasks did not complete within timeout")
            
            record_counter("app.shutdown.successes", 1)
            logger.info("✅ Graceful shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ Shutdown error: {e}")
            record_counter("app.shutdown.failures", 1)

async def main():
    """Main entry point"""
    # 设置全局信号处理
    import signal
    
    def global_signal_handler(signum, frame):
        logger.info(f"全局信号处理器: 收到信号 {signum}")
        # 尝试终止主事件循环
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                logger.info("正在停止事件循环...")
                loop.call_soon_threadsafe(loop.stop)
        except Exception as e:
            logger.error(f"停止事件循环失败: {e}")
    
    signal.signal(signal.SIGINT, global_signal_handler)
    signal.signal(signal.SIGTERM, global_signal_handler)
    
    try:
        # Setup logging first
        setup_logging(
            console_output=True,
            file_output=True,
            json_output=getattr(Config, 'JSON_LOGGING', False),
            telegram_logging=getattr(Config, 'TELEGRAM_LOGGING', False)
        )
        
        logger.info("=" * 60)
        logger.info("🤖 Telegram Bot Group Message Grabber")
        logger.info(f"📅 Started at: {datetime.now().isoformat()}")
        logger.info(f"🌍 Environment: {getattr(Config, 'ENV', 'development')}")
        logger.info(f"🐍 Python: {sys.version}")
        logger.info("=" * 60)
        
        # Create and run application
        app = TelegramBotApplication()
        success = await app.run()
        
        if success:
            logger.info("🎉 Application completed successfully")
        else:
            logger.error("💥 Application failed")
            
    except KeyboardInterrupt:
        logger.info("🛑 Application interrupted by user")
    except Exception as e:
        logger.error(f"💥 Unhandled application error: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # 确保所有资源都被正确释放
        logger.info("👋 Application shutting down...")
        # 不再调用 sys.exit()，让程序自然退出

if __name__ == "__main__":
    # Check if we should run the enhanced version or fall back to original
    if len(sys.argv) > 1 and sys.argv[1] == "--original":
        # Run original Group_record.py
        logger.info("Running original Group_record.py...")
        asyncio.run(group_record_main())
    else:
        # Run enhanced version with monitoring
        asyncio.run(main())
