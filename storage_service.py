#!/usr/bin/env python3
"""
存储服务模块
整合所有存储相关功能的高级接口
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from config import Config
from data_models import GameRecord, OperationResult, RecordStatus, DataValidator
from async_file_ops import get_file_manager, get_excel_processor
from google_sheets_pool import get_sheets_manager, GoogleSheetsManager
from config_manager import get_rebate_manager, get_salary_manager
from error_handling import (
    with_retry, with_error_handling, get_error_handler, 
    NETWORK_RETRY_CONFIG, FILE_RETRY_CONFIG, GOOGLE_API_RETRY_CONFIG,
    StorageError, NetworkError, FileOperationError, GoogleAPIError
)
from security_utils import InputValidator

logger = logging.getLogger(__name__)

class StorageService:
    """统一的存储服务"""
    
    def __init__(self):
        self.file_manager = get_file_manager()
        self.excel_processor = get_excel_processor()
        self.rebate_manager = get_rebate_manager()
        self.salary_manager = get_salary_manager()
        self.error_handler = get_error_handler()
        
        # 配置文件路径
        self.excel_file = Config.EXCEL_FILE
        self.google_sheet_name = Config.GOOGLE_SHEET_NAME
        
        # 初始化标志
        self._initialized = False
    
    async def initialize(self) -> bool:
        """初始化存储服务"""
        try:
            if self._initialized:
                return True
            
            logger.info("初始化存储服务...")
            
            # 确保 Excel 文件存在
            await self._ensure_excel_file()
            
            # 初始化 Google Sheets 连接
            sheets_manager: GoogleSheetsManager = await get_sheets_manager()
            
            self._initialized = True
            logger.info("存储服务初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"存储服务初始化失败: {e}")
            return False
    
    @with_error_handling(context="ensure_excel_file")
    @with_retry(FILE_RETRY_CONFIG)
    async def _ensure_excel_file(self):
        """确保 Excel 文件存在"""
        try:
            import os
            if not os.path.exists(self.excel_file):
                from alias_manager import get_all_field_names
                field_names = get_all_field_names()
                # 确保字段顺序一致，使用工作日期而不是日期
                ordered_fields = []
                for field in ["工作日期", "人员", "场子", "游戏", "卡号", "起始本金", "点码", "工资", "输反", "盈利", "备注"]:
                    if field in field_names:
                        ordered_fields.append(field)
                headers = ["消息时间"] + ordered_fields
                success = await self.file_manager.create_excel_async(self.excel_file, headers)
                if not success:
                    raise FileOperationError(f"创建 Excel 文件失败: {self.excel_file}")
                logger.info(f"Excel 文件创建成功: {self.excel_file}")
        except Exception as e:
            raise FileOperationError(f"确保 Excel 文件存在失败: {e}")
    
    @with_error_handling(context="write_record")
    async def write_record(self, data: Dict[str, Any], msg_time_str: str) -> OperationResult:
        """写入记录到存储系统"""
        try:
            logger.info(f"开始写入记录: 用户={data.get('人员', 'Unknown')}, 场子={data.get('场子', 'Unknown')}")

            if not self._initialized:
                logger.info("存储服务未初始化，正在初始化...")
                await self.initialize()
                if not self._initialized:
                    error_msg = "存储服务初始化失败，无法处理记录"
                    logger.error(error_msg)
                    return OperationResult(success=False, message=error_msg)
                else:
                    logger.info("存储服务初始化成功，继续处理记录")
            
            # 创建记录对象
            record = GameRecord.from_dict(data)
            if Config.ENV == "dev":
                print(f"调试模式：尝试写入记录，数据={record}, 时间={msg_time_str}")
            record.msg_time = datetime.fromisoformat(msg_time_str.replace(' ', 'T'))
            
            # 验证记录
            validation_result = DataValidator.validate_game_record(record)
            if not validation_result.success:
                return validation_result
            
            # 优先写入 Google Sheets
            logger.info("开始写入 Google Sheets...")
            sheets_success = await self._write_to_google_sheets(record, msg_time_str)
            logger.info(f"Google Sheets 写入结果: {'成功' if sheets_success else '失败'}")
            
            # 只有Google Sheets写入成功后才写入Excel
            excel_success = False
            if sheets_success:
                logger.info("Google Sheets 写入成功，开始写入 Excel 文件...")
                excel_success = await self._write_to_excel(record, msg_time_str)
                logger.info(f"Excel 写入结果: {'成功' if excel_success else '失败'}")
            
            if sheets_success and excel_success:
                record.status = RecordStatus.SYNCED
                return OperationResult(success=True, message="记录写入成功（Google Sheets + Excel）", data=record.to_dict())
            elif sheets_success:
                # Google Sheets成功但Excel失败，这种情况比较少见
                record.status = RecordStatus.SYNCED  # Google Sheets是主要存储
                return OperationResult(success=True, message="记录已写入 Google Sheets，但本地 Excel 写入失败", data=record.to_dict())
            else:
                # Google Sheets写入失败，不写入Excel，将消息缓存以便重试
                record.status = RecordStatus.PENDING
                return OperationResult(success=False, message="Google Sheets 写入失败，记录已缓存等待重试", data=record.to_dict())
                
        except Exception as e:
            self.error_handler.log_error(e, "write_record")
            return OperationResult(success=False, message=f"写入记录失败: {e}")
    
    @with_retry(FILE_RETRY_CONFIG)
    async def _write_to_excel(self, record: GameRecord, msg_time_str: str) -> bool:
        """写入记录到 Excel"""
        try:
            # 使用 to_excel_row_dict() 获取正确的 Excel 行数据
            row_data = record.to_excel_row_dict(include_msg_time=True)
            if Config.ENV == "dev":
                print(f"调试模式：展示 Excel 行数据，数据={row_data}, 时间={msg_time_str}")
            success = await self.excel_processor.write_record_async(
                self.excel_file, row_data, msg_time_str
            )
            if success:
                logger.debug(f"Excel 写入成功: {msg_time_str}")
                return success
            else:
                logger.error(f"Excel 写入失败: {msg_time_str}")
                return False

        except Exception as e:
            raise FileOperationError(f"Excel 写入失败: {e}")
    
    @with_retry(GOOGLE_API_RETRY_CONFIG)
    async def _write_to_google_sheets(self, record: GameRecord, msg_time_str: str) -> bool:
        """写入记录到 Google Sheets"""
        try:
            logger.info("获取 Google Sheets 连接管理器...")
            sheets_manager: GoogleSheetsManager = await get_sheets_manager()

            # 记录连接池状态（通过公开接口）
            try:
                pool = getattr(sheets_manager, 'pool', None)
                if pool and hasattr(pool, 'get_stats'):
                    pool_stats = pool.get_stats()
                    logger.info(
                        "连接池状态: 活跃=%s, 池内闲置=%s, 最大=%s, 统计=%s, 已初始化=%s",
                        pool_stats.get('active_connections'),
                        pool_stats.get('pool_size'),
                        pool_stats.get('max_connections'),
                        pool_stats.get('stats'),
                        pool_stats.get('initialized'),
                    )
            except Exception as e:
                logger.debug(f"获取连接池状态失败: {e}")

            logger.info(f"准备写入 Google Sheets: {self.google_sheet_name}")
            row_data = record.to_excel_row(include_msg_time=True)
            logger.debug(f"写入数据: {row_data}")

            await sheets_manager.append_row(self.google_sheet_name, row_data)
            logger.info(f"Google Sheets 写入成功: {msg_time_str}")
            return True
        except Exception as e:
            # 检查是否是网络相关错误
            error_msg = str(e).lower()
            error_type = type(e).__name__.lower()

            # 检查常见的网络错误模式
            network_keywords = ['timeout', 'connection', 'network', 'readerror', 'httpcore', 'connecttimeout', 'readtimeout']
            is_network_error = any(keyword in error_msg for keyword in network_keywords) or any(keyword in error_type for keyword in network_keywords)

            if is_network_error:
                logger.warning(f"Google Sheets 网络连接失败 ({type(e).__name__}): {e}")
                # 记录详细的网络错误信息用于调试
                logger.debug(f"网络错误详情 - 错误类型: {type(e).__name__}, 错误消息: {str(e)}")
                raise NetworkError(f"Google Sheets 网络连接失败: {e}")
            else:
                logger.error(f"Google Sheets API 错误 ({type(e).__name__}): {e}")
                raise GoogleAPIError(f"Google Sheets 写入失败: {e}")
    
    @with_error_handling(context="load_records")
    async def load_records(self, start_time: Optional[datetime] = None, 
                          end_time: Optional[datetime] = None, full_load: bool = False, 
                          exclude_zero: bool = True) -> List[Dict[str, Any]]:
        """加载记录"""
        try:
            if not self._initialized:
                if Config.ENV == "dev":
                    print(f"storage_service.load_records : 未初始化，开始初始化")
                await self.initialize()
            
            records = await self.excel_processor.load_data_async(
                self.excel_file, start_time, end_time, full_load, exclude_zero
            )
            if Config.ENV == "dev":
                print(f"storage_service.load_records : start_time={start_time}, end_time={end_time}, full_load={full_load}, exclude_zero={exclude_zero}, records: {records}")
            logger.info(f"加载记录完成，共 {len(records)} 条")
            return records
            
        except Exception as e:
            self.error_handler.log_error(e, "load_records")
            return []
    
    @with_error_handling(context="search_records")
    async def search_records(self, keywords: List[str], parsed_dates: List[str] = None) -> List[Dict[str, Any]]:
        """搜索记录"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # 加载所有记录
            all_records = await self.load_records(full_load=True, exclude_zero=False)
            
            matches = []
            for record in all_records:
                # 关键词匹配
                row_strs = [str(v).lower() for v in record.values() if v is not None]
                
                all_keywords_match = all(
                    any(keyword.lower() in cell for cell in row_strs)
                    for keyword in keywords
                )
                
                # 日期匹配
                date_match = True
                if parsed_dates:
                    date_cells = [
                        str(record.get("msg_time", "")).lower(),
                        str(record.get("work_date", "")).lower()
                    ]
                    date_match = any(date_str in cell for date_str in parsed_dates for cell in date_cells)
                
                if all_keywords_match and date_match:
                    matches.append(record)
            
            logger.info(f"搜索完成，找到 {len(matches)} 条匹配记录")
            return matches
            
        except Exception as e:
            self.error_handler.log_error(e, "search_records")
            return []
    
    # Rebate 配置相关方法
    @with_error_handling(context="load_rebate_config")
    async def load_rebate_config(self) -> Dict[str, Any]:
        """加载 rebate 配置"""
        return await self.rebate_manager.load_local_config()
    
    @with_error_handling(context="save_rebate_config")
    async def save_rebate_config(self, config: Dict[str, Any]) -> bool:
        """保存 rebate 配置"""
        return await self.rebate_manager.save_local_config(config)
    
    @with_error_handling(context="sync_rebate_from_google")
    async def sync_rebate_from_google(self) -> OperationResult:
        """从 Google Sheets 同步 rebate 配置"""
        try:
            config = await self.rebate_manager.load_from_google_sheet()
            success = await self.rebate_manager.save_local_config(config)
            
            if success:
                return OperationResult(success=True, message="Rebate 配置同步成功", data=config)
            else:
                return OperationResult(success=False, message="Rebate 配置保存失败")
                
        except Exception as e:
            self.error_handler.log_error(e, "sync_rebate_from_google")
            return OperationResult(success=False, message=f"同步失败: {e}")
    
    @with_error_handling(context="update_rebate_ratio")
    async def update_rebate_ratio(self, venue: str, person: Optional[str] = None, 
                                 ratio: float = None) -> OperationResult:
        """更新 rebate 比例"""
        try:
            # 输入验证
            if venue:
                is_valid, error = InputValidator.validate_field('场子', venue)
                if not is_valid:
                    return OperationResult(success=False, message=f"场子名称无效: {error}")
            
            if person:
                is_valid, error = InputValidator.validate_field('人员', person)
                if not is_valid:
                    return OperationResult(success=False, message=f"人员名称无效: {error}")
            
            # 更新 Google Sheets
            sheets_success, sheets_message = await self.rebate_manager.update_google_sheet(venue, person, ratio)
            
            if not sheets_success:
                return OperationResult(success=False, message=f"Google Sheets 更新失败: {sheets_message}")
            
            # 更新本地配置
            config = await self.rebate_manager.load_local_config()
            
            if venue not in config or not isinstance(config[venue], dict):
                config[venue] = {}
            
            if person:
                config[venue][person] = ratio
                setting_desc = f"{venue} / {person} 的输返比例"
            else:
                config[venue]["默认比例"] = ratio
                setting_desc = f"{venue} 的默认输返比例"
            
            logger.info(f"开始保存本地 rebate 配置: {venue}/{person} = {ratio}")
            local_success = await self.rebate_manager.save_local_config(config)

            if local_success:
                # 刷新配置缓存以立即生效
                try:
                    from config_cache import config_cache
                    await config_cache.refresh_configs(force_google_sheet=False)
                    logger.info(f"配置缓存已刷新: {venue}/{person} = {ratio}")
                except Exception as cache_error:
                    logger.warning(f"刷新配置缓存失败，错误信息: {cache_error}")
                    return OperationResult(success=False, message="Rebate 在远程文件和本地文件更新成功，但配置缓存刷新失败...请手动执行/refresh_configs 命令以刷新配置缓存")
                
                logger.info(f"Rebate 配置更新完全成功: {venue}/{person} = {ratio}")
                return OperationResult(
                    success=True,
                    message=f"✅ {setting_desc} 设置成功：{ratio:.2%}",
                    data={'venue': venue, 'person': person, 'ratio': ratio}
                )
            else:
                logger.error(f"本地 rebate 配置保存失败: {venue}/{person} = {ratio}")
                return OperationResult(
                    success=False,
                    message="Google Sheets 更新成功，但本地配置保存失败"
                )
                
        except Exception as e:
            self.error_handler.log_error(e, "update_rebate_ratio")
            return OperationResult(success=False, message=f"更新失败: {e}")
    
    def get_rebate_ratio(self, venue: str, person: str) -> float:
        """获取 rebate 比例（同步方法，用于兼容现有代码）"""
        try:
            # 这里需要同步调用，为了兼容性
            import asyncio
            loop = asyncio.get_event_loop()
            config = loop.run_until_complete(self.rebate_manager.load_local_config())
            return self.rebate_manager.get_ratio(config, venue, person)
        except Exception as e:
            logger.error(f"获取 rebate 比例失败: {e}")
            return 0.1
    
    # Salary 配置相关方法
    @with_error_handling(context="load_salary_config")
    async def load_salary_config(self) -> Dict[str, Any]:
        """加载 salary 配置"""
        return await self.salary_manager.load_local_config()
    
    @with_error_handling(context="save_salary_config")
    async def save_salary_config(self, config: Dict[str, Any]) -> bool:
        """保存 salary 配置"""
        return await self.salary_manager.save_local_config(config)
    
    @with_error_handling(context="sync_salary_from_google")
    async def sync_salary_from_google(self) -> OperationResult:
        """从 Google Sheets 同步 salary 配置"""
        try:
            config = await self.salary_manager.load_from_google_sheet()
            success = await self.salary_manager.save_local_config(config)
            
            if success:
                return OperationResult(success=True, message="Salary 配置同步成功", data=config)
            else:
                return OperationResult(success=False, message="Salary 配置保存失败")
                
        except Exception as e:
            self.error_handler.log_error(e, "sync_salary_from_google")
            return OperationResult(success=False, message=f"同步失败: {e}")
    
    @with_error_handling(context="update_salary_rule")
    async def update_salary_rule(self, rebate: float, game: str, profit_min: Optional[int] = None,
                                profit_max: Optional[int] = None, salary: int = None) -> OperationResult:
        """更新 salary 规则"""
        try:
            # 输入验证
            if not (0 <= rebate <= 1):
                return OperationResult(success=False, message="rebate 必须在 0-1 之间")
            
            if game:
                is_valid, error = InputValidator.validate_field('游戏', game)
                if not is_valid:
                    return OperationResult(success=False, message=f"游戏名称无效: {error}")
            
            if salary is not None and salary < 0:
                return OperationResult(success=False, message="工资不能为负数")
            
            # 更新 Google Sheets
            sheets_success, sheets_message = await self.salary_manager.update_google_sheet(
                rebate, game, profit_min, profit_max, salary
            )
            
            if not sheets_success:
                return OperationResult(success=False, message=f"Google Sheets 更新失败: {sheets_message}")
            
            # 重新从 Google Sheets 加载配置
            sync_result = await self.sync_salary_from_google()
            
            if sync_result.success:
                # 刷新配置缓存以立即生效
                try:
                    from config_cache import config_cache
                    await config_cache.refresh_configs(force_google_sheet=False)
                    logger.info(f"Salary 配置缓存已刷新: {rebate}/{game}")
                except Exception as cache_error:
                    logger.warning(f"刷新 Salary 配置缓存失败，但不影响主要功能: {cache_error}")
                    return OperationResult(success=True, message="Rebate 在远程文件和本地文件更新成功，但配置缓存刷新失败...请手动执行/refresh_configs 命令以刷新配置缓存")
                # 构建描述信息
                range_desc = ""
                if profit_min is not None and profit_max is not None:
                    range_desc = f"{profit_min}~{profit_max}"
                elif profit_min is not None:
                    range_desc = f"{profit_min} 以上"
                elif profit_max is not None:
                    range_desc = f"{profit_max} 以下"
                else:
                    range_desc = "无限制"
                
                return OperationResult(
                    success=True,
                    message=f"✅ 工资规则设置成功：{rebate:.1%} {game} {range_desc} 工资{salary}",
                    data={'rebate': rebate, 'game': game, 'profit_min': profit_min, 'profit_max': profit_max, 'salary': salary}
                )
            else:
                return OperationResult(
                    success=False,
                    message="Google Sheets 更新成功，但本地配置同步失败"
                )
                
        except Exception as e:
            self.error_handler.log_error(e, "update_salary_rule")
            return OperationResult(success=False, message=f"更新失败: {e}")
    
    def get_salary_for_profit(self, rebate_ratio: float, game: str, profit: int) -> Optional[int]:
        """获取指定盈利的工资（同步方法，用于兼容现有代码）"""
        try:
            # 这里需要同步调用，为了兼容性
            import asyncio
            loop = asyncio.get_event_loop()
            config = loop.run_until_complete(self.salary_manager.load_local_config())
            return self.salary_manager.find_salary_rule(config, rebate_ratio, game, profit)
        except Exception as e:
            logger.error(f"获取工资失败: {e}")
            return None
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            'initialized': self._initialized,
            'error_stats': self.error_handler.get_error_stats(),
            'excel_file': self.excel_file,
            'google_sheet_name': self.google_sheet_name
        }


# 全局存储服务实例
_storage_service = None

async def get_storage_service() -> StorageService:
    """获取全局存储服务实例"""
    global _storage_service

    if _storage_service is None:
        _storage_service = StorageService()
        try:
            await _storage_service.initialize()
            logger.info("存储服务初始化成功")
        except Exception as e:
            logger.error(f"存储服务初始化失败: {e}")
            # 重置为 None，以便下次重试
            _storage_service = None
            raise StorageError(f"存储服务初始化失败: {e}")

    # 验证服务是否正常工作
    if not _storage_service._initialized:
        logger.warning("存储服务未正确初始化，尝试重新初始化")
        try:
            await _storage_service.initialize()
            logger.info("存储服务重新初始化成功")
        except Exception as e:
            logger.error(f"存储服务重新初始化失败: {e}")
            # 重置为 None，以便下次重试
            _storage_service = None
            raise StorageError(f"存储服务重新初始化失败: {e}")

    return _storage_service

def get_storage_service_sync() -> StorageService:
    """获取全局存储服务实例（同步版本，用于兼容现有代码）"""
    global _storage_service
    
    if _storage_service is None:
        _storage_service = StorageService()
        # 注意：这里不能调用 initialize()，因为它是异步的
        # 需要在适当的地方调用 initialize()
    
    return _storage_service
