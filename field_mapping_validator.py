#!/usr/bin/env python3
"""
字段映射验证器
确保aliases.json中的字段映射与data_models.py中的字段定义保持一致
"""

import json
import logging
from typing import Dict, List, Tuple, Set
from pathlib import Path

from data_models import FIELDS_CHINESE
from config import Config

logger = logging.getLogger(__name__)

class FieldMappingValidator:
    """字段映射验证器"""

    def __init__(self):
        self.alias_file = Config.ALIAS_FILE
        self.expected_fields = set(FIELDS_CHINESE)

    def load_aliases(self) -> Dict:
        """加载别名配置"""
        try:
            with open(self.alias_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"别名文件不存在: {self.alias_file}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"别名文件格式错误: {e}")
            return {}
        except Exception as e:
            logger.error(f"加载别名文件失败: {e}")
            return {}

    def validate_field_mappings(self) -> Tu<PERSON>[bool, List[str]]:
        """
        验证字段映射是否一致

        Returns:
            Tuple[bool, List[str]]: (是否验证通过, 错误信息列表)
        """
        errors = []

        # 加载别名配置
        aliases = self.load_aliases()
        if not aliases:
            errors.append(f"无法加载别名配置文件: {self.alias_file}")
            return False, errors

        # 获取fields部分
        field_aliases = aliases.get("fields", {})
        if not field_aliases:
            errors.append("别名配置中没有找到 'fields' 部分")
            return False, errors

        # 检查别名中定义的字段
        alias_fields = set(field_aliases.keys())

        # 检查缺失的字段
        missing_fields = self.expected_fields - alias_fields
        if missing_fields:
            errors.append(f"别名配置中缺少以下字段: {', '.join(sorted(missing_fields))}")

        # 检查多余的字段
        extra_fields = alias_fields - self.expected_fields
        if extra_fields:
            errors.append(f"别名配置中包含未知字段: {', '.join(sorted(extra_fields))}")

        # 检查每个字段的别名配置
        for field in self.expected_fields:
            if field in field_aliases:
                aliases_list = field_aliases[field]
                if not isinstance(aliases_list, list):
                    errors.append(f"字段 '{field}' 的别名配置应该是列表格式")
                    continue

                # 检查字段本身是否在别名列表中
                if field not in aliases_list:
                    errors.append(f"字段 '{field}' 的别名列表中应该包含字段本身")

                # 检查别名是否为空
                if not aliases_list:
                    errors.append(f"字段 '{field}' 的别名列表不能为空")

        return len(errors) == 0, errors

    def suggest_fixes(self) -> List[str]:
        """
        提供修复建议

        Returns:
            List[str]: 修复建议列表
        """
        is_valid, errors = self.validate_field_mappings()
        if is_valid:
            return ["字段映射验证通过，无需修复"]

        suggestions = []
        aliases = self.load_aliases()
        field_aliases = aliases.get("fields", {})
        alias_fields = set(field_aliases.keys())

        # 为缺失的字段提供建议
        missing_fields = self.expected_fields - alias_fields
        for field in missing_fields:
            suggestions.append(f"添加字段 '{field}' 的别名配置: \"{field}\": [\"{field}\"]")

        # 为多余的字段提供建议
        extra_fields = alias_fields - self.expected_fields
        for field in extra_fields:
            suggestions.append(f"移除未知字段 '{field}' 或检查是否应该重命名")

        return suggestions

    def generate_correct_aliases_template(self) -> Dict:
        """
        生成正确的别名配置模板

        Returns:
            Dict: 正确的别名配置
        """
        aliases = self.load_aliases()
        correct_fields = {}

        for field in FIELDS_CHINESE:
            if field in aliases.get("fields", {}):
                # 保留现有的别名配置，但确保字段本身在列表中
                existing_aliases = aliases["fields"][field]
                if isinstance(existing_aliases, list):
                    # 确保字段本身在列表的第一位
                    if field in existing_aliases:
                        existing_aliases.remove(field)
                    correct_fields[field] = [field] + existing_aliases
                else:
                    correct_fields[field] = [field]
            else:
                # 为新字段创建基本别名配置
                correct_fields[field] = [field]

        return {
            "fields": correct_fields,
            "games": aliases.get("games", {}),
            "persons": aliases.get("persons", {}),
            "venues": aliases.get("venues", {})
        }

    def auto_fix_aliases(self) -> Tuple[bool, str]:
        """
        自动修复别名配置

        Returns:
            Tuple[bool, str]: (是否修复成功, 结果信息)
        """
        try:
            # 备份原文件
            backup_file = f"{self.alias_file}.backup"
            if Path(self.alias_file).exists():
                import shutil
                shutil.copy2(self.alias_file, backup_file)
                logger.info(f"已备份原文件到: {backup_file}")

            # 生成正确的配置
            correct_config = self.generate_correct_aliases_template()

            # 保存修复后的配置
            with open(self.alias_file, 'w', encoding='utf-8') as f:
                json.dump(correct_config, f, ensure_ascii=False, indent=2)

            # 验证修复结果
            is_valid, errors = self.validate_field_mappings()
            if is_valid:
                return True, f"字段映射已自动修复，备份文件: {backup_file}"
            else:
                return False, f"自动修复失败，错误: {'; '.join(errors)}"

        except Exception as e:
            logger.error(f"自动修复失败: {e}")
            return False, f"自动修复失败: {str(e)}"


def validate_field_mappings() -> Tuple[bool, List[str], List[str]]:
    """
    便捷函数：验证字段映射

    Returns:
        Tuple[bool, List[str], List[str]]: (是否验证通过, 错误信息, 修复建议)
    """
    validator = FieldMappingValidator()
    is_valid, errors = validator.validate_field_mappings()
    suggestions = validator.suggest_fixes() if not is_valid else []
    return is_valid, errors, suggestions


def auto_fix_field_mappings() -> Tuple[bool, str]:
    """
    便捷函数：自动修复字段映射

    Returns:
        Tuple[bool, str]: (是否修复成功, 结果信息)
    """
    validator = FieldMappingValidator()
    return validator.auto_fix_aliases()


if __name__ == "__main__":
    # 测试验证器
    logging.basicConfig(level=logging.INFO)

    print("=== Field Mapping Validation ===")
    is_valid, errors, suggestions = validate_field_mappings()

    if is_valid:
        print("[OK] Field mapping validation passed")
    else:
        print("[ERROR] Field mapping validation failed")
        print("\nErrors:")
        for error in errors:
            print(f"  - {error}")

        print("\nSuggestions:")
        for suggestion in suggestions:
            print(f"  - {suggestion}")

        # 询问是否自动修复
        response = input("\nAuto-fix issues? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            success, message = auto_fix_field_mappings()
            if success:
                print(f"[OK] {message}")
            else:
                print(f"[ERROR] {message}")