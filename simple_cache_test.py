#!/usr/bin/env python3
"""
Simple test to verify message caching works
"""

def test_cache_functionality():
    """Test basic cache functionality"""
    print("Testing message caching functionality...")
    
    try:
        from message_cache import message_cache
        
        # Test data
        parsed_data = {
            "人员": "TestUser",
            "场子": "TestVenue", 
            "游戏": "百家乐",
            "本金": 1000,
            "输反": 100,
            "赢亏": -100
        }
        
        msg_time = "2025-08-08 21:38:59"
        original_message = "TestUser TestVenue 百家乐 本金1000 输反100 输100"
        
        print(f"Adding test message to cache...")
        print(f"Message: {original_message}")
        
        # Add message to cache
        message_cache.add_message(parsed_data, msg_time, original_message)
        print("Message added to cache successfully")
        
        # Check cache stats
        stats = message_cache.get_cache_stats()
        print(f"Cache stats - Total: {stats['total_cached']}, Pending: {stats['pending_retry']}")
        
        # Get pending messages
        pending = message_cache.get_pending_messages()
        print(f"Found {len(pending)} pending messages")
        
        if pending:
            msg = pending[0]
            print(f"First message: {msg['parsed_data']['人员']} - {msg['parsed_data']['场子']}")
        
        print("Cache test completed successfully!")
        return True
        
    except Exception as e:
        print(f"Cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_existing_cache():
    """Check existing cache files"""
    print("\nChecking existing cache files...")
    
    import os
    
    cache_file = "message_cache.txt"
    retry_log = "retry_log.txt"
    
    print(f"Cache file exists: {os.path.exists(cache_file)}")
    print(f"Retry log exists: {os.path.exists(retry_log)}")
    
    if os.path.exists(retry_log):
        try:
            with open(retry_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"Retry log has {len(lines)} entries")
                if lines:
                    print(f"Last entry: {lines[-1].strip()}")
        except Exception as e:
            print(f"Error reading retry log: {e}")
    
    return True


if __name__ == "__main__":
    print("Starting cache functionality test...\n")
    
    success1 = check_existing_cache()
    success2 = test_cache_functionality()
    
    if success1 and success2:
        print("\nAll tests passed! Message caching is working.")
    else:
        print("\nSome tests failed.")
