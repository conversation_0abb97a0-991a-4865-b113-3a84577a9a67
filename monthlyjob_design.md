# monthlyjob.py 月度任务详细功能和技术设计

## 🏗️ 系统架构设计

### 工作流程图
```mermaid
graph TD
    A[月度定时任务触发] --> B[工资检查和更新]
    B --> C[获取盈利数据]
    C --> D[获取开支数据]
    D --> E[数据汇总计算]
    E --> F[记录到Google Sheet]
    F --> G[生成净利润报表]
    G --> H[发送到群组]
    
    D --> D1[Excel数据源]
    D --> D2[Google Sheet数据源]
    
    E --> E1[净利润计算]
    E --> E2[成员统计]
    E --> E3[开支分类统计]
    
    G --> G1[概览板块]
    G --> G2[成员贡献板块]
    G --> G3[工资板块]
    G --> G4[开支明细板块]
```

## 📊 数据模型设计

### 2.1 开支数据模型
```python
@dataclass
class ExpenseRecord:
    submit_time: datetime          # 提交时间
    fill_id: str                  # 填写ID
    submitter: str                # 提交者
    report_date: datetime         # 填报日期
    expense_person: str           # 开支人
    expense_category: str         # 开支类别
    expense_category_extra: str   # 开支类别补充
    expense_amount: float         # 开支金额
    expense_date: datetime        # 开支项目发生日期时间
    remark: str                   # 备注
    receipt_upload: str           # 票据上传
```

### 2.2 月报数据模型
```python
@dataclass
class MonthlyReport:
    report_month: str             # 报告月份 (2025-08)
    report_time: datetime         # 报告生成时间
    total_profit: float           # 总盈利
    total_salary: float           # 总工资
    total_expense: float          # 总开支
    net_profit: float             # 净利润
    profit_members_count: int     # 盈利成员数
    loss_members_count: int       # 亏损成员数
    total_work_sessions: int      # 开工总次数
    main_expense_category: str    # 主要开支类别
    remark: str                   # 备注
```

### 2.3 成员贡献数据模型
```python
@dataclass
class MemberContribution:
    member_name: str              # 成员姓名
    profit: float                 # 盈利金额
    salary: float                 # 工资金额
    work_sessions: int            # 开工次数
    profit_percentage: float      # 盈利占比
```

## 🔧 核心功能模块设计

### 3.1 开支数据获取模块
```python
class ExpenseDataManager:
    """开支数据管理器"""
    
    def __init__(self):
        self.excel_file_pattern = "(G国先锋开支填报)团队开支明细*.xlsx"
        self.google_sheet_name = "团队开支明细"
    
    async def get_monthly_expenses(self, start_date: datetime, end_date: datetime, 
                                 data_source: str = "auto") -> List[ExpenseRecord]:
        """获取指定时间范围内的开支数据"""
        
    def _get_expenses_from_excel(self, start_date: datetime, end_date: datetime) -> List[ExpenseRecord]:
        """从Excel文件获取开支数据"""
        
    async def _get_expenses_from_google_sheet(self, start_date: datetime, end_date: datetime) -> List[ExpenseRecord]:
        """从Google Sheet获取开支数据"""
```

### 3.2 月报数据记录模块
```python
class MonthlyReportManager:
    """月报数据管理器"""
    
    def __init__(self, google_sheets_manager: GoogleSheetsManager):
        self.sheets_manager = google_sheets_manager
        self.report_sheet_name = "Group_profit_report"
    
    async def record_monthly_report(self, report_data: MonthlyReport) -> bool:
        """记录月报数据到Google Sheet"""
        
    async def _ensure_sheet_structure(self) -> bool:
        """确保Sheet结构正确"""
```

### 3.3 净利润报表生成模块
```python
class ProfitReportGenerator:
    """净利润报表生成器"""
    
    def generate_monthly_profit_report(self, profit_data: Dict, expense_data: List[ExpenseRecord], 
                                     report_month: str) -> str:
        """生成月度净利润报表"""
        
    def _generate_overview_section(self, total_profit: float, total_expense: float, 
                                 net_profit: float, report_month: str) -> str:
        """生成概览板块"""
        
    def _generate_member_contribution_section(self, profit_data: Dict, total_profit: float) -> str:
        """生成成员贡献板块"""
        
    def _generate_salary_section(self, profit_data: Dict) -> str:
        """生成成员工资板块"""
        
    def _generate_expense_section(self, expense_data: List[ExpenseRecord]) -> str:
        """生成开支明细板块"""
```

### 3.4 完整月度工作流模块
```python
class CompleteMonthlyJobManager:
    """完整月度工作流管理器"""
    
    def __init__(self):
        self.expense_manager = ExpenseDataManager()
        self.report_manager = MonthlyReportManager(google_sheets_manager)
        self.profit_generator = ProfitReportGenerator()
    
    async def execute_complete_monthly_job(self, context) -> Dict[str, Any]:
        """执行完整的月度工作流"""
```

## ⚙️ 配置扩展设计

### 配置文件扩展
```python
# config.py 新增配置项
class Config:
    # 月报相关配置
    MONTHLY_REPORT_SHEET_NAME = "Group_profit_report"
    EXPENSE_EXCEL_PATTERN = "(G国先锋开支填报)团队开支明细*.xlsx"
    EXPENSE_GOOGLE_SHEET_NAME = "团队开支明细"
    
    # 报表格式配置
    CURRENCY_FORMAT = "{:+,.0f}"
    PERCENTAGE_FORMAT = "{:.1f}%"
    
    # 月度任务执行时间配置
    MONTHLY_JOB_HOUR = 13  # UTC+4 13:00 执行完整月度任务
    MONTHLY_JOB_MINUTE = 0
```

## 🛡️ 错误处理和日志设计

```python
class MonthlyJobException(Exception):
    """月度任务异常基类"""
    pass

class ExpenseDataException(MonthlyJobException):
    """开支数据异常"""
    pass

class ReportGenerationException(MonthlyJobException):
    """报表生成异常"""
    pass

class GoogleSheetException(MonthlyJobException):
    """Google Sheet操作异常"""
    pass
```

## 🧪 测试设计

```python
class TestMonthlyJob:
    """月度任务测试类"""
    
    def test_expense_data_parsing(self):
        """测试开支数据解析"""
        
    def test_monthly_report_calculation(self):
        """测试月报计算"""
        
    def test_profit_report_generation(self):
        """测试净利润报表生成"""
        
    def test_complete_workflow(self):
        """测试完整工作流"""
```

## 🚀 性能优化设计

- **数据缓存**：缓存月度数据避免重复计算
- **批量操作**：Google Sheet批量读写操作
- **异步处理**：数据获取和处理异步化
- **错误重试**：网络操作自动重试机制
- **资源管理**：及时释放Excel文件句柄

## 🔒 安全性设计

- **数据验证**：输入数据格式和范围验证
- **权限控制**：Google Sheet访问权限验证
- **敏感信息**：避免在日志中记录敏感数据
- **异常处理**：防止异常信息泄露系统细节

## 📋 Google Sheet月报结构

### "Group_profit_report" Sheet结构：

| 列名 | 数据类型 | 说明 |
|------|----------|------|
| 报告月份 | 文本 | 格式：2025-08 |
| 报告生成时间 | 日期时间 | 自动记录生成时间 |
| 总盈利 | 数值 | 团队总盈利金额 |
| 总工资 | 数值 | 团队总工资支出 |
| 总开支 | 数值 | 团队总开支金额 |
| 净利润 | 数值 | 总盈利 - 总工资 - 总开支 |
| 盈利成员数 | 整数 | 正盈利成员数量 |
| 亏损成员数 | 整数 | 负盈利成员数量 |
| 开工总次数 | 整数 | 所有成员开工次数总和 |
| 主要开支类别 | 文本 | 占比最高的开支类别 |
| 备注 | 文本 | 其他说明信息 |

## 📊 净利润报表格式设计

### 概览板块
```
📅 报告周期：2025年8月  
💰 总盈利：+123,456  
🧾 总支出：-45,678  
🏦 净利润：+77,778
```

### 成员贡献板块
```
| 成员 | 盈利贡献    | 占比   |
| -- | ------- | ---- |
| A  | +40,000 | 32%  |
| B  | -15,000 | -12% |
| C  | +20,000 | 16%  |
| D  | -5,000  | -4%  |
| 合计 | +40,000 | 100% |
```

### 成员工资板块
```
| 成员 | 工资 | 开工次数 | 备注 |
```

### 开支明细板块
```
| 开支项 | 金额     | 占总支出的比例 |
| --- | ------ | ------- |
| 差旅  | 20,000 | 44%     |
| 工具  | 15,000 | 33%     |
| 其他  | 10,678 | 23%     |
```

## 🎯 实现优先级

1. **高优先级**：开支数据获取、月报计算、报表生成
2. **中优先级**：Google Sheet记录、定时任务集成
3. **低优先级**：性能优化、高级功能扩展
