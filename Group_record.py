#########
# 文件说明：
# 1. 该文件是一个 Telegram Bo<PERSON>，用于记录群组成员的游戏数据。
# 2. 该 Bot 支持从群组消息中提取数据，并将其存储到 Excel 文件和 Google Sheets 中。
# 3. Bot 支持命令操作，如导出数据、查找记录、生成日报和周报等。
# 4. Bot 使用了 gspread 库与 Google Sheets 进行交互，使用 openpyxl 库处理 Excel 文件。
import logging
from logging.handlers import TimedRotatingFileHandler
from datetime import time as dt_time

import nest_asyncio
from telegram.request import HTTPXRequest

from Parser import parse_message
from commander import handle_correction, person_report_command_handler, \
    export_data_command_handler, trend_command_handler, confirm_correction_command_handler, \
    venue_report_command_handler, set_rebate_ratio_command_handler, daily_command_handler, weekly_command_handler, \
    monthly_command_handler, load_rebate_config_command_handler, load_salary_config_command_handler, \
    set_salary_command_handler, cache_status_command_handler, retry_sync_command_handler, \
    config_cache_status_command_handler, refresh_config_cache_command_handler, help_command_handler, help_simple_command_handler, \
    add_venue_command_handler
from job import send_profit_trend_chart_job, send_venue_report_job, send_person_report_job, send_daily_report_job, \
    send_weekly_report_job, send_monthly_report_job
from salary import calculate_salary
from storage import load_rebate_config, load_rebate_ratio, sync_to_google_sheets, write_to_excel, \
    load_or_create_excel, search_records
from config_cache import config_cache, get_cached_rebate_config, get_cached_rebate_ratio, refresh_config_cache
from security_utils import InputValidator
from message_cache import message_cache, retry_sync_cached_messages
from startup_checker import run_startup_checks, print_startup_report

nest_asyncio.apply()

from telegram import BotCommand
from telegram import Update
from telegram.ext import (
    ApplicationBuilder,
    CommandHandler,
    MessageHandler,
    ContextTypes,
    filters,
    JobQueue,
)

# 以下代码注释掉了，改为使用统一的日志配置 2025-9-10

# 全局日志格式
# logging.basicConfig(
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     level=logging.DEBUG
# )

logging.getLogger("httpx").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

# 文件 Handler（每天 16:00 切割，保留 7 天）
# file_handler = TimedRotatingFileHandler(
#     'bot.log',
#     when='midnight',          # 按天切割
#     interval=1,
#     backupCount=7,
#     encoding='utf-8',
#     atTime=dt_time(16, 0)        # 每天 16:00 切
# )
# formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# file_handler.setFormatter(formatter)
# file_handler.setLevel(logging.DEBUG)  # 明确设置文件handler为DEBUG级别

# 控制台 Handler（可选）
# console_handler = logging.StreamHandler()
# console_handler.setFormatter(formatter)
# console_handler.setLevel(logging.INFO)  # 控制台保持INFO级别，避免太多DEBUG信息

# 添加 Handler
# logger.addHandler(file_handler)
# logger.addHandler(console_handler)

from config import Config
from logging_config import setup_logging, get_logger
# 配置
ENV = Config.ENV
EXCEL_FILE = Config.EXCEL_FILE
GROUP_CHAT_ID = Config.GROUP_CHAT_ID
BOT_TOKEN = Config.BOT_TOKEN
RECORD_SHEET_NAME = Config.RECORD_SHEET_NAME
CREDENTIALS_FILE = Config.CREDENTIALS_FILE
TIME_ZONE = Config.TIME_ZONE  # 使用 zoneinfo 模块处理时区

BASE_WAGER = Config.BASE_WAGER
REBATE_RATIO_1 = Config.REBATE_RATIO_1  # 10% 输返
REBATE_RATIO_2 = Config.REBATE_RATIO_2  # 20% 输返


async def error_handler(update, context):
    logger.error(msg="异常捕获:", exc_info=context.error)


async def retry_sync_job(context):
    """定时重试同步缓存消息的任务"""
    try:
        result = await retry_sync_cached_messages()

        if result["total"] > 0:
            success = result["success"]
            failed = result["failed"]
            total = result["total"]

            logger.info(f"重试同步结果: 成功 {success}/{total}, 失败 {failed}")

            # 如果有成功同步的消息，需要写入本地Excel
            if success > 0:
                # 这里可以添加写入Excel的逻辑，但需要获取具体的消息数据
                logger.info(f"成功同步 {success} 条消息到 Google Sheets")

    except Exception as e:
        logger.error(f"重试同步任务执行失败: {e}")


async def refresh_config_cache_job(context):
    """定时刷新配置缓存的任务"""
    try:
        result = await refresh_config_cache(force_google_sheet=True)

        if result['success']:
            logger.info(f"配置缓存刷新成功: Rebate={result.get('rebate_count', 0)} 项, Salary={result.get('salary_rules', 0)} 条规则")
        else:
            logger.warning(f"配置缓存刷新失败: {result.get('error', 'Unknown error')}")

    except Exception as e:
        logger.error(f"配置缓存刷新任务执行失败: {e}")


async def handle_group_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # 添加调试日志
    logger.info(f"收到消息，群组ID: {update.message.chat.id}, 配置的群组ID: {GROUP_CHAT_ID}")

    # 校验群组 ID
    if update.message.chat.id != GROUP_CHAT_ID: # type: ignore
        logger.info(f"群组ID不匹配，忽略消息。收到: {update.message.chat.id}, 期望: {GROUP_CHAT_ID}")
        return

    msg = update.message

    # 跳过非文本消息
    if msg is None or msg.text is None:
        logger.info(f"忽略了非文本消息：{msg}")
        return

    msg_text = msg.text
    logger.info(f"处理文本消息: {msg_text[:100]}...")  # 只显示前100个字符

    # 输入清理和基本验证
    msg_text = InputValidator.sanitize_input(msg_text)

    if not msg_text or len(msg_text.strip()) == 0:
        logger.warning("接收到空消息，已忽略")
        return

    if msg_text.startswith("更正：") or msg_text.startswith("更正:") or msg_text.startswith("更正"):
        await handle_correction(msg, msg_text[3:].strip())  # 跳过“更正：”
        return

    user = msg.from_user.first_name # type: ignore

    try:
        # 获取带时区的时间
        msg_time = msg.date.astimezone(TIME_ZONE).strftime("%Y-%m-%d %H:%M:%S") # type: ignore
        # print(f"接收到消息：{msg_text}，时间：{msg_time}，用户：{user}")
        # 解析消息
        parsed, missing = parse_message(msg_text)

        # 验证解析后的数据
        is_valid, validation_errors = InputValidator.validate_message_data(parsed)
        if not is_valid:
            error_msg = f"❌ 消息格式错误：\n" + "\n".join(validation_errors)
            await msg.reply_text(error_msg)
            logger.warning(f"消息验证失败: {validation_errors}, 原消息: {msg_text}")
            return
        # 自动计算工资字段
        venue = parsed.get("场子")
        person = parsed.get("人员")
        rebate_amount = parsed.get("输反")

        # 通过ConfigCache获取rebate比例（自动三层策略，利用config_manager）
        from config_cache import config_cache
        rebate_ratio = config_cache.get_rebate_ratio(venue, person)

        salary = 0
        is_qualified = False

        if parsed.get("工资") in [None, 0, "0"]:
            # if ENV == "dev":
            #     print(f"调试模式：未提供工资，使用自动计算，场子={venue}, 人员={person}, 输返={rebate_amount}, 输返比例={rebate_ratio}")
            try:
                profit = parsed.get("盈利", 0)
                if ENV == "dev":
                    print(
                        f"调试模式：计算工资，场子={venue}, 人员={person}, 输返={rebate_amount}, 盈亏={profit}, 输返比例={rebate_ratio}")

                game = parsed.get("游戏")  # 获取游戏类型
                is_qualified, salary = calculate_salary(venue, person, rebate_amount, profit, game)
                print(f"计算结果：是否达标={is_qualified}, 工资={salary}")
                parsed["工资"] = salary
            except Exception:
                parsed["工资"] = 0

        # 使用新的存储服务写入记录
        storage_success = False
        sheets_sync_failed = False

        # 生成唯一的消息ID用于跟踪
        import uuid
        message_id = str(uuid.uuid4())[:8]
        logger.info(f"[MSG-{message_id}] 开始处理消息: 用户={user}, 场子={venue}")

        # 启动消息处理状态跟踪
        from message_state_tracker import start_message_processing, update_processing_step
        message_data = {
            "user": user,
            "venue": venue,
            "parsed_data": parsed,
            "msg_time": msg_time,
            "original_message": msg_text[:100] + "..." if len(msg_text) > 100 else msg_text
        }
        start_message_processing(message_id, message_data)
        update_processing_step(message_id, "message_parsed")

        # 检查消息是否已经被处理过（防重复处理）
        logger.info(f"[MSG-{message_id}] 检查消息是否已处理")
        write_status = message_cache.is_message_written(parsed, msg_time)

        if write_status["write_status"] == "complete":
            logger.warning(f"[MSG-{message_id}] 消息已完全处理，跳过重复处理: Excel={write_status['excel_written']}, Sheets={write_status['sheets_written']}")
            await msg.reply_text("⚠️ 此消息已经处理过，请勿重复发送。")
            return
        elif write_status["write_status"] == "partial":
            logger.info(f"[MSG-{message_id}] 消息部分处理，将继续完成: Excel={write_status['excel_written']}, Sheets={write_status['sheets_written']}")

        # 早期缓存消息以防止进程中断导致数据丢失
        logger.info(f"[MSG-{message_id}] 预缓存消息以防进程中断")
        update_processing_step(message_id, "pre_caching")
        try:
            # 如果消息不在缓存中，添加到缓存
            if write_status["write_status"] == "not_cached":
                message_cache.add_message(parsed, msg_time, msg_text, "pending")
                logger.info(f"[MSG-{message_id}] 消息已预缓存，将在成功存储后移除")
            else:
                logger.info(f"[MSG-{message_id}] 消息已在缓存中，继续处理")
            update_processing_step(message_id, "pre_cached")
        except Exception as cache_error:
            logger.warning(f"[MSG-{message_id}] 预缓存失败: {cache_error}")
            update_processing_step(message_id, "pre_cache_failed")

        try:
            from storage_service import get_storage_service
            logger.info(f"[MSG-{message_id}] 正在获取存储服务实例...")
            update_processing_step(message_id, "getting_storage_service")
            storage_service = await get_storage_service()
            logger.info(f"[MSG-{message_id}] 存储服务实例获取成功")
            update_processing_step(message_id, "storage_service_obtained")

            # 详细记录写入过程
            logger.info(f"[MSG-{message_id}] 开始调用 storage_service.write_record")
            logger.debug(f"[MSG-{message_id}] 写入数据: {parsed}")
            logger.debug(f"[MSG-{message_id}] 消息时间: {msg_time}")
            update_processing_step(message_id, "calling_write_record")

            # 记录开始时间
            import time
            start_time = time.time()

            result = await storage_service.write_record(parsed, msg_time)

            # 记录完成时间
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"[MSG-{message_id}] write_record 调用完成，耗时: {duration:.3f}秒")
            update_processing_step(message_id, "write_record_completed")

            if hasattr(result, 'success') and result.success:
                logger.info(f"[MSG-{message_id}] 记录写入成功: {result.message}")
                storage_success = True

                # 检查是否只是部分成功（Excel成功但Google Sheets失败）
                excel_success = True  # storage_service.write_record 成功意味着Excel写入成功
                sheets_success = True  # 默认假设都成功

                if hasattr(result, 'data') and result.data:
                    record_data = result.data
                    if record_data.get('status') == 'PENDING':
                        sheets_sync_failed = True
                        sheets_success = False
                        logger.info(f"[MSG-{message_id}] Google Sheets 同步失败，消息将保留在缓存以便重试")
                    else:
                        logger.info(f"[MSG-{message_id}] 完全成功，准备清理预缓存")

                # 更新缓存中的写入状态
                try:
                    message_cache.update_write_status(parsed, msg_time, excel_success, sheets_success)
                    logger.info(f"[MSG-{message_id}] 已更新写入状态: Excel={excel_success}, Sheets={sheets_success}")
                except Exception as status_error:
                    logger.warning(f"[MSG-{message_id}] 更新写入状态失败: {status_error}")

                # 如果完全成功，清理预缓存的消息
                if not sheets_sync_failed:
                    try:
                        # 从缓存中移除成功处理的消息
                        cached_messages = message_cache._load_cache()
                        for cached_msg in cached_messages:
                            if (cached_msg.get('parsed_data') == parsed and
                                cached_msg.get('msg_time') == msg_time):
                                message_cache.remove_message(cached_msg)
                                logger.info(f"[MSG-{message_id}] 已清理预缓存消息")
                                break
                    except Exception as cleanup_error:
                        logger.warning(f"[MSG-{message_id}] 清理预缓存失败: {cleanup_error}")

            elif result:  # 如果返回 True 表示成功
                logger.info(f"[MSG-{message_id}] 记录写入成功（旧格式返回）")
                storage_success = True

                # 更新缓存中的写入状态（假设都成功）
                try:
                    message_cache.update_write_status(parsed, msg_time, True, True)
                    logger.info(f"[MSG-{message_id}] 已更新写入状态: Excel=True, Sheets=True")
                except Exception as status_error:
                    logger.warning(f"[MSG-{message_id}] 更新写入状态失败: {status_error}")

                # 清理预缓存
                try:
                    cached_messages = message_cache._load_cache()
                    for cached_msg in cached_messages:
                        if (cached_msg.get('parsed_data') == parsed and
                            cached_msg.get('msg_time') == msg_time):
                            message_cache.remove_message(cached_msg)
                            logger.info(f"[MSG-{message_id}] 已清理预缓存消息")
                            break
                except Exception as cleanup_error:
                    logger.warning(f"[MSG-{message_id}] 清理预缓存失败: {cleanup_error}")
            else:
                logger.error(f"[MSG-{message_id}] 记录写入失败: {getattr(result, 'message', '未知错误')}")
                # 如果新存储服务失败，回退到旧方法
                try:
                    logger.info(f"[MSG-{message_id}] 尝试回退到旧存储方法")
                    write_to_excel(parsed, msg_time)
                    logger.info(f"[MSG-{message_id}] 回退到旧存储方法成功")
                    storage_success = True
                    sheets_sync_failed = True  # 旧方法不包含Google Sheets同步

                    # 更新缓存中的写入状态（Excel成功，Sheets失败）
                    try:
                        message_cache.update_write_status(parsed, msg_time, True, False)
                        logger.info(f"[MSG-{message_id}] 已更新写入状态: Excel=True, Sheets=False")
                    except Exception as status_error:
                        logger.warning(f"[MSG-{message_id}] 更新写入状态失败: {status_error}")

                    # 保留预缓存消息用于Google Sheets重试
                    logger.info(f"[MSG-{message_id}] 保留预缓存消息用于Google Sheets重试")
                except Exception as fallback_error:
                    logger.error(f"[MSG-{message_id}] 回退存储方法也失败: {fallback_error}")
                    # 预缓存消息已存在，无需重复添加
                    logger.info(f"[MSG-{message_id}] 消息已在预缓存中，将自动重试")
                    await msg.reply_text("❗ 本地和云端保存都失败，但已缓存消息，将自动重试同步。\n"
                                        "💡 使用 /cache_status 查看缓存状态，/retry_sync 手动重试")
                    return

        except Exception as e:
            logger.error(f"[MSG-{message_id}] 使用新存储服务失败: {e}")
            logger.error(f"[MSG-{message_id}] 异常类型: {type(e).__name__}")
            import traceback
            logger.error(f"[MSG-{message_id}] 异常堆栈: {traceback.format_exc()}")

            # 检查是否是存储服务初始化失败
            if "存储服务初始化失败" in str(e) or "StorageError" in str(type(e).__name__):
                logger.error(f"[MSG-{message_id}] 存储服务初始化失败，这可能是首次启动或网络问题")
                # 预缓存消息已存在，无需重复添加
                logger.info(f"[MSG-{message_id}] 消息已在预缓存中，将自动重试")
                await msg.reply_text("⚠️ 存储服务初始化失败（可能是首次启动或网络问题），消息已缓存，将自动重试。\n"
                                    "💡 使用 /cache_status 查看缓存状态，/retry_sync 手动重试")
                return

            # 回退到旧方法
            try:
                logger.info(f"[MSG-{message_id}] 尝试回退到旧存储方法")
                write_to_excel(parsed, msg_time)
                logger.info(f"[MSG-{message_id}] 回退到旧存储方法成功")
                storage_success = True
                sheets_sync_failed = True  # 旧方法不包含Google Sheets同步
                # 保留预缓存消息用于Google Sheets重试
                logger.info(f"[MSG-{message_id}] 保留预缓存消息用于Google Sheets重试")
            except Exception as fallback_error:
                logger.error(f"[MSG-{message_id}] 回退存储方法也失败: {fallback_error}")
                logger.error(f"[MSG-{message_id}] 回退异常类型: {type(fallback_error).__name__}")
                # 预缓存消息已存在，无需重复添加
                logger.info(f"[MSG-{message_id}] 消息已在预缓存中，将自动重试")
                await msg.reply_text("❗ 本地和云端保存都失败，但已缓存消息，将自动重试同步。\n"
                                    "💡 使用 /cache_status 查看缓存状态，/retry_sync 手动重试")
                return

        # 如果Google Sheets同步失败但Excel成功，确保消息在缓存中
        if storage_success and sheets_sync_failed:
            logger.info(f"[MSG-{message_id}] Google Sheets同步失败但Excel成功，消息已在预缓存中")
            logger.info(f"[MSG-{message_id}] 将在后台自动重试Google Sheets同步")

        # 回复用户 - 确保在存储成功后发送消息
        if storage_success:
            logger.info(f"[MSG-{message_id}] 准备发送成功反馈给用户")
            # 构建基础回复消息
            base_message = f"✅ 已记录，感谢你，{user}！\n（按 {venue} 输返比例 {rebate_ratio:.0%} 自动计算工资）\n"

            if is_qualified:
                base_message += f"💰 工资：{salary}（自动计算工资(测试版本)）"
            else:
                base_message += f"工作未达标，💰 工资为 0 💸😢（自动计算工资(测试版本)）"

            # 如果Google Sheets同步失败，添加提示信息
            if sheets_sync_failed:
                base_message += f"\n\n⚠️ Google Sheets 同步暂时失败（可能是网络问题），消息已缓存，将自动重试同步。\n"
                base_message += f"💡 使用 /cache_status 查看缓存状态，/retry_sync 手动重试同步"

            await msg.reply_text(base_message)
            logger.info(f"[MSG-{message_id}] 用户反馈已发送")
        else:
            logger.error(f"[MSG-{message_id}] 存储失败，发送失败反馈给用户")
            await msg.reply_text("❗ 记录保存失败，请稍后重试。")

        logger.info(f"[MSG-{message_id}] 消息处理完成")

        # 完成消息处理状态跟踪
        from message_state_tracker import complete_message_processing
        complete_message_processing(message_id, storage_success)

    except ValueError as e:
        await msg.reply_text(f"❌ 数据验证失败: {str(e)}")
    except Exception as e:
        await msg.reply_text(f"❌ 记录失败: {str(e)}")



async def initialize_storage_service():
    """预初始化存储服务以避免首次消息处理失败"""
    try:
        logger.info("预初始化存储服务...")
        from storage_service import get_storage_service
        storage_service = await get_storage_service()
        logger.info("存储服务预初始化成功")
        return True
    except Exception as e:
        logger.warning(f"存储服务预初始化失败: {e}")
        logger.info("存储服务将在首次使用时初始化")
        return False

# 主程序
async def main():
    setup_logging(console_output=True, file_output=True)
    logger = get_logger(__name__)
    # 运行统一启动检查（包含进程中断检查、消息状态检查等）
    logger.info("🔍 开始启动检查...")
    startup_report = await run_startup_checks()
    print_startup_report(startup_report)

    # 预初始化存储服务
    print("🔧 预初始化存储服务...")
    storage_init_success = await initialize_storage_service()
    if storage_init_success:
        print("✅ 存储服务预初始化成功")
    else:
        print("⚠️ 存储服务预初始化失败，将在首次使用时重试")

    # 启动进程监控
    print("📊 启动进程监控...")
    from process_monitor import start_process_monitoring
    start_process_monitoring()

    # 检查是否有严重错误
    if not startup_report['success']:
        print("❌ 启动检查发现严重错误，程序可能无法正常运行")
        if startup_report['errors']:
            print("🛑 建议修复以下错误后重新启动：")
            for error in startup_report['errors']:
                print(f"   • {error}")

        # 询问是否继续启动
        try:
            response = input("\n是否仍要继续启动？(y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("👋 程序启动已取消")
                return
        except KeyboardInterrupt:
            print("\n👋 程序启动已取消")
            return

    load_or_create_excel()

    # 导入并使用共享的 bot_setup 模块
    from bot_setup import create_application

    # 创建并配置应用程序
    app = create_application()
    # Bot 命令已通过 create_application() 设置


    # 设置定时任务（以 UTC 时间为基准）
    job_queue: JobQueue = app.job_queue

    
    # 注册消息处理器
    app.add_handler(MessageHandler(filters.TEXT & filters.ChatType.GROUPS, handle_group_message))

    print("✅ Bot 已启动，监听中...")
    print(f"运行模式：{Config.ENV}; 📂 Excel 文件：{EXCEL_FILE}; Google Sheet 名称：{RECORD_SHEET_NAME}")
    
    # 设置信号处理
    import signal
    shutdown_requested = False
    
    def signal_handler(signum, frame):
        nonlocal shutdown_requested
        logger.info(f"收到终止信号 {signum}，准备关闭 Bot...")
        shutdown_requested = True
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    try:
        # 创建一个任务来运行轮询，以便我们可以取消它
        polling_task = asyncio.create_task(
            app.run_polling(
                drop_pending_updates=True,
                allowed_updates=Update.ALL_TYPES,
                close_loop=True
            )
        )
        
        # 等待终止信号或轮询任务完成
        while not shutdown_requested and not polling_task.done():
            await asyncio.sleep(0.5)
            
        # 如果收到终止信号，取消轮询任务
        if shutdown_requested and not polling_task.done():
            logger.info("正在停止 Telegram Bot...")
            polling_task.cancel()
            try:
                await polling_task
            except asyncio.CancelledError:
                logger.info("Telegram Bot 轮询已取消")
    finally:
        print("👋 Bot 正在关闭...")
        # 停止进程监控
        from process_monitor import stop_process_monitoring
        stop_process_monitoring()
        print("👋 Bot 已关闭，退出完毕。")


if __name__ == "__main__":
    import asyncio

    try:
        asyncio.run(main())
    except RuntimeError as e:
        if "Cannot close a running event loop" in str(e):
            logger.info("事件循环已关闭")
        else:
            raise
