# 🚀 部署指南

本项目提供多种部署方式，选择最适合你的方式：

## 📋 部署方式对比

| 部署方式 | 难度 | 适用场景 | 特点 |
|---------|------|----------|------|
| **一键脚本部署** | ⭐ | 快速体验 | 最简单，自动化程度高 |
| **Docker 部署** | ⭐⭐ | 生产环境 | 容器化，易于管理 |
| **传统部署** | ⭐⭐⭐ | 自定义需求 | 灵活性高，完全控制 |

---

## 🎯 一键脚本部署（推荐新手）

### ✨ 新功能：统一部署脚本

现在的部署脚本支持**两种部署方式**，让你可以根据需求选择：

1. **🐳 Docker 部署** - 容器化部署，适合生产环境
2. **🐍 传统部署** - Python 虚拟环境，适合开发和自定义需求

### Linux/macOS 用户

```bash
# 1. 克隆项目
git clone https://github.com/wunifeng/tele_bot_grab_group_message.git
cd tele_bot_grab_group_message

# 2. 配置环境变量
cp .env.template .env
# 编辑 .env 文件，填入你的配置

# 3. 添加 Google Sheets 凭据
# 将 mysheetapp.json 放到项目根目录

# 4. 运行统一部署脚本
chmod +x quick_deploy.sh
./quick_deploy.sh

# 脚本会提示你选择部署方式：
# 1) Docker 部署 (需要 Docker)
# 2) 传统部署 (需要 Python 3.9+)
```

### Windows 用户

```cmd
# 1. 克隆项目
git clone https://github.com/wunifeng/tele_bot_grab_group_message.git
cd tele_bot_grab_group_message

# 2. 配置环境变量
copy .env.template .env
# 编辑 .env 文件，填入你的配置

# 3. 添加 Google Sheets 凭据
# 将 mysheetapp.json 放到项目根目录

# 4. 运行统一部署脚本
quick_deploy.bat

# 脚本会提示你选择部署方式：
# 1) Docker 部署 (需要 Docker Desktop)
# 2) 传统部署 (需要 Python 3.9+)
```

### 🚀 部署方式选择指南

#### 选择 Docker 部署，如果你：
- ✅ 想要最简单的生产环境部署
- ✅ 需要完整的监控和健康检查
- ✅ 希望应用与系统环境隔离
- ✅ 计划在服务器上长期运行

#### 选择传统部署，如果你：
- ✅ 想要更灵活的配置选项
- ✅ 需要直接访问 Python 环境
- ✅ 希望更快的启动速度
- ✅ 用于开发和调试

---

## 🐳 Docker 部署（推荐生产环境）

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 2GB+ 可用内存
- 2GB+ 可用磁盘空间

### 快速部署

```bash
# 1. 准备配置
cp .env.template .env
# 编辑 .env 文件

# 2. 基础部署
cd docker
docker-compose up -d

# 3. 带监控的完整部署
docker-compose --profile monitoring up -d
```

### 详细说明

请查看 **[Docker 部署详细指南](DOCKER_DEPLOYMENT_GUIDE.md)** 获取完整的部署说明。

---

## 🛠️ 传统部署（已集成到统一脚本）

### ✨ 新方式：使用统一部署脚本

传统部署功能现已集成到 `quick_deploy.sh` 和 `quick_deploy.bat` 中！

```bash
# Linux/macOS
./quick_deploy.sh
# 选择 "2) 传统部署"

# Windows
quick_deploy.bat
# 选择 "2) 传统部署"
```

### 传统部署功能特点

- **自动环境检查** - 检测 Python 3.9+ 版本
- **虚拟环境管理** - 自动创建和激活虚拟环境
- **依赖安装** - 自动安装所有必需依赖
- **测试运行** - 可选的自动化测试
- **应用启动** - 支持基础版本和增强版本启动

### 手动部署（高级用户）

如果你需要完全手动控制部署过程：

```bash
# 1. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境
cp .env.template .env
# 编辑 .env 文件

# 4. 运行应用
python Group_record.py
# 或增强版本
python main_with_monitoring.py
```

### 使用独立部署脚本（已弃用）

```bash
# 这些命令仍然可用，但推荐使用统一脚本
python deploy.py --env development
python deploy.py --env production
```

---

## ⚙️ 配置说明

### 必需配置

在 `.env` 文件中配置以下必需项：

```bash
# Telegram Bot 配置
BOT_TOKEN=你的Bot Token
CHAT_ID=你的群组Chat ID

# Google Sheets 配置
GOOGLE_SHEET_NAME=你的表格名称
CREDENTIALS_FILE=mysheetapp.json
```

### 获取配置信息

#### 1. 获取 Bot Token

1. 在 Telegram 中搜索 `@BotFather`
2. 发送 `/newbot` 创建新机器人
3. 按提示设置机器人名称
4. 获得 Bot Token

#### 2. 获取 Chat ID

1. 将机器人添加到目标群组
2. 在群组中发送任意消息
3. 访问 `https://api.telegram.org/bot<你的Bot Token>/getUpdates`
4. 在返回的 JSON 中找到 `chat.id`

#### 3. 配置 Google Sheets

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google Sheets API
4. 创建服务账号并下载 JSON 凭据文件
5. 将凭据文件重命名为 `mysheetapp.json`
6. 将服务账号邮箱添加到你的 Google Sheets 中

---

## 📊 监控和维护

### 健康检查

```bash
# 检查应用状态
curl http://localhost:8080/health

# 详细健康检查
curl http://localhost:8080/health/detailed
```

### 查看日志

```bash
# Docker 部署
docker-compose logs -f telegram-bot

# 传统部署
tail -f logs/telegram_bot_*.log
```

### 备份数据

```bash
# 创建备份
tar -czf backup-$(date +%Y%m%d).tar.gz data/ logs/ .env mysheetapp.json

# Docker 数据备份
docker-compose exec telegram-bot tar -czf /app/backups/backup-$(date +%Y%m%d).tar.gz /app/data/
```

---

## 🔧 故障排除

### 常见问题

#### 1. Bot 无法连接到 Telegram

- 检查 `BOT_TOKEN` 是否正确
- 确认网络连接正常
- 验证防火墙设置

#### 2. Google Sheets 连接失败

- 检查 `mysheetapp.json` 文件是否存在
- 确认服务账号有表格访问权限
- 验证表格名称是否正确

#### 3. 容器启动失败

```bash
# 查看详细错误
docker-compose logs telegram-bot

# 检查配置
docker-compose config

# 重新构建
docker-compose build --no-cache
```

### 获取帮助

1. 查看日志文件获取详细错误信息
2. 检查 [Issues](https://github.com/wunifeng/tele_bot_grab_group_message/issues) 页面
3. 运行部署验证脚本：`python verify_deployment.py`

---

## 📚 相关文档

- **[Docker 部署详细指南](DOCKER_DEPLOYMENT_GUIDE.md)** - 完整的 Docker 部署说明
- **[API 文档](API_DOCUMENTATION.md)** - 接口说明文档
- **[开发指南](DEVELOPMENT_GUIDE.md)** - 开发环境搭建

---

## 🎉 部署成功后

部署成功后，你可以：

1. **测试机器人功能**：在群组中发送消息测试
2. **查看监控面板**：访问 http://localhost:3000 (如果启用了监控)
3. **检查健康状态**：访问 http://localhost:8080/health
4. **查看应用日志**：监控应用运行状态

**恭喜！你的 Telegram Bot 现在已经成功运行！** 🎊
