# group_bot/parser.py
import re
from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple
import cn2an, arrow
from dateutil import parser
import logging
from alias_manager import FIELD_ALIASES, resolve_with_aliases, GAME_ALIASES, PERSON_ALIASES, LOCATION_ALIASES
from config import Config

# 设置时区
TIME_ZONE = Config.TIME_ZONE
ENV = Config.ENV
logger = logging.getLogger(__name__)


def parse_date_range(args: list[str]) -> Optional[Tuple[datetime, datetime]]:
    try:
        if len(args) == 2:
            start = parse_datetime(args[0])
            start = start.replace(hour=12, minute=0, second=0, microsecond=0)
            end = parse_datetime(args[1])
            end = (end + timedelta(days=1)).replace(hour=11, minute=59, second=59, microsecond=999999)
        elif len(args) == 0:
            start, end = get_current_month_range()
        else:
            raise ValueError
        return start, end
    except Exception:
        return None


# 计算昨天的起止日期
def get_yesterday_range() -> tuple[datetime, datetime]:
    now = datetime.now(TIME_ZONE)
    start_of_yesterday = (now - timedelta(days=1)).replace(hour=12, minute=0, second=0, microsecond=0)
    end_of_yesterday = now.replace(hour=11, minute=59, second=59, microsecond=999999)
    return start_of_yesterday, end_of_yesterday


# 计算本周的起止日期
def get_current_week_range() -> tuple[datetime, datetime]:
    now = datetime.now(TIME_ZONE)
    start_of_week = now - timedelta(days=now.weekday())
    start_of_week = start_of_week.replace(hour=12, minute=0, second=0, microsecond=0)
    end_of_week = now.replace(hour=11, minute=59, second=59, microsecond=999999)
    return start_of_week, end_of_week

# 计算上周的起止日期
def get_last_week_range() -> tuple[datetime, datetime]:
    now = datetime.now(TIME_ZONE)
    start_of_this_week = (now - timedelta(days=now.weekday())).replace(hour=12, minute=0, second=0, microsecond=0)
    start_of_last_week = start_of_this_week - timedelta(days=7)
    end_of_last_week = start_of_this_week - timedelta(seconds=1)
    return start_of_last_week, end_of_last_week


# 计算本月1到到目前的起止日期
def get_current_month_range() -> tuple[datetime, datetime]:
    now = datetime.now(TIME_ZONE)
    first_day_this_month = now.replace(day=1, hour=12, minute=0, second=0, microsecond=0)
    last_day_end = now.replace(hour=11, minute=59, second=59, microsecond=999999)
    return first_day_this_month, last_day_end


# 计算上个月的起止日期
def get_last_month_range() -> tuple[datetime, datetime]:
    now = datetime.now(TIME_ZONE)
    first_day_this_month = now.replace(day=1, hour=12, minute=0, second=0, microsecond=0)
    last_day_end = first_day_this_month - timedelta(seconds=1)
    first_day_start = (last_day_end - timedelta(days=1)).replace(day=1, hour=12, minute=0, second=0, microsecond=0)
    return first_day_start, last_day_end




def parse_datetime(text, prefer_future=True) -> datetime:
    """解析各种时间格式，统一返回 UTC+4 datetime"""
    if isinstance(text, datetime):
        if text.tzinfo is None:
            return text.replace(tzinfo=TIME_ZONE)
        else:
            return text.astimezone(TIME_ZONE)

    try:
        text = cn2an.transform(text, "cn2an")
    except Exception as e:
        # 中文数字转换失败不是致命错误，记录日志但继续处理
        logger.debug(f"中文数字转换失败，使用原始文本: {e}")

    replace_map = {
        r"年": "-", r"月": "-", r"日": " ",
        r"号": " ", r"点": ":", r"时": ":", r"分": "",
        r"上午": "AM", r"下午": "PM", r"中午": "12:", r"晚上": "PM", r"早上": "AM"
    }
    for k, v in replace_map.items():
        text = re.sub(k, v, text)

    now = datetime.now(TIME_ZONE)

    match = re.match(r"(\d+)\s*天前", text)
    if match:
        return now - timedelta(days=int(match.group(1)))
    match = re.match(r"(\d+)\s*小时前", text)
    if match:
        return now - timedelta(hours=int(match.group(1)))
    match = re.match(r"(\d+)\s*分钟前", text)
    if match:
        return now - timedelta(minutes=int(match.group(1)))

    if "昨天" in text:
        text = text.replace("昨天", (now - timedelta(days=1)).strftime("%Y-%m-%d"))
    elif "前天" in text:
        text = text.replace("前天", (now - timedelta(days=2)).strftime("%Y-%m-%d"))
    elif "今天" in text:
        text = text.replace("今天", now.strftime("%Y-%m-%d"))
    elif "明天" in text:
        text = text.replace("明天", (now + timedelta(days=1)).strftime("%Y-%m-%d"))

    try:
        t = arrow.get(text).datetime
        if t.tzinfo is None:
            t = t.replace(tzinfo=TIME_ZONE)
        else:
            t = t.astimezone(TIME_ZONE)
        return t
    except Exception as e:
        logger.debug(f"Arrow时间解析失败: {e}")

    try:
        t = parser.parse(text)
        if t.tzinfo is None:
            t = t.replace(tzinfo=TIME_ZONE)
        else:
            t = t.astimezone(TIME_ZONE)
        return t
    except Exception as e:
        logger.debug(f"Dateutil时间解析失败: {e}")

    from exceptions import ValidationError
    raise ValidationError(f"无法解析时间字符串: {text}", field_name="时间", field_value=text)


def normalize_msg_time_timezone(value: datetime | str,
                                original_text: Optional[str] = None,
                                prefer_label_fix: bool = True) -> datetime:
    """将消息时间统一规范为带 Config.TIME_ZONE 的 datetime。

    规则：
    - 若无 tz，则直接贴上 TIME_ZONE（不改变时刻）。
    - 若 tz 为 0 偏移（看起来像 UTC）且原始文本不含时区线索，按“贴标签纠偏”将 tz 改为 TIME_ZONE（不改变时刻）。
    - 若原始文本显式包含时区（或已有非 0 偏移 tz），则转换到 TIME_ZONE（保持绝对时刻）。
    """
    # 归一化输入与原始文本
    text = None
    if isinstance(value, str):
        text = value
        dt = parse_datetime(value)
    elif isinstance(value, datetime):
        dt = value
        text = original_text if isinstance(original_text, str) else None
    else:
        # 非预期类型，尝试字符串化再解析
        text = str(value)
        dt = parse_datetime(text)

    # 判定原文是否包含显式时区线索
    contains_tz_hint = False
    if isinstance(text, str):
        tz_hints = [r"\bZ\b", r"UTC", r"GMT", r"[+-]\d{2}:?\d{2}"]
        for pat in tz_hints:
            if re.search(pat, text, flags=re.IGNORECASE):
                contains_tz_hint = True
                break

    # 无 tz：直接贴标签
    if dt.tzinfo is None:
        return dt.replace(tzinfo=TIME_ZONE)

    # 已有 tz：根据规则处理
    zero_offset = (dt.utcoffset() == timedelta(0))

    # 优先“贴标签纠偏”：看似 UTC 但原文无 tz 线索
    if prefer_label_fix and zero_offset and not contains_tz_hint:
        return dt.replace(tzinfo=TIME_ZONE)

    # 正常转换至配置时区（若不相等）
    try:
        return dt if dt.tzinfo == TIME_ZONE else dt.astimezone(TIME_ZONE)
    except Exception:
        # 回退：强行贴标签
        return dt.replace(tzinfo=TIME_ZONE)

def parse_message(text: str, strict: bool = True) -> tuple[dict, list[str]]:
    """解析消息文本，包含输入验证和清理"""
    from input_validator import SecurityValidator
    
    # 输入验证和清理
    if not text or not isinstance(text, str):
        raise ValueError("❌ 输入文本无效")
    
    # 长度限制和安全清理
    text = SecurityValidator.sanitize_input(text, max_length=5000)
    if not text.strip():
        raise ValueError("❌ 输入文本为空")
    
    fields = {k: None for k in FIELD_ALIASES}
    fields["备注"] = ""
    lines = text.strip().splitlines()
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 解析键值对
        if ":" in line:
            raw_key, value = line.split(":", 1)
        elif "：" in line:
            raw_key, value = line.split("：", 1)
        else:
            continue
            
        # 清理键和值
        raw_key = SecurityValidator.sanitize_input(raw_key.strip(), max_length=50)
        value = SecurityValidator.sanitize_input(value.strip(), max_length=200)
        
        if not raw_key:
            continue
            
        key = resolve_with_aliases(raw_key, FIELD_ALIASES)
        if key:
            fields[key] = value

    if strict:
        required_fields = [k for k in fields if k != "备注"]
        missing = [k for k in required_fields if not fields[k]]
        if missing:
            raise ValueError(f"❌ 缺少字段：{', '.join(missing)}")

    # 验证和转换数字字段（从aliases.json动态获取）
    try:
        from alias_manager import get_numeric_field_names
        int_fields = get_numeric_field_names()
        if ENV == "dev":
            print(f"调试模式：动态获取数字字段: {int_fields}")
    except Exception as e:
        logger.error(f"获取数字字段失败，使用fallback: {e}")
        # Fallback到硬编码列表
        int_fields = ["起始本金", "点码", "工资", "输返", "盈利"]
    for field in int_fields:
        if fields[field] is None:
            continue
        try:
            # 清理数字字符串
            value = str(fields[field]).replace(",", "").replace("+", "").strip()
            value = re.sub(r"[－—–‒]", "-", value)
            
            # 数字验证
            is_valid, error = SecurityValidator.validate_number(value, min_val=-999999999, max_val=999999999)
            if not is_valid:
                raise ValueError(f"❌ 字段「{field}」{error}。收到：{fields[field]}")
            
            fields[field] = int(value)
            
        except ValueError:
            raise  # 重新抛出验证错误
        except Exception:
            if ENV == "dev":
                print(f"调试模式：字段「{field}」格式错误，必须是整数。收到：{fields[field]}")
            raise ValueError(f"❌ 字段「{field}」格式错误，必须是数字。收到：{fields[field]}")
    
    # 验证日期字段
    if fields.get("日期"):
        try:
            raw = SecurityValidator.sanitize_input(fields["日期"], max_length=50)
            if "年" not in raw:
                raw = f"{datetime.now().year}年{raw}"
            # 验证日期格式
            parsed_date = datetime.strptime(raw, "%Y年%m月%d日")
            fields["日期"] = parsed_date.strftime("%Y-%m-%d")
        except Exception:
            raise ValueError(f"❌ 日期格式错误。收到：{fields['日期']}")

    # 验证和清理卡号
    if fields.get("卡号") is not None:
        card_no = SecurityValidator.sanitize_input(str(fields["卡号"]), max_length=50)
        is_valid, error = SecurityValidator.validate_field("卡号", card_no)
        if not is_valid:
            raise ValueError(f"❌ 卡号格式错误：{error}")
        fields["卡号"] = card_no

    # 解析游戏别名
    if fields.get("游戏"):
        game = SecurityValidator.sanitize_input(fields["游戏"], max_length=50)
        is_valid, error = SecurityValidator.validate_field("游戏", game)
        if not is_valid:
            raise ValueError(f"❌ 游戏名称格式错误：{error}")
        fields["游戏"] = resolve_with_aliases(game, GAME_ALIASES)

    # 解析人员别名
    if fields.get("人员"):
        person = SecurityValidator.sanitize_input(fields["人员"], max_length=50)
        is_valid, error = SecurityValidator.validate_field("人员", person)
        if not is_valid:
            raise ValueError(f"❌ 人员名称格式错误：{error}")
        fields["人员"] = resolve_with_aliases(person, PERSON_ALIASES)

    # 解析场子别名
    if fields.get("场子"):
        venue = SecurityValidator.sanitize_input(fields["场子"], max_length=50)
        is_valid, error = SecurityValidator.validate_field("场子", venue)
        if not is_valid:
            raise ValueError(f"❌ 场子名称格式错误：{error}")
        fields["场子"] = resolve_with_aliases(venue, LOCATION_ALIASES)

    missing = [k for k in fields if fields[k] is None]
    if strict and missing:
        raise ValueError(f"缺少字段: {', '.join(missing)}")

    return fields, missing
