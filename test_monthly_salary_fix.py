#!/usr/bin/env python3
"""
测试月度工资检查修复
模拟实际的月度工资检查流程，验证游戏类型别名转换修复是否有效
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_game_aliases():
    """加载游戏别名"""
    try:
        with open('aliases.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('games', {})
    except Exception as e:
        print(f"加载别名文件失败: {e}")
        return {}

def load_salary_config():
    """加载工资配置"""
    try:
        with open('salary_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载工资配置失败: {e}")
        return {}

def resolve_with_aliases_simple(value, alias_dict, fallback=None):
    """简化版别名解析函数"""
    if not value:
        return fallback
    
    value_clean = value.strip().lower()
    if not value_clean:
        return fallback
    
    # 构建映射表
    mapping = {std.lower(): std for std in alias_dict}
    
    for std, aliases in alias_dict.items():
        for alias in aliases:
            clean_alias = alias.strip().lower()
            if clean_alias:
                mapping[clean_alias] = std
    
    if value_clean in mapping:
        return mapping[value_clean]
    
    return value_clean

def find_salary_rule(salary_config, rebate_ratio, game, profit):
    """查找工资规则"""
    rebate_key = str(rebate_ratio)
    if rebate_key not in salary_config:
        return None
    
    if game not in salary_config[rebate_key]:
        return None
    
    rules = salary_config[rebate_key][game]
    
    # 遍历规则，找到匹配的盈利范围
    for rule in rules:
        if not rule.get("enabled", True):
            continue
        
        profit_min = rule.get("profit_min")
        profit_max = rule.get("profit_max")
        
        # 检查盈利是否在范围内
        if profit_min is not None and profit < profit_min:
            continue
        if profit_max is not None and profit > profit_max:
            continue
        
        return rule.get("salary", 0)
    
    return None

def simulate_monthly_salary_check():
    """模拟月度工资检查流程"""
    print("🔍 模拟月度工资检查流程")
    print("=" * 60)
    
    # 加载配置
    game_aliases = load_game_aliases()
    salary_config = load_salary_config()
    
    # 模拟问题案例：Excel中的数据
    test_case = {
        "row": 614,
        "person": "吴风",
        "venue": "Merit北塞", 
        "game": "uth",  # 小写，这是问题的根源
        "profit": -1600,
        "current_salary": 15,
        "rebate_ratio": 0.2
    }
    
    print(f"📊 测试案例（第{test_case['row']}行）:")
    print(f"  人员: {test_case['person']}")
    print(f"  场子: {test_case['venue']}")
    print(f"  原始游戏类型: '{test_case['game']}'")
    print(f"  盈利: {test_case['profit']}")
    print(f"  当前工资: {test_case['current_salary']}")
    print(f"  返佣比例: {test_case['rebate_ratio']}")
    print()
    
    # 修复前：直接使用原始游戏类型
    print("❌ 修复前的逻辑:")
    original_game = test_case['game']
    salary_before = find_salary_rule(salary_config, test_case['rebate_ratio'], original_game, test_case['profit'])
    print(f"  使用游戏类型: '{original_game}'")
    print(f"  查找到的工资规则: {salary_before}")
    if salary_before is None:
        print(f"  ⚠️ 未找到匹配的工资规则，默认工资为0")
        expected_salary_before = 0
    else:
        expected_salary_before = salary_before
    print(f"  预期工资: {expected_salary_before}")
    print(f"  是否需要更新: {'是' if test_case['current_salary'] != expected_salary_before else '否'}")
    print()
    
    # 修复后：应用别名转换
    print("✅ 修复后的逻辑:")
    resolved_game = resolve_with_aliases_simple(test_case['game'], game_aliases, test_case['game'])
    salary_after = find_salary_rule(salary_config, test_case['rebate_ratio'], resolved_game, test_case['profit'])
    print(f"  原始游戏类型: '{test_case['game']}' -> 转换后: '{resolved_game}'")
    print(f"  查找到的工资规则: {salary_after}")
    if salary_after is None:
        print(f"  ⚠️ 未找到匹配的工资规则，默认工资为0")
        expected_salary_after = 0
    else:
        expected_salary_after = salary_after
    print(f"  预期工资: {expected_salary_after}")
    print(f"  是否需要更新: {'是' if test_case['current_salary'] != expected_salary_after else '否'}")
    print()
    
    # 结果对比
    print("📈 修复效果对比:")
    print(f"  修复前: 游戏类型 '{original_game}' -> 工资 {expected_salary_before}")
    print(f"  修复后: 游戏类型 '{resolved_game}' -> 工资 {expected_salary_after}")
    
    if expected_salary_before != expected_salary_after:
        print(f"  ✅ 修复成功！避免了错误的工资更新")
    else:
        print(f"  ⚠️ 修复前后结果相同")
    
    # 验证具体的UTH规则
    print()
    print("🎯 UTH游戏工资规则验证:")
    rebate_key = str(test_case['rebate_ratio'])
    if rebate_key in salary_config and 'UTH' in salary_config[rebate_key]:
        uth_rules = salary_config[rebate_key]['UTH']
        print(f"  返佣比例 {test_case['rebate_ratio']} 下的UTH规则:")
        for i, rule in enumerate(uth_rules, 1):
            print(f"    {i}. 盈利范围: {rule.get('profit_min', '无限制')} ~ {rule.get('profit_max', '无限制')}")
            print(f"       工资: {rule.get('salary', 0)}")
            print(f"       描述: {rule.get('description', '无描述')}")
            
            # 检查当前案例是否匹配这个规则
            profit = test_case['profit']
            profit_min = rule.get('profit_min')
            profit_max = rule.get('profit_max')
            
            matches = True
            if profit_min is not None and profit < profit_min:
                matches = False
            if profit_max is not None and profit > profit_max:
                matches = False
            
            if matches and rule.get("enabled", True):
                print(f"       ✅ 匹配当前案例（盈利 {profit}）")
            print()

def main():
    """主函数"""
    print("🔧 月度工资检查修复验证")
    print("=" * 60)
    print()
    
    try:
        simulate_monthly_salary_check()
        print("✅ 验证完成")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
