#!/usr/bin/env python3
"""
Deployment verification script
Comprehensive testing of all production features
"""

import asyncio
import sys
import os
import json
import time
import requests
from pathlib import Path
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeploymentVerifier:
    """Verifies deployment and all features"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'overall_success': False
        }
    
    def test_python_environment(self) -> bool:
        """Test Python environment and dependencies"""
        logger.info("🐍 Testing Python environment...")
        
        try:
            # Check Python version
            import sys
            version = sys.version_info
            if version.major >= 3 and version.minor >= 9:
                logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro}")
            else:
                logger.error(f"❌ Python version too old: {version}")
                return False
            
            # Test critical imports
            critical_modules = [
                'telegram',
                'gspread',
                'openpyxl',
                'aiofiles',
                'psutil',
                'pytest'
            ]
            
            missing_modules = []
            for module in critical_modules:
                try:
                    __import__(module)
                    logger.info(f"✅ {module}")
                except ImportError:
                    logger.error(f"❌ {module} not found")
                    missing_modules.append(module)
            
            if missing_modules:
                logger.error(f"Missing modules: {missing_modules}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Python environment test failed: {e}")
            return False
    
    def test_file_structure(self) -> bool:
        """Test required file structure"""
        logger.info("📁 Testing file structure...")
        
        try:
            required_files = [
                'Group_record.py',
                'storage.py',
                'config.py',
                'requirements.txt',
                'logging_config.py',
                'monitoring.py',
                'health_check.py',
                'deploy.py'
            ]
            
            required_dirs = [
                'tests',
                'tests/unit',
                'tests/integration',
                'docker'
            ]
            
            missing_files = []
            for file_path in required_files:
                if not (self.project_root / file_path).exists():
                    missing_files.append(file_path)
                    logger.error(f"❌ Missing file: {file_path}")
                else:
                    logger.info(f"✅ {file_path}")
            
            missing_dirs = []
            for dir_path in required_dirs:
                if not (self.project_root / dir_path).exists():
                    missing_dirs.append(dir_path)
                    logger.error(f"❌ Missing directory: {dir_path}")
                else:
                    logger.info(f"✅ {dir_path}/")
            
            return len(missing_files) == 0 and len(missing_dirs) == 0
            
        except Exception as e:
            logger.error(f"❌ File structure test failed: {e}")
            return False
    
    def test_configuration(self) -> bool:
        """Test configuration files"""
        logger.info("⚙️ Testing configuration...")
        
        try:
            # Test config.py import
            from config import Config
            logger.info("✅ Config module imported")
            
            # Check required config attributes
            required_attrs = [
                'EXCEL_FILE',
                'REBATE_FILE',
                'SALARY_FILE',
                'GOOGLE_SHEET_NAME',
                'CREDENTIALS_FILE'
            ]
            
            missing_attrs = []
            for attr in required_attrs:
                if not hasattr(Config, attr):
                    missing_attrs.append(attr)
                    logger.error(f"❌ Missing config: {attr}")
                else:
                    logger.info(f"✅ {attr}")
            
            # Check .env template
            env_template = self.project_root / ".env.template"
            if env_template.exists():
                logger.info("✅ .env.template exists")
            else:
                logger.warning("⚠️ .env.template not found")
            
            return len(missing_attrs) == 0
            
        except Exception as e:
            logger.error(f"❌ Configuration test failed: {e}")
            return False
    
    async def test_core_modules(self) -> bool:
        """Test core module functionality"""
        logger.info("🧩 Testing core modules...")
        
        try:
            # Test data models
            from data_models import GameRecord, DataValidator
            
            test_record = GameRecord(
                msg_time=datetime.now(),
                work_date='2025-01-01',
                person='TestUser',
                venue='TestVenue',
                game='BJ',
                principal=1000,
                profit=500
            )
            
            validation_result = DataValidator.validate_game_record(test_record)
            if validation_result.success:
                logger.info("✅ Data models working")
            else:
                logger.error(f"❌ Data validation failed: {validation_result.message}")
                return False
            
            # Test error handling
            from error_handling import ErrorHandler, StorageError
            
            error_handler = ErrorHandler()
            test_error = StorageError("Test error")
            error_handler.log_error(test_error, "test_context")
            
            stats = error_handler.get_error_stats()
            if stats['recent_errors_count'] > 0:
                logger.info("✅ Error handling working")
            else:
                logger.error("❌ Error handling not working")
                return False
            
            # Test monitoring
            from monitoring import get_app_monitor, record_counter
            
            monitor = get_app_monitor()
            record_counter("test.counter", 1)
            
            metrics = monitor.metrics.get_all_metrics()
            if 'counters' in metrics:
                logger.info("✅ Monitoring working")
            else:
                logger.error("❌ Monitoring not working")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Core modules test failed: {e}")
            return False
    
    async def test_health_check_server(self) -> bool:
        """Test health check server"""
        logger.info("🏥 Testing health check server...")
        
        try:
            from health_check import start_health_check_server
            
            # Start server on a test port
            test_port = 8081
            server = await start_health_check_server("127.0.0.1", test_port)
            
            # Wait a moment for server to start
            await asyncio.sleep(2)
            
            # Test endpoints
            base_url = f"http://127.0.0.1:{test_port}"
            endpoints = ['/ping', '/health', '/status']
            
            all_working = True
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{base_url}{endpoint}", timeout=5)
                    if response.status_code == 200:
                        logger.info(f"✅ {endpoint} working")
                    else:
                        logger.error(f"❌ {endpoint} returned {response.status_code}")
                        all_working = False
                except requests.RequestException as e:
                    logger.error(f"❌ {endpoint} failed: {e}")
                    all_working = False
            
            return all_working
            
        except Exception as e:
            logger.error(f"❌ Health check server test failed: {e}")
            return False
    
    def test_logging_system(self) -> bool:
        """Test logging system"""
        logger.info("📝 Testing logging system...")
        
        try:
            from logging_config import setup_logging, get_logger
            
            # Setup test logging
            test_logger = setup_logging(
                console_output=False,
                file_output=True,
                json_output=False
            )
            
            # Test logging
            test_logger.info("Test log message")
            test_logger.error("Test error message")
            
            # Check if log files are created
            logs_dir = self.project_root / "logs"
            if logs_dir.exists():
                log_files = list(logs_dir.glob("*.log"))
                if log_files:
                    logger.info(f"✅ Log files created: {len(log_files)}")
                    return True
                else:
                    logger.error("❌ No log files found")
                    return False
            else:
                logger.error("❌ Logs directory not created")
                return False
            
        except Exception as e:
            logger.error(f"❌ Logging system test failed: {e}")
            return False
    
    def test_deployment_scripts(self) -> bool:
        """Test deployment scripts"""
        logger.info("🚀 Testing deployment scripts...")
        
        try:
            # Test deploy.py import
            from deploy import DeploymentManager
            
            deployer = DeploymentManager("development")
            logger.info("✅ DeploymentManager created")
            
            # Test Docker files
            dockerfile = self.project_root / "docker" / "Dockerfile"
            docker_compose = self.project_root / "docker" / "docker-compose.yml"
            
            if dockerfile.exists():
                logger.info("✅ Dockerfile exists")
            else:
                logger.warning("⚠️ Dockerfile not found")
            
            if docker_compose.exists():
                logger.info("✅ docker-compose.yml exists")
            else:
                logger.warning("⚠️ docker-compose.yml not found")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment scripts test failed: {e}")
            return False
    
    def run_unit_tests(self) -> bool:
        """Run unit tests"""
        logger.info("🧪 Running unit tests...")
        
        try:
            import subprocess
            
            # Run pytest
            result = subprocess.run([
                sys.executable, "-m", "pytest",
                "tests/unit/",
                "-v",
                "--tb=short"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✅ Unit tests passed")
                return True
            else:
                logger.error("❌ Unit tests failed")
                logger.error(result.stdout)
                logger.error(result.stderr)
                return False
                
        except Exception as e:
            logger.error(f"❌ Unit tests execution failed: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all verification tests"""
        logger.info("🔍 Starting comprehensive deployment verification...")
        
        tests = [
            ("Python Environment", self.test_python_environment),
            ("File Structure", self.test_file_structure),
            ("Configuration", self.test_configuration),
            ("Core Modules", self.test_core_modules),
            ("Health Check Server", self.test_health_check_server),
            ("Logging System", self.test_logging_system),
            ("Deployment Scripts", self.test_deployment_scripts),
            ("Unit Tests", self.run_unit_tests)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                self.results['tests'][test_name] = {
                    'passed': result,
                    'timestamp': datetime.now().isoformat()
                }
                
                if result:
                    passed += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                logger.error(f"💥 {test_name}: ERROR - {e}")
                self.results['tests'][test_name] = {
                    'passed': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
        
        # Summary
        logger.info(f"\n{'='*60}")
        logger.info("📊 VERIFICATION SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {total - passed}")
        logger.info(f"Success Rate: {(passed/total)*100:.1f}%")
        
        self.results['overall_success'] = passed == total
        self.results['summary'] = {
            'total': total,
            'passed': passed,
            'failed': total - passed,
            'success_rate': (passed/total)*100
        }
        
        # Save results
        results_file = self.project_root / "verification_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 Results saved to: {results_file}")
        
        if self.results['overall_success']:
            logger.info("🎉 ALL TESTS PASSED! Deployment is ready for production.")
        else:
            logger.error("💥 Some tests failed. Please fix issues before deploying.")
        
        return self.results['overall_success']

async def main():
    """Main verification function"""
    verifier = DeploymentVerifier()
    success = await verifier.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
