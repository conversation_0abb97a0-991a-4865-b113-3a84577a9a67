#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Google Sheet connection and operations
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_connection():
    """Test basic Google Sheet connection"""
    try:
        print("🔗 测试基本Google Sheet连接...")
        
        from config import Config
        import gspread
        from oauth2client.service_account import ServiceAccountCredentials
        
        # 检查凭证文件
        if not os.path.exists(Config.CREDENTIALS_FILE):
            print(f"❌ 凭证文件不存在: {Config.CREDENTIALS_FILE}")
            return False
        
        print(f"✅ 凭证文件存在: {Config.CREDENTIALS_FILE}")
        
        # 创建连接
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        creds = ServiceAccountCredentials.from_json_keyfile_name(Config.CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        
        print("✅ Google Sheets客户端创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 基本连接测试失败: {e}")
        return False

def test_sheet_access():
    """Test access to Group_profit_report sheet"""
    try:
        print("\n📊 测试Group_profit_report表格访问...")
        
        from config import Config
        import gspread
        from oauth2client.service_account import ServiceAccountCredentials
        
        # 创建连接
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        creds = ServiceAccountCredentials.from_json_keyfile_name(Config.CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        
        # 尝试打开表格
        sheet_name = "Group_profit_report"
        sheet = client.open(sheet_name)
        print(f"✅ 成功打开表格: {sheet_name}")
        
        # 访问第一个工作表
        worksheet = sheet.sheet1
        print(f"✅ 成功访问工作表: {worksheet.title}")
        
        # 检查现有内容
        existing_data = worksheet.get_all_values()
        print(f"✅ 工作表当前行数: {len(existing_data)}")
        
        if existing_data:
            print(f"✅ 第一行内容: {existing_data[0]}")
        else:
            print("ℹ️ 工作表为空")
        
        return True
        
    except Exception as e:
        print(f"❌ 表格访问测试失败: {e}")
        return False

def test_write_operations():
    """Test write operations to Google Sheet"""
    try:
        print("\n✏️ 测试Google Sheet写入操作...")
        
        from config import Config
        import gspread
        from oauth2client.service_account import ServiceAccountCredentials
        
        # 创建连接
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        creds = ServiceAccountCredentials.from_json_keyfile_name(Config.CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        
        # 打开表格
        sheet_name = "Group_profit_report"
        sheet = client.open(sheet_name)
        worksheet = sheet.sheet1
        
        # 测试表头设置
        headers = [
            "报告月份", "报告生成时间", "总盈利", "总工资", "总开支", "净利润",
            "盈利成员数", "亏损成员数", "开工总次数", "主要开支类别", "备注"
        ]
        
        print("🧹 清空工作表...")
        clear_response = worksheet.clear()
        print(f"✅ 清空响应: {type(clear_response)}")
        
        print("📝 设置表头...")
        append_response = worksheet.append_row(headers)
        print(f"✅ 追加响应: {type(append_response)}")
        
        # 验证表头是否设置成功
        current_headers = worksheet.row_values(1)
        if current_headers == headers:
            print("✅ 表头设置成功")
        else:
            print(f"❌ 表头设置失败，期望: {headers}")
            print(f"❌ 实际获得: {current_headers}")
            return False
        
        # 测试数据写入
        test_data = [
            "2025-08", 
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "10000", "3000", "2000", "5000", "3", "1", "10", "交通", "测试数据"
        ]
        
        print("📝 写入测试数据...")
        data_response = worksheet.append_row(test_data)
        print(f"✅ 数据写入响应: {type(data_response)}")
        
        # 验证数据是否写入成功
        all_data = worksheet.get_all_values()
        if len(all_data) >= 2 and all_data[1] == test_data:
            print("✅ 测试数据写入成功")
        else:
            print(f"❌ 测试数据写入失败")
            return False
        
        print("✅ 所有写入操作测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 写入操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monthly_report_manager():
    """Test MonthlyReportManager"""
    try:
        print("\n📈 测试MonthlyReportManager...")
        
        from monthlyjob import MonthlyReportManager
        from google_sheets_pool import GoogleSheetsManager, GoogleSheetsConnectionPool
        from monthlyjob import MonthlyReport
        from datetime import datetime
        
        # 创建管理器
        pool = GoogleSheetsConnectionPool()
        sheets_manager = GoogleSheetsManager(pool)
        report_manager = MonthlyReportManager(sheets_manager)
        
        print("✅ MonthlyReportManager创建成功")
        
        # 创建测试报告
        test_report = MonthlyReport(
            report_month="2025-08",
            total_income=10000,
            total_expense=5000,
            net_profit=5000,
            profit_members_count=3,
            loss_members_count=1,
            total_work_sessions=10,
            main_expense_category="交通",
            remark="测试报告"
        )
        
        # 测试记录报告
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success = loop.run_until_complete(report_manager.record_monthly_report(test_report))
            if success:
                print("✅ MonthlyReportManager测试成功")
                return True
            else:
                print("❌ MonthlyReportManager测试失败")
                return False
        finally:
            loop.close()
        
    except Exception as e:
        print(f"❌ MonthlyReportManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Google Sheet连接和操作测试")
    print("=" * 50)
    
    # 测试1: 基本连接
    success1 = test_basic_connection()
    
    # 测试2: 表格访问
    success2 = test_sheet_access() if success1 else False
    
    # 测试3: 写入操作
    success3 = test_write_operations() if success2 else False
    
    # 测试4: MonthlyReportManager
    success4 = test_monthly_report_manager() if success3 else False
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"基本连接: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"表格访问: {'✅ 通过' if success2 else '❌ 失败'}")
    print(f"写入操作: {'✅ 通过' if success3 else '❌ 失败'}")
    print(f"MonthlyReportManager: {'✅ 通过' if success4 else '❌ 失败'}")
    
    if success1 and success2 and success3 and success4:
        print("\n🎉 所有测试通过！Google Sheet配置正确。")
    else:
        print("\n❌ 部分测试失败，请检查上述错误信息。")

if __name__ == "__main__":
    main()
