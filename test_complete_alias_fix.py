#!/usr/bin/env python3
"""
测试完整的别名转换修复
验证月度工资检查模块中场子和游戏类型别名转换的完整效果
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_configs():
    """加载所有配置文件"""
    try:
        # 加载别名配置
        with open('aliases.json', 'r', encoding='utf-8') as f:
            aliases = json.load(f)
        
        # 加载返佣配置
        with open('rebate_config.json', 'r', encoding='utf-8') as f:
            rebate_config = json.load(f)
        
        # 加载工资配置
        with open('salary_config.json', 'r', encoding='utf-8') as f:
            salary_config = json.load(f)
        
        return aliases, rebate_config, salary_config
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return {}, {}, {}

def resolve_with_aliases_simple(value, alias_dict, fallback=None):
    """简化版别名解析函数"""
    if not value:
        return fallback
    
    value_clean = value.strip().lower()
    if not value_clean:
        return fallback
    
    # 构建映射表
    mapping = {std.lower(): std for std in alias_dict}
    
    for std, aliases in alias_dict.items():
        for alias in aliases:
            clean_alias = alias.strip().lower()
            if clean_alias:
                mapping[clean_alias] = std
    
    if value_clean in mapping:
        return mapping[value_clean]
    
    return value_clean

def get_rebate_ratio_simple(rebate_config, venue, person):
    """简化版获取返佣比例"""
    # 先查找场子特定配置
    if venue in rebate_config:
        venue_config = rebate_config[venue]
        if isinstance(venue_config, dict):
            # 查找人员特定比例
            if person in venue_config:
                return venue_config[person]
            # 查找场子默认比例
            if "默认比例" in venue_config:
                return venue_config["默认比例"]
        else:
            # 直接是比例值
            return venue_config
    
    # 使用全局默认比例
    return rebate_config.get("默认比例", 0.1)

def find_salary_rule(salary_config, rebate_ratio, game, profit):
    """查找工资规则"""
    rebate_key = str(rebate_ratio)
    if rebate_key not in salary_config:
        return None
    
    if game not in salary_config[rebate_key]:
        return None
    
    rules = salary_config[rebate_key][game]
    
    # 遍历规则，找到匹配的盈利范围
    for rule in rules:
        if not rule.get("enabled", True):
            continue
        
        profit_min = rule.get("profit_min")
        profit_max = rule.get("profit_max")
        
        # 检查盈利是否在范围内
        if profit_min is not None and profit < profit_min:
            continue
        if profit_max is not None and profit > profit_max:
            continue
        
        return rule.get("salary", 0)
    
    return None

def simulate_complete_workflow():
    """模拟完整的月度工资检查工作流"""
    print("🔍 模拟完整的月度工资检查工作流")
    print("=" * 60)
    
    # 加载配置
    aliases, rebate_config, salary_config = load_configs()
    venue_aliases = aliases.get('venues', {})
    game_aliases = aliases.get('games', {})
    
    # 模拟Excel中的问题数据（使用别名）
    test_cases = [
        {
            "row": 614,
            "person": "吴风",
            "venue": "merit1",      # 别名，应该转换为 Merit北塞
            "game": "uth",          # 别名，应该转换为 UTH
            "profit": -1600,
            "current_salary": 15,
            "description": "原始问题案例"
        },
        {
            "row": 500,
            "person": "俊",
            "venue": "iveria",      # 别名，应该转换为 Iveria
            "game": "bj",           # 别名，应该转换为 BJ
            "profit": 500,
            "current_salary": 10,
            "description": "Iveria场子BJ游戏"
        },
        {
            "row": 300,
            "person": "敏",
            "venue": "otium",       # 别名，应该转换为 Otium
            "game": "俄",           # 别名，应该转换为 俄罗斯
            "profit": 800,
            "current_salary": 20,
            "description": "Otium场子俄罗斯游戏"
        }
    ]
    
    for case in test_cases:
        print(f"📊 测试案例：{case['description']}（第{case['row']}行）")
        print(f"  人员: {case['person']}")
        print(f"  原始场子: '{case['venue']}'")
        print(f"  原始游戏: '{case['game']}'")
        print(f"  盈利: {case['profit']}")
        print(f"  当前工资: {case['current_salary']}")
        print()
        
        # 修复前的逻辑（不进行别名转换）
        print("❌ 修复前的逻辑:")
        rebate_ratio_before = get_rebate_ratio_simple(rebate_config, case['venue'], case['person'])
        salary_before = find_salary_rule(salary_config, rebate_ratio_before, case['game'], case['profit'])
        expected_salary_before = salary_before if salary_before is not None else 0
        
        print(f"  场子: '{case['venue']}' (未转换)")
        print(f"  游戏: '{case['game']}' (未转换)")
        print(f"  返佣比例: {rebate_ratio_before}")
        print(f"  工资规则查找结果: {salary_before}")
        print(f"  预期工资: {expected_salary_before}")
        print(f"  是否需要更新: {'是' if case['current_salary'] != expected_salary_before else '否'}")
        print()
        
        # 修复后的逻辑（进行别名转换）
        print("✅ 修复后的逻辑:")
        resolved_venue = resolve_with_aliases_simple(case['venue'], venue_aliases, case['venue'])
        resolved_game = resolve_with_aliases_simple(case['game'], game_aliases, case['game'])
        rebate_ratio_after = get_rebate_ratio_simple(rebate_config, resolved_venue, case['person'])
        salary_after = find_salary_rule(salary_config, rebate_ratio_after, resolved_game, case['profit'])
        expected_salary_after = salary_after if salary_after is not None else 0
        
        print(f"  场子: '{case['venue']}' -> '{resolved_venue}'")
        print(f"  游戏: '{case['game']}' -> '{resolved_game}'")
        print(f"  返佣比例: {rebate_ratio_after}")
        print(f"  工资规则查找结果: {salary_after}")
        print(f"  预期工资: {expected_salary_after}")
        print(f"  是否需要更新: {'是' if case['current_salary'] != expected_salary_after else '否'}")
        print()
        
        # 结果对比
        print("📈 修复效果对比:")
        print(f"  修复前: 返佣比例 {rebate_ratio_before}, 工资 {expected_salary_before}")
        print(f"  修复后: 返佣比例 {rebate_ratio_after}, 工资 {expected_salary_after}")
        
        if rebate_ratio_before != rebate_ratio_after:
            print(f"  🔄 返佣比例发生变化: {rebate_ratio_before} -> {rebate_ratio_after}")
        
        if expected_salary_before != expected_salary_after:
            print(f"  💰 工资计算发生变化: {expected_salary_before} -> {expected_salary_after}")
            if case['current_salary'] == expected_salary_after:
                print(f"  ✅ 修复成功！避免了错误的工资更新")
            else:
                print(f"  ⚠️ 仍需要工资更新，但计算更准确")
        else:
            print(f"  ℹ️ 工资计算结果相同")
        
        print("=" * 60)
        print()

def main():
    """主函数"""
    print("🔧 完整别名转换修复验证")
    print("=" * 60)
    print()
    
    try:
        simulate_complete_workflow()
        print("✅ 验证完成")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
