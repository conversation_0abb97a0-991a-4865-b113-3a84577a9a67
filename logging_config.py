#!/usr/bin/env python3
"""
Logging configuration module
Provides centralized logging setup with different levels and outputs
"""
import asyncio
import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional
import json

from config import Config

class ColoredFormatter(logging.Formatter):
    """Colored console formatter"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)

class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry, ensure_ascii=False)

class LoggingManager:
    """Centralized logging manager"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self.loggers = {}
        self.handlers = {}
        
        # Default configuration
        self.config = {
            'version': 1,
            'disable_existing_loggers': False,
            'level': getattr(Config, 'LOG_LEVEL', 'INFO'),
            'console_level': getattr(Config, 'CONSOLE_LOG_LEVEL', 'INFO'),
            'file_level': getattr(Config, 'FILE_LOG_LEVEL', 'DEBUG'),
            'max_file_size': getattr(Config, 'LOG_MAX_FILE_SIZE', 10 * 1024 * 1024),  # 10MB
            'backup_count': getattr(Config, 'LOG_BACKUP_COUNT', 5),
            'format': {
                'console': '%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
                'file': '%(asctime)s [%(levelname)8s] %(name)s:%(lineno)d - %(message)s',
                'json': None  # Will use JSONFormatter
            }
        }
    
    def setup_logging(self, 
                     console_output: bool = True,
                     file_output: bool = True,
                     json_output: bool = False,
                     telegram_logging: bool = False) -> logging.Logger:
        """Setup logging configuration"""
        
        # Get root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        if console_output:
            console_handler = self._create_console_handler()
            root_logger.addHandler(console_handler)
        
        # File handler
        if file_output:
            file_handler = self._create_file_handler()
            root_logger.addHandler(file_handler)
        
        # JSON file handler
        if json_output:
            json_handler = self._create_json_handler()
            root_logger.addHandler(json_handler)
        
        # Telegram handler (for critical errors)
        if telegram_logging:
            telegram_handler = self._create_telegram_handler()
            root_logger.addHandler(telegram_handler)
        
        # Setup specific loggers
        self._setup_module_loggers()
        
        return root_logger
    
    def _create_console_handler(self) -> logging.Handler:
        """Create console handler"""
        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(getattr(logging, self.config['console_level']))
        
        # Use colored formatter for console
        formatter = ColoredFormatter(
            fmt=self.config['format']['console'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        return handler
    
    def _create_file_handler(self) -> logging.Handler:
        """Create rotating file handler"""
        log_file = self.log_dir / f"telegram_bot_{datetime.now().strftime('%Y%m%d')}.log"
        
        handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=self.config['max_file_size'],
            backupCount=self.config['backup_count'],
            encoding='utf-8'
        )
        handler.setLevel(getattr(logging, self.config['file_level']))
        
        formatter = logging.Formatter(
            fmt=self.config['format']['file'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        return handler
    
    def _create_json_handler(self) -> logging.Handler:
        """Create JSON file handler"""
        log_file = self.log_dir / f"telegram_bot_{datetime.now().strftime('%Y%m%d')}.json"
        
        handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=self.config['max_file_size'],
            backupCount=self.config['backup_count'],
            encoding='utf-8'
        )
        handler.setLevel(logging.DEBUG)
        
        formatter = JSONFormatter()
        handler.setFormatter(formatter)
        
        return handler
    
    def _create_telegram_handler(self) -> logging.Handler:
        """Create Telegram handler for critical errors"""
        try:
            from telegram_log_handler import TelegramHandler
            
            handler = TelegramHandler(
                bot_token=getattr(Config, 'LOG_BOT_TOKEN', None),
                chat_id=getattr(Config, 'LOG_CHAT_ID', None)
            )
            handler.setLevel(logging.CRITICAL)
            
            formatter = logging.Formatter(
                fmt='🚨 CRITICAL ERROR\n%(asctime)s\n%(name)s:%(lineno)d\n%(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            
            return handler
        except ImportError:
            # Telegram handler not available
            return None
    
    def _setup_module_loggers(self):
        """Setup specific module loggers"""
        module_configs = {
            'storage_service': {'level': 'INFO'},
            'google_sheets_pool': {'level': 'INFO'},
            'async_file_ops': {'level': 'INFO'},
            'config_manager': {'level': 'INFO'},
            'error_handling': {'level': 'WARNING'},
            'security_utils': {'level': 'WARNING'},
            'Group_record': {'level': 'INFO'},
            'commander': {'level': 'INFO'},
            'Parser': {'level': 'DEBUG'},
            'telegram': {'level': 'WARNING'},
            'gspread': {'level': 'WARNING'},
            'openpyxl': {'level': 'WARNING'},
            'urllib3': {'level': 'WARNING'},
            'httpx': {'level': 'WARNING'}
        }
        
        for module_name, config in module_configs.items():
            logger = logging.getLogger(module_name)
            logger.setLevel(getattr(logging, config['level']))
            self.loggers[module_name] = logger
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger for a specific module"""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]
    
    def set_log_level(self, level: str, logger_name: Optional[str] = None):
        """Set log level for specific logger or root logger"""
        log_level = getattr(logging, level.upper())
        
        if logger_name:
            logger = self.get_logger(logger_name)
            logger.setLevel(log_level)
        else:
            logging.getLogger().setLevel(log_level)
    
    def add_context_filter(self, logger_name: str, context: Dict):
        """Add context filter to logger"""
        logger = self.get_logger(logger_name)
        
        class ContextFilter(logging.Filter):
            def filter(self, record):
                for key, value in context.items():
                    setattr(record, key, value)
                return True
        
        logger.addFilter(ContextFilter())
    
    def get_log_stats(self) -> Dict:
        """Get logging statistics"""
        stats = {
            'log_directory': str(self.log_dir),
            'log_files': [],
            'total_size': 0,
            'loggers_count': len(self.loggers),
            'handlers_count': len(logging.getLogger().handlers)
        }
        
        # Get log files info
        for log_file in self.log_dir.glob("*.log"):
            file_stats = log_file.stat()
            stats['log_files'].append({
                'name': log_file.name,
                'size': file_stats.st_size,
                'modified': datetime.fromtimestamp(file_stats.st_mtime).isoformat()
            })
            stats['total_size'] += file_stats.st_size
        
        return stats
    
    def cleanup_old_logs(self, days: int = 30):
        """Clean up log files older than specified days"""
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        cleaned_files = []
        
        for log_file in self.log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    cleaned_files.append(str(log_file))
                except OSError as e:
                    logging.warning(f"Failed to delete log file {log_file}: {e}")
        
        if cleaned_files:
            logging.info(f"Cleaned up {len(cleaned_files)} old log files")
        
        return cleaned_files

# Global logging manager instance
_logging_manager = None

def get_logging_manager() -> LoggingManager:
    """Get global logging manager instance"""
    global _logging_manager
    
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    
    return _logging_manager

def setup_logging(console_output: bool = True,
                 file_output: bool = True,
                 json_output: bool = False,
                 telegram_logging: bool = False) -> logging.Logger:
    """Setup logging (convenience function)"""
    manager = get_logging_manager()
    return manager.setup_logging(console_output, file_output, json_output, telegram_logging)

def get_logger(name: str) -> logging.Logger:
    """Get logger (convenience function)"""
    manager = get_logging_manager()
    return manager.get_logger(name)

# Performance logging decorator
def log_performance(logger_name: str = None):
    """Decorator to log function performance"""
    def decorator(func):
        import time
        import functools
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{func.__name__} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func.__name__} failed after {duration:.3f}s: {e}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"{func.__name__} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{func.__name__} failed after {duration:.3f}s: {e}")
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
