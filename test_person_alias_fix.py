#!/usr/bin/env python3
"""
测试人员别名转换修复
验证月度工资检查模块中的人员别名转换是否正常工作
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_configs():
    """加载所有配置文件"""
    try:
        # 加载别名配置
        with open('aliases.json', 'r', encoding='utf-8') as f:
            aliases = json.load(f)
        
        # 加载返佣配置
        with open('rebate_config.json', 'r', encoding='utf-8') as f:
            rebate_config = json.load(f)
        
        return aliases, rebate_config
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return {}, {}

def resolve_with_aliases_simple(value, alias_dict, fallback=None):
    """简化版别名解析函数"""
    if not value:
        return fallback
    
    value_clean = value.strip().lower()
    if not value_clean:
        return fallback
    
    # 构建映射表
    mapping = {std.lower(): std for std in alias_dict}
    
    for std, aliases in alias_dict.items():
        for alias in aliases:
            clean_alias = alias.strip().lower()
            if clean_alias:
                mapping[clean_alias] = std
    
    if value_clean in mapping:
        return mapping[value_clean]
    
    return value_clean

def get_rebate_ratio_simple(rebate_config, venue, person):
    """简化版获取返佣比例"""
    # 先查找场子特定配置
    if venue in rebate_config:
        venue_config = rebate_config[venue]
        if isinstance(venue_config, dict):
            # 查找人员特定比例
            if person in venue_config:
                return venue_config[person]
            # 查找场子默认比例
            if "默认比例" in venue_config:
                return venue_config["默认比例"]
        else:
            # 直接是比例值
            return venue_config
    
    # 使用全局默认比例
    return rebate_config.get("默认比例", 0.1)

def test_person_alias_resolution():
    """测试人员别名解析"""
    print("👤 测试人员别名解析")
    print("=" * 50)
    
    aliases, _ = load_configs()
    person_aliases = aliases.get('persons', {})
    
    # 测试用例：各种人员别名
    test_cases = [
        ("wf", "吴风"),
        ("wufeng", "吴风"),
        ("吴", "吴风"),
        ("吴风", "吴风"),
        ("min", "敏"),
        ("敏哥", "敏"),
        ("敏", "敏"),
        ("jun", "俊"),
        ("俊哥", "俊"),
        ("小俊", "俊"),
        ("俊", "俊"),
        ("加", "加林"),
        ("林", "加林"),
        ("jalin", "加林"),
        ("嘉陵", "加林"),
        ("加林", "加林"),
        ("unknown_person", "unknown_person")  # 未知人员应该返回原值
    ]
    
    for input_person, expected_output in test_cases:
        resolved_person = resolve_with_aliases_simple(input_person, person_aliases, input_person)
        status = "✅" if resolved_person == expected_output else "❌"
        print(f"{status} {input_person:15} -> {resolved_person:15} (期望: {expected_output})")
    
    print()

def test_rebate_ratio_with_person_alias():
    """测试人员别名转换对返佣比例获取的影响"""
    print("💰 测试人员别名转换对返佣比例的影响")
    print("=" * 50)
    
    aliases, rebate_config = load_configs()
    person_aliases = aliases.get('persons', {})
    
    # 测试用例：使用别名的人员名称
    test_cases = [
        ("Iveria", "jun", "俊"),      # jun -> 俊
        ("Iveria", "俊哥", "俊"),     # 俊哥 -> 俊
        ("Iveria", "小俊", "俊"),     # 小俊 -> 俊
        ("Iveria", "俊", "俊"),       # 标准名称
        ("Iveria", "min", "敏"),      # min -> 敏
        ("Iveria", "敏哥", "敏"),     # 敏哥 -> 敏
        ("Iveria", "敏", "敏"),       # 标准名称
        ("GB", "wf", "吴风"),         # wf -> 吴风
        ("GB", "wufeng", "吴风"),     # wufeng -> 吴风
        ("GB", "吴", "吴风"),         # 吴 -> 吴风
        ("GB", "吴风", "吴风"),       # 标准名称
    ]
    
    for venue, person_input, expected_person in test_cases:
        # 转换前：直接使用原始人员名称
        ratio_before = get_rebate_ratio_simple(rebate_config, venue, person_input)
        
        # 转换后：使用别名转换后的人员名称
        resolved_person = resolve_with_aliases_simple(person_input, person_aliases, person_input)
        ratio_after = get_rebate_ratio_simple(rebate_config, venue, resolved_person)
        
        print(f"场子: {venue}, 人员: '{person_input}' -> '{resolved_person}'")
        print(f"  转换前返佣比例: {ratio_before}")
        print(f"  转换后返佣比例: {ratio_after}")
        
        if ratio_before != ratio_after:
            print(f"  ⚠️ 人员别名转换影响了返佣比例获取")
        else:
            print(f"  ✅ 返佣比例一致")
        
        # 验证转换是否正确
        if resolved_person == expected_person:
            print(f"  ✅ 人员别名转换正确")
        else:
            print(f"  ❌ 人员别名转换错误，期望: {expected_person}")
        print()

def test_complete_alias_transformation():
    """测试完整的三字段别名转换"""
    print("🔄 测试完整的人员、场子、游戏别名转换")
    print("=" * 50)
    
    aliases, rebate_config = load_configs()
    person_aliases = aliases.get('persons', {})
    venue_aliases = aliases.get('venues', {})
    game_aliases = aliases.get('games', {})
    
    # 模拟Excel中的数据（全部使用别名）
    test_cases = [
        {
            "person": "wf",         # 别名 -> 吴风
            "venue": "merit1",      # 别名 -> Merit北塞
            "game": "uth",          # 别名 -> UTH
            "profit": -1600,
            "description": "全别名案例1"
        },
        {
            "person": "jun",        # 别名 -> 俊
            "venue": "iveria",      # 别名 -> Iveria
            "game": "bj",           # 别名 -> BJ
            "profit": 500,
            "description": "全别名案例2"
        },
        {
            "person": "敏哥",       # 别名 -> 敏
            "venue": "otium",       # 别名 -> Otium
            "game": "俄",           # 别名 -> 俄罗斯
            "profit": 800,
            "description": "全别名案例3"
        },
        {
            "person": "小俊",       # 别名 -> 俊
            "venue": "gb",          # 别名 -> GB
            "game": "21点",         # 别名 -> BJ
            "profit": 1000,
            "description": "全别名案例4"
        }
    ]
    
    for case in test_cases:
        print(f"📊 {case['description']}")
        print(f"  原始数据:")
        print(f"    人员: '{case['person']}'")
        print(f"    场子: '{case['venue']}'")
        print(f"    游戏: '{case['game']}'")
        print(f"    盈利: {case['profit']}")
        
        # 应用别名转换
        resolved_person = resolve_with_aliases_simple(case['person'], person_aliases, case['person'])
        resolved_venue = resolve_with_aliases_simple(case['venue'], venue_aliases, case['venue'])
        resolved_game = resolve_with_aliases_simple(case['game'], game_aliases, case['game'])
        
        print(f"  转换后数据:")
        print(f"    人员: '{case['person']}' -> '{resolved_person}'")
        print(f"    场子: '{case['venue']}' -> '{resolved_venue}'")
        print(f"    游戏: '{case['game']}' -> '{resolved_game}'")
        
        # 获取返佣比例（使用转换后的数据）
        rebate_ratio = get_rebate_ratio_simple(rebate_config, resolved_venue, resolved_person)
        print(f"    返佣比例: {rebate_ratio}")
        print()

def main():
    """主测试函数"""
    print("🔧 人员别名转换修复测试")
    print("=" * 60)
    print()
    
    try:
        # 测试人员别名解析
        test_person_alias_resolution()
        
        # 测试返佣比例获取
        test_rebate_ratio_with_person_alias()
        
        # 测试完整别名转换
        test_complete_alias_transformation()
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
