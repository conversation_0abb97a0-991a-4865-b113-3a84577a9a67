#!/usr/bin/env python3
"""
Deployment automation script
Handles deployment, configuration, and environment setup
"""

import os
import sys
import subprocess
import shutil
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeploymentManager:
    """Manages deployment process"""
    
    def __init__(self, environment: str = "production"):
        self.environment = environment
        self.project_root = Path(__file__).parent
        self.deploy_dir = self.project_root / "deploy"
        self.backup_dir = self.project_root / "backups"
        self.logs_dir = self.project_root / "logs"
        
        # Create directories
        self.deploy_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # Deployment configuration
        self.config = {
            "development": {
                "python_version": "3.9+",
                "required_packages": [
                    "python-telegram-bot>=20.7",
                    "python-dotenv>=1.0.0",
                    "google-api-python-client>=2.169.0",
                    "gspread>=6.2.1",
                    "oauth2client>=4.1.3",
                    "openpyxl>=3.1.5",
                    "matplotlib>=3.10.3",
                    "aiofiles>=23.2.1",
                    "psutil>=5.9.0",
                    "pytest>=7.0.0",
                    "pytest-asyncio>=0.21.0",
                    "pytest-cov>=4.0.0"
                ],
                "services": [],
                "env_file": ".env.dev"
            },
            "production": {
                "python_version": "3.9+",
                "required_packages": [
                    "python-telegram-bot>=20.7",
                    "python-dotenv>=1.0.0",
                    "google-api-python-client>=2.169.0",
                    "gspread>=6.2.1",
                    "oauth2client>=4.1.3",
                    "openpyxl>=3.1.5",
                    "matplotlib>=3.10.3",
                    "aiofiles>=23.2.1",
                    "psutil>=5.9.0"
                ],
                "services": ["telegram-bot"],
                "env_file": ".env.prod"
            }
        }
    
    def run_command(self, cmd: List[str], description: str, check: bool = True) -> subprocess.CompletedProcess:
        """Run a command and handle errors"""
        logger.info(f"🔄 {description}...")
        logger.debug(f"Command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                check=check,
                cwd=self.project_root
            )
            
            if result.stdout:
                logger.debug(f"Output: {result.stdout}")
            
            logger.info(f"✅ {description} completed")
            return result
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {description} failed")
            logger.error(f"Error: {e.stderr}")
            if e.stdout:
                logger.error(f"Output: {e.stdout}")
            raise
        except FileNotFoundError:
            logger.error(f"❌ Command not found: {cmd[0]}")
            raise
    
    def check_python_version(self) -> bool:
        """Check Python version"""
        logger.info("🐍 Checking Python version...")
        
        try:
            result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
            version_str = result.stdout.strip()
            logger.info(f"Python version: {version_str}")
            
            # Extract version numbers
            version_parts = version_str.split()[1].split('.')
            major, minor = int(version_parts[0]), int(version_parts[1])
            
            if major >= 3 and minor >= 9:
                logger.info("✅ Python version is compatible")
                return True
            else:
                logger.error("❌ Python 3.9+ is required")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to check Python version: {e}")
            return False
    
    def create_virtual_environment(self) -> bool:
        """Create virtual environment"""
        venv_path = self.project_root / "venv"
        
        if venv_path.exists():
            logger.info("📦 Virtual environment already exists")
            return True
        
        try:
            self.run_command(
                [sys.executable, "-m", "venv", str(venv_path)],
                "Creating virtual environment"
            )
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create virtual environment: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        logger.info("📦 Installing dependencies...")
        
        # Determine pip executable
        if os.name == 'nt':  # Windows
            pip_exe = self.project_root / "venv" / "Scripts" / "pip.exe"
        else:  # Unix/Linux
            pip_exe = self.project_root / "venv" / "bin" / "pip"
        
        if not pip_exe.exists():
            pip_exe = sys.executable + " -m pip"
        
        try:
            # Upgrade pip first
            self.run_command([str(pip_exe), "install", "--upgrade", "pip"], "Upgrading pip")
            
            # Install from requirements.txt if it exists
            requirements_file = self.project_root / "requirements.txt"
            if requirements_file.exists():
                self.run_command(
                    [str(pip_exe), "install", "-r", str(requirements_file)],
                    "Installing from requirements.txt"
                )
            else:
                # Install packages individually
                packages = self.config[self.environment]["required_packages"]
                for package in packages:
                    self.run_command(
                        [str(pip_exe), "install", package],
                        f"Installing {package}"
                    )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to install dependencies: {e}")
            return False
    
    def setup_configuration(self) -> bool:
        """Setup configuration files"""
        logger.info("⚙️ Setting up configuration...")
        
        try:
            # Copy environment file
            env_template = self.config[self.environment]["env_file"]
            env_source = self.project_root / f"{env_template}.template"
            env_target = self.project_root / ".env"
            
            if env_source.exists() and not env_target.exists():
                shutil.copy2(env_source, env_target)
                logger.info(f"✅ Created {env_target} from template")
                logger.warning("⚠️ Please edit .env file with your actual configuration")
            
            # Create logs directory
            self.logs_dir.mkdir(exist_ok=True)
            
            # Create data directories
            data_dir = self.project_root / "data"
            data_dir.mkdir(exist_ok=True)
            
            # Set up log rotation
            self._setup_log_rotation()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup configuration: {e}")
            return False
    
    def _setup_log_rotation(self):
        """Setup log rotation configuration"""
        if os.name != 'nt':  # Unix/Linux only
            logrotate_config = f"""
{self.logs_dir}/*.log {{
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 {os.getenv('USER', 'root')} {os.getenv('USER', 'root')}
}}
"""
            
            logrotate_file = self.deploy_dir / "telegram-bot-logrotate"
            with open(logrotate_file, 'w') as f:
                f.write(logrotate_config)
            
            logger.info("✅ Log rotation configuration created")
    
    def run_tests(self) -> bool:
        """Run tests before deployment"""
        logger.info("🧪 Running tests...")
        
        try:
            # Run unit tests
            self.run_command(
                [sys.executable, "run_tests.py", "--fast"],
                "Running fast tests"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Tests failed: {e}")
            return False
    
    def create_backup(self) -> Optional[str]:
        """Create backup of current deployment"""
        logger.info("💾 Creating backup...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{self.environment}_{timestamp}"
            backup_path = self.backup_dir / backup_name
            
            # Files to backup
            backup_files = [
                "*.py",
                "*.json",
                "*.xlsx",
                ".env",
                "logs/",
                "data/"
            ]
            
            backup_path.mkdir(exist_ok=True)
            
            for pattern in backup_files:
                for file_path in self.project_root.glob(pattern):
                    if file_path.is_file():
                        shutil.copy2(file_path, backup_path)
                    elif file_path.is_dir() and not file_path.name.startswith('.'):
                        shutil.copytree(file_path, backup_path / file_path.name, dirs_exist_ok=True)
            
            logger.info(f"✅ Backup created: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"❌ Failed to create backup: {e}")
            return None
    
    def create_systemd_service(self) -> bool:
        """Create systemd service file (Linux only)"""
        if os.name == 'nt':
            logger.info("⏭️ Skipping systemd service creation on Windows")
            return True
        
        logger.info("🔧 Creating systemd service...")
        
        try:
            service_content = f"""[Unit]
Description=Telegram Bot Group Message Grabber
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'root')}
WorkingDirectory={self.project_root}
Environment=PATH={self.project_root}/venv/bin
ExecStart={self.project_root}/venv/bin/python Group_record.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
            
            service_file = self.deploy_dir / "telegram-bot.service"
            with open(service_file, 'w') as f:
                f.write(service_content)
            
            logger.info(f"✅ Systemd service file created: {service_file}")
            logger.info("To install the service, run:")
            logger.info(f"  sudo cp {service_file} /etc/systemd/system/")
            logger.info("  sudo systemctl daemon-reload")
            logger.info("  sudo systemctl enable telegram-bot")
            logger.info("  sudo systemctl start telegram-bot")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create systemd service: {e}")
            return False
    
    def create_windows_service(self) -> bool:
        """Create Windows service script"""
        if os.name != 'nt':
            logger.info("⏭️ Skipping Windows service creation on non-Windows")
            return True
        
        logger.info("🔧 Creating Windows service script...")
        
        try:
            # Create batch file to run the bot
            batch_content = f"""@echo off
cd /d "{self.project_root}"
"{self.project_root}\\venv\\Scripts\\python.exe" Group_record.py
pause
"""
            
            batch_file = self.deploy_dir / "run_telegram_bot.bat"
            with open(batch_file, 'w') as f:
                f.write(batch_content)
            
            # Create service installation script
            service_script = f"""@echo off
echo Installing Telegram Bot as Windows Service...
sc create "TelegramBot" binPath= "{batch_file}" start= auto
sc description "TelegramBot" "Telegram Bot Group Message Grabber"
echo Service installed. Use 'sc start TelegramBot' to start the service.
pause
"""
            
            service_file = self.deploy_dir / "install_service.bat"
            with open(service_file, 'w') as f:
                f.write(service_script)
            
            logger.info(f"✅ Windows service scripts created:")
            logger.info(f"  Run script: {batch_file}")
            logger.info(f"  Service installer: {service_file}")
            logger.info("To install as service, run install_service.bat as Administrator")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create Windows service: {e}")
            return False
    
    def deploy(self, skip_tests: bool = False, skip_backup: bool = False) -> bool:
        """Main deployment process"""
        logger.info(f"🚀 Starting deployment for {self.environment} environment")
        
        try:
            # Pre-deployment checks
            if not self.check_python_version():
                return False
            
            # Create backup
            if not skip_backup:
                backup_path = self.create_backup()
                if not backup_path:
                    logger.warning("⚠️ Backup creation failed, continuing anyway...")
            
            # Setup environment
            if not self.create_virtual_environment():
                return False
            
            if not self.install_dependencies():
                return False
            
            if not self.setup_configuration():
                return False
            
            # Run tests
            if not skip_tests:
                if not self.run_tests():
                    logger.error("❌ Tests failed, deployment aborted")
                    return False
            
            # Create service files
            if self.environment == "production":
                self.create_systemd_service()
                self.create_windows_service()
            
            logger.info("🎉 Deployment completed successfully!")
            logger.info("\n📋 Next steps:")
            logger.info("1. Edit .env file with your configuration")
            logger.info("2. Add your Google Sheets credentials")
            logger.info("3. Test the bot manually")
            
            if self.environment == "production":
                logger.info("4. Install and start the system service")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Deployment failed: {e}")
            return False
    
    def rollback(self, backup_path: str) -> bool:
        """Rollback to a previous backup"""
        logger.info(f"🔄 Rolling back to {backup_path}...")
        
        try:
            backup_dir = Path(backup_path)
            if not backup_dir.exists():
                logger.error(f"❌ Backup directory not found: {backup_path}")
                return False
            
            # Stop service if running
            self.stop_service()
            
            # Restore files
            for item in backup_dir.iterdir():
                target = self.project_root / item.name
                if item.is_file():
                    shutil.copy2(item, target)
                elif item.is_dir():
                    if target.exists():
                        shutil.rmtree(target)
                    shutil.copytree(item, target)
            
            logger.info("✅ Rollback completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Rollback failed: {e}")
            return False
    
    def stop_service(self):
        """Stop the service"""
        logger.info("🛑 Stopping service...")
        
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(["sc", "stop", "TelegramBot"], check=False)
            else:  # Linux
                subprocess.run(["sudo", "systemctl", "stop", "telegram-bot"], check=False)
        except Exception:
            pass  # Service might not be installed
    
    def start_service(self):
        """Start the service"""
        logger.info("▶️ Starting service...")
        
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(["sc", "start", "TelegramBot"], check=False)
            else:  # Linux
                subprocess.run(["sudo", "systemctl", "start", "telegram-bot"], check=False)
        except Exception:
            pass  # Service might not be installed

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Deployment automation for Telegram Bot")
    parser.add_argument("--env", choices=["development", "production"], 
                       default="production", help="Deployment environment")
    parser.add_argument("--skip-tests", action="store_true", help="Skip running tests")
    parser.add_argument("--skip-backup", action="store_true", help="Skip creating backup")
    parser.add_argument("--rollback", type=str, help="Rollback to specified backup")
    parser.add_argument("--stop", action="store_true", help="Stop the service")
    parser.add_argument("--start", action="store_true", help="Start the service")
    
    args = parser.parse_args()
    
    deployer = DeploymentManager(args.env)
    
    if args.rollback:
        success = deployer.rollback(args.rollback)
    elif args.stop:
        deployer.stop_service()
        success = True
    elif args.start:
        deployer.start_service()
        success = True
    else:
        success = deployer.deploy(args.skip_tests, args.skip_backup)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
