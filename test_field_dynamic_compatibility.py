#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段动态化兼容性测试脚本
验证从硬编码字段名到动态字段加载的迁移是否成功
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_alias_manager_apis():
    """测试alias_manager中的新API"""
    print("1. 测试alias_manager API...")

    try:
        from alias_manager import (
            get_all_field_names,
            get_numeric_field_names,
            is_numeric_field,
            get_required_field_names,
            validate_aliases_file,
            create_aliases_backup
        )

        # 测试获取所有字段
        all_fields = get_all_field_names()
        print(f"   ✓ 获取所有字段: {all_fields}")
        assert len(all_fields) > 0, "字段列表不能为空"

        # 测试获取数字字段
        numeric_fields = get_numeric_field_names()
        print(f"   ✓ 获取数字字段: {numeric_fields}")
        assert len(numeric_fields) > 0, "数字字段列表不能为空"

        # 测试字段类型判断
        assert is_numeric_field("起始本金") == True, "起始本金应该是数字字段"
        assert is_numeric_field("人员") == False, "人员不应该是数字字段"
        print("   ✓ 字段类型判断正确")

        # 测试必需字段
        required_fields = get_required_field_names()
        print(f"   ✓ 获取必需字段: {required_fields}")
        assert "备注" not in required_fields, "备注不应该是必需字段"

        # 测试文件验证
        is_valid, msg = validate_aliases_file()
        print(f"   ✓ 文件验证: {is_valid}, {msg}")

        print("   [PASS] alias_manager API测试通过")
        return True

    except Exception as e:
        print(f"   ❌ alias_manager API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_models_lazy_loading():
    """测试data_models中的延迟初始化"""
    print("2. 测试data_models延迟初始化...")

    try:
        from data_models import FieldConstants

        field_constants = FieldConstants()

        # 测试字段列表
        fields = field_constants.FIELDS_CHINESE
        print(f"   ✓ FIELDS_CHINESE: {fields}")
        assert len(fields) > 0, "字段列表不能为空"

        # 测试数字字段
        numeric_fields = field_constants.NUMERIC_FIELDS
        print(f"   ✓ NUMERIC_FIELDS: {numeric_fields}")
        assert len(numeric_fields) > 0, "数字字段列表不能为空"

        # 测试必需字段
        required_fields = field_constants.REQUIRED_FIELDS
        print(f"   ✓ REQUIRED_FIELDS: {required_fields}")
        assert "备注" not in required_fields, "备注不应该是必需字段"

        print("   ✅ data_models延迟初始化测试通过")
        return True

    except Exception as e:
        print(f"   ❌ data_models延迟初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parser_dynamic_fields():
    """测试Parser中的动态字段使用"""
    print("3. 测试Parser动态字段...")

    try:
        from Parser import parse_message

        test_msg = '''工作日期：12月24日
人员：吴风
场子：GB
游戏：BJ
卡号：12345
起始本金：1400
点码：10000
工资：50
输反：0
盈利：2075
备注：测试动态字段'''

        fields, missing = parse_message(test_msg, strict=False)
        print(f"   ✓ 解析结果字段数: {len(fields)}")

        # 验证关键字段
        expected_fields = ["工作日期", "人员", "场子", "游戏", "起始本金", "盈利"]
        for field in expected_fields:
            assert field in fields, f"缺少字段: {field}"
            assert fields[field] is not None, f"字段值为空: {field}"

        # 验证数字字段解析
        assert isinstance(fields["起始本金"], int), "起始本金应该是整数"
        assert isinstance(fields["盈利"], int), "盈利应该是整数"
        assert fields["起始本金"] == 1400, "起始本金值不正确"
        assert fields["盈利"] == 2075, "盈利值不正确"

        print("   ✅ Parser动态字段测试通过")
        return True

    except Exception as e:
        print(f"   ❌ Parser动态字段测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("4. 测试向后兼容性...")

    try:
        # 测试原有的常量是否还存在
        from data_models import FIELDS_CHINESE, NUMERIC_FIELDS, REQUIRED_FIELDS

        print(f"   ✓ FIELDS_CHINESE可访问: {len(FIELDS_CHINESE)} 个字段")
        print(f"   ✓ NUMERIC_FIELDS可访问: {len(NUMERIC_FIELDS)} 个字段")
        print(f"   ✓ REQUIRED_FIELDS可访问: {len(REQUIRED_FIELDS)} 个字段")

        # 验证关键字段存在
        assert "起始本金" in FIELDS_CHINESE, "起始本金字段缺失"
        assert "盈利" in FIELDS_CHINESE, "盈利字段缺失"
        assert "起始本金" in NUMERIC_FIELDS, "起始本金不在数字字段中"
        assert "盈利" in NUMERIC_FIELDS, "盈利不在数字字段中"

        print("   ✅ 向后兼容性测试通过")
        return True

    except Exception as e:
        print(f"   ❌ 向后兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("5. 测试错误处理机制...")

    try:
        from alias_manager import safe_get_field_with_fallback

        # 测试正常情况
        all_fields = safe_get_field_with_fallback("all", ["fallback"])
        print(f"   ✓ 正常获取字段: {len(all_fields)} 个字段")
        assert len(all_fields) > 1, "字段数量应该大于fallback"

        # 测试未知类型
        unknown_fields = safe_get_field_with_fallback("unknown", ["fallback"])
        print(f"   ✓ 未知类型fallback: {unknown_fields}")
        assert unknown_fields == ["fallback"], "应该返回fallback列表"

        print("   ✅ 错误处理机制测试通过")
        return True

    except Exception as e:
        print(f"   ❌ 错误处理机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始字段动态化兼容性测试...\n")

    tests = [
        test_alias_manager_apis,
        test_data_models_lazy_loading,
        test_parser_dynamic_fields,
        test_backward_compatibility,
        test_error_handling
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            print()

    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！字段动态化系统运行正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)