import os
from datetime import datetime, timedelta
import re
import json

import openpyxl
from telegram import Update, InputFile
from telegram.ext import ContextTypes

import logging
from Parser import parse_message, get_last_month_range, parse_date_range
from config import Config
from report import generate_and_send_report
from charts import send_profit_trend_chart, send_venue_profit_bar_chart, \
    send_person_profit_bar_chart
from storage import FIELDS, set_rebate_ratio, load_rebate_from_google_sheet, save_rebate_config, \
    load_salary_from_google_sheet, save_salary_config, set_salary_config
from message_cache import message_cache, retry_sync_cached_messages
from config_cache import config_cache, refresh_config_cache
from input_validator import SecurityValidator

EXCEL_FILE = Config.EXCEL_FILE
TIME_ZONE = Config.TIME_ZONE  # 使用 zoneinfo 模块处理时区

logger = logging.getLogger(__name__)

correction_cache = {}  # 临时缓存（聊天ID: 匹配记录列表）


def normalize_date(date_str):
    """
    将各种日期格式标准化为 YYYY-MM-DD
    """
    # 移除空格和特殊字符
    date_str = re.sub(r'[^\d-/.]', '', str(date_str))

    # 尝试多种日期格式
    formats = [
        '%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d',
        '%m-%d', '%m/%d', '%m.%d',
        '%d-%m', '%d/%m', '%d.%m'
    ]

    for fmt in formats:
        try:
            dt = datetime.strptime(date_str, fmt)
            # 如果没有年份，使用当前年份
            if fmt in ['%m-%d', '%m/%d', '%m.%d', '%d-%m', '%d/%m', '%d.%m']:
                dt = dt.replace(year=datetime.now().year)
            return dt.strftime('%Y-%m-%d')
        except:
            continue

    return date_str


def is_date_match(parsed_date, cell_date):
    """
    智能日期匹配
    """
    try:
        # 标准化日期格式
        parsed_normalized = normalize_date(parsed_date)
        cell_normalized = normalize_date(cell_date)

        return parsed_normalized == cell_normalized
    except:
        return False


def calculate_match_score(parsed_data, row_data):
    """
    计算匹配分数，返回 (总分, 精确匹配数)
    """
    score = 0
    exact_matches = 0

    # 权重配置（动态获取字段名）
    try:
        from alias_manager import get_all_field_names
        all_fields = get_all_field_names()

        # 确定关键字段名（检查字段是否存在）
        person_field = "人员" if "人员" in all_fields else None
        card_field = "卡号" if "卡号" in all_fields else None
        date_field = "工作日期" if "工作日期" in all_fields else ("日期" if "日期" in all_fields else None)

        weights = {}
        check_fields = []

        if person_field:
            weights[person_field] = 3  # 人员最重要
            check_fields.append(person_field)
        if card_field:
            weights[card_field] = 2  # 卡号次之
            check_fields.append(card_field)
        if date_field:
            weights[date_field] = 2  # 日期也很重要
            check_fields.append(date_field)

    except Exception as e:
        logger.error(f"获取字段名失败，使用fallback: {e}")
        # Fallback到硬编码
        weights = {"人员": 3, "卡号": 2, "日期": 2}
        check_fields = ["人员", "卡号", "日期"]

    for key in check_fields:
        if not parsed_data.get(key):
            continue

        idx = FIELDS.index(key)
        cell_val = str(row_data[idx + 1].value or "").strip()
        parsed_val = str(parsed_data[key]).strip()

        if not cell_val:
            continue

        # 精确匹配
        if cell_val == parsed_val:
            score += weights[key] * 2  # 精确匹配双倍分数
            exact_matches += 1
        # 模糊匹配（仅对人员字段）
        elif key == person_field and (parsed_val in cell_val or cell_val in parsed_val):
            score += weights[key] * 0.5  # 模糊匹配半分
        # 日期特殊处理
        elif key == date_field:
            if is_date_match(parsed_val, cell_val):
                score += weights[key] * 1.5
                exact_matches += 0.5  # 日期匹配算半个精确匹配

    return score, exact_matches


# --- Command handler ---
from error_strategy import with_error_handling


@with_error_handling(operation_name="daily_report", notify_user=True)
async def daily_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await generate_and_send_report(context, period="daily")


@with_error_handling(operation_name="weekly_report", notify_user=True)
async def weekly_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await generate_and_send_report(context, period="weekly")


@with_error_handling(operation_name="monthly_report", notify_user=True)
async def monthly_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    from exceptions import ValidationError

    args = context.args
    now = datetime.now(TIME_ZONE)
    if now.day == 1:
        # 如果今天是1号，则上个月的报告
        date_result = get_last_month_range()
    else:
        date_result = parse_date_range(args)
    if date_result is None:
        raise ValidationError("日期格式错误，请输入两个合法的日期，例如：/trend 2024-01-01 2024-01-31")
    start, end = date_result
    await generate_and_send_report(context, start, end, "monthly")


# 发送盈亏趋势图
@with_error_handling(operation_name="trend_chart", notify_user=True)
async def trend_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    from exceptions import ValidationError

    args = context.args
    date_result = parse_date_range(args)
    if date_result is None:
        raise ValidationError("日期格式错误，请输入两个合法的日期，例如：/trend 2024-01-01 2024-01-31")
    start, end = date_result
    await send_profit_trend_chart(context, start, end)


@with_error_handling(operation_name="person_report", notify_user=True)
async def person_report_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    from exceptions import ValidationError

    args = context.args
    date_result = parse_date_range(args)
    if date_result is None:
        raise ValidationError("日期格式错误，请输入两个合法的日期，例如：/person_report 2024-01-01 2024-01-31")
    start, end = date_result
    await send_person_profit_bar_chart(context, start, end)


# 如果没有指定时间范围，则默认统计本月
@with_error_handling(operation_name="venue_report", notify_user=True)
async def venue_report_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    from exceptions import ValidationError

    args = context.args
    date_result = parse_date_range(args)
    if date_result is None:
        raise ValidationError("日期格式错误，请输入两个合法的日期，例如：/venue_report 2024-01-01 2024-01-31")
    start, end = date_result
    logger.info(f"生成场子报告: {start} 到 {end}")
    await send_venue_profit_bar_chart(context, start, end)


# 确认更正命令
async def confirm_correction_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    chat_id = update.effective_chat.id
    if chat_id not in correction_cache:
        await update.message.reply_text("⚠️ 没有待确认的更正记录，请先发送‘更正：’消息。")
        return

    args = context.args
    if not args or not args[0].isdigit():
        await update.message.reply_text("❗ 格式应为：/confirm 1（数字为待更正记录编号）")
        return

    idx = int(args[0]) - 1
    rows = correction_cache[chat_id]["rows"]
    parsed = correction_cache[chat_id]["parsed"]

    if idx < 0 or idx >= len(rows):
        await update.message.reply_text("❌ 编号超出范围")
        return

    row_idx, _ = rows[idx]
    wb = openpyxl.load_workbook(EXCEL_FILE)
    ws = wb.active
    row = ws[row_idx]
    for i, field in enumerate(FIELDS):
        if parsed.get(field) is not None:
            row[i + 1].value = parsed[field]

    wb.save(EXCEL_FILE)
    del correction_cache[chat_id]
    await update.message.reply_text(f"✅ 第 {idx + 1} 条记录更正成功")


async def handle_correction(msg, correction_text: str):
    from exceptions import ValidationError, FileSystemError, SystemError
    from error_strategy import handle_telegram_command_error

    chat_id = msg.chat_id
    try:
        parsed, _ = parse_message(correction_text, strict=False)
    except ValueError as e:
        error = ValidationError(f"更正格式错误: {str(e)}")
        await handle_telegram_command_error(error, msg, None, "handle_correction")
        return
    except Exception as e:
        error = SystemError(f"解析更正消息失败: {str(e)}")
        await handle_telegram_command_error(error, msg, None, "handle_correction")
        return

    try:
        wb = openpyxl.load_workbook(EXCEL_FILE)
        ws = wb.active
    except FileNotFoundError:
        error = FileSystemError(f"Excel文件不存在: {EXCEL_FILE}", operation="read")
        await handle_telegram_command_error(error, msg, None, "handle_correction")
        return
    except PermissionError:
        error = FileSystemError(f"Excel文件权限不足: {EXCEL_FILE}", operation="read")
        await handle_telegram_command_error(error, msg, None, "handle_correction")
        return
    except Exception as e:
        error = SystemError(f"打开Excel文件失败: {str(e)}")
        await handle_telegram_command_error(error, msg, None, "handle_correction")
        return

    # 第一步：精确匹配"人员"、"卡号"、"日期"三项
    exact_matched_rows = []

    # 检查是否提供了这三个关键字段
    has_person = person_field and parsed.get(person_field) is not None
    has_card = card_field and parsed.get(card_field) is not None
    has_date = date_field and parsed.get(date_field) is not None

    if has_person and has_card and has_date:
        for row_idx, row in enumerate(ws.iter_rows(min_row=2), start=2):
            person_idx = FIELDS.index(person_field)
            card_idx = FIELDS.index(card_field)
            date_idx = FIELDS.index(date_field)

            person_cell = str(row[person_idx + 1].value or "").strip()
            card_cell = str(row[card_idx + 1].value or "").strip()
            date_cell = str(row[date_idx + 1].value or "").strip()

            parsed_person = str(parsed[person_field]).strip()
            parsed_card = str(parsed[card_field]).strip()
            parsed_date = str(parsed[date_field]).strip()

            # 精确匹配三个字段
            person_match = person_cell == parsed_person
            card_match = card_cell == parsed_card
            date_match = is_date_match(parsed_date, date_cell)

            if person_match and card_match and date_match:
                exact_matched_rows.append((row_idx, row))

    # 如果精确匹配到记录
    if exact_matched_rows:
        if len(exact_matched_rows) == 1:
            # 只有一条记录，直接更改并保存
            row_idx, row = exact_matched_rows[0]
            for i, field in enumerate(FIELDS):
                if parsed.get(field) is not None:
                    row[i + 1].value = parsed[field]
            try:
                wb.save(EXCEL_FILE)
                await msg.reply_text("✅ 更正成功（精确匹配自动更新）")
                return
            except PermissionError:
                error = FileSystemError(f"Excel文件保存权限不足: {EXCEL_FILE}", operation="write")
                await handle_telegram_command_error(error, msg, None, "handle_correction_save")
                return
            except Exception as e:
                error = SystemError(f"保存Excel文件失败: {str(e)}")
                await handle_telegram_command_error(error, msg, None, "handle_correction_save")
                return
        else:
            # 多条记录匹配，提示用户确认
            correction_cache[chat_id] = {
                "rows": exact_matched_rows,
                "parsed": parsed
            }
            preview = []
            for i, (row_idx, row) in enumerate(exact_matched_rows):
                preview.append(f"{i + 1}. {'｜'.join(str(cell.value or '') for cell in row[1:7])}")
            await msg.reply_text(
                "⚠️ 精确匹配到多条记录，请使用 /confirm N 命令确认更改（例如 /confirm 2）：\n" + "\n".join(preview)
            )
            return

    # 第二步：如果没有精确匹配，执行权重查找逻辑
    weighted_matched_rows = []
    best_score = 0

    for row_idx, row in enumerate(ws.iter_rows(min_row=2), start=2):
        score, exact_matches = calculate_match_score(parsed, row)

        # 只保留分数较高的记录
        if score > 0:
            if score > best_score:
                best_score = score
                weighted_matched_rows = [(row_idx, row, score, exact_matches)]
            elif score == best_score:
                weighted_matched_rows.append((row_idx, row, score, exact_matches))

    if not weighted_matched_rows:
        await msg.reply_text("⚠️ 没有找到匹配记录，请检查'人员/卡号/日期'是否填写正确。")
        return

    # 过滤出最佳匹配的记录
    final_matched_rows = [(row_idx, row) for row_idx, row, score, _ in weighted_matched_rows if score == best_score]

    if len(final_matched_rows) == 1:
        row_idx, row = final_matched_rows[0]
        for i, field in enumerate(FIELDS):
            if parsed.get(field) is not None:
                row[i + 1].value = parsed[field]
        wb.save(EXCEL_FILE)
        await msg.reply_text("✅ 更正成功（权重匹配自动更新）")
    else:
        correction_cache[chat_id] = {
            "rows": final_matched_rows,
            "parsed": parsed
        }
        preview = []
        for i, (row_idx, row) in enumerate(final_matched_rows):
            # 显示匹配分数信息
            score_info = next(
                (f"(分数:{score:.1f})" for r_idx, r, score, _ in weighted_matched_rows if r_idx == row_idx), "")
            preview.append(f"{i + 1}. {'｜'.join(str(cell.value or '') for cell in row[1:7])} {score_info}")
        await msg.reply_text(
            "⚠️ 权重匹配到多条记录，请使用 /confirm N 命令确认更改（例如 /confirm 2）：\n" + "\n".join(preview)
        )


# 导出数据命令
async def export_data_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not os.path.exists(EXCEL_FILE):
        await update.message.reply_text("❗ 暂无记录可导出。")
        return
    try:
        await update.message.reply_document(InputFile(EXCEL_FILE), filename="群组记录.xlsx")
    except Exception as e:
        await update.message.reply_text(f"导出失败: {str(e)}")


async def set_rebate_ratio_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await set_rebate_ratio(update, context)


async def add_venue_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """添加新venue的命令处理器"""
    args = context.args

    # 输入验证
    if len(args) < 2:
        await update.message.reply_text(
            "❗ 格式错误：\n"
            "📋 格式：`/add_venue venue名称 别名1 [别名2] [别名3] ...`\n"
            "💡 示例：`/add_venue 新赌场 nc newcasino 新场`\n\n"
            "⚠️ 注意：venue名称和至少一个别名是必需的"
        )
        return

    # 用户授权检查
    is_authorized, error_msg = SecurityValidator.check_user_authorization(update, required_level="admin")
    if not is_authorized:
        await update.message.reply_text(f"❌ 权限不足: {error_msg}")
        return

    venue_name = SecurityValidator.sanitize_input(args[0])
    aliases = [SecurityValidator.sanitize_input(alias) for alias in args[1:]]

    # 验证venue名称
    is_valid, error_msg = SecurityValidator.validate_field('场子', venue_name)
    if not is_valid:
        await update.message.reply_text(f"❗ venue名称无效: {error_msg}")
        return

    # 验证别名
    for alias in aliases:
        is_valid, error_msg = SecurityValidator.validate_field('场子', alias)
        if not is_valid:
            await update.message.reply_text(f"❗ 别名 '{alias}' 无效: {error_msg}")
            return

    try:
        # 使用安全文件管理器读取配置
        from input_validator import SecureFileManager
        alias_data = SecureFileManager.safe_load_json(Config.ALIAS_FILE)

        # 检查venue是否已存在
        if venue_name in alias_data.get('venues', {}):
            await update.message.reply_text(
                f"⚠️ venue '{venue_name}' 已存在\n"
                f"当前别名：{alias_data['venues'][venue_name]}\n\n"
                f"💡 如需修改别名，请手动编辑 aliases.json 文件"
            )
            return

        # 检查别名是否与现有venue或别名冲突
        existing_venues = alias_data.get('venues', {})
        conflicts = []

        # 检查venue名称冲突
        for existing_venue, existing_aliases in existing_venues.items():
            if venue_name.lower() in [alias.lower() for alias in existing_aliases]:
                conflicts.append(f"venue名称 '{venue_name}' 与 '{existing_venue}' 的别名冲突")

        # 检查别名冲突
        for alias in aliases:
            for existing_venue, existing_aliases in existing_venues.items():
                if alias.lower() == existing_venue.lower():
                    conflicts.append(f"别名 '{alias}' 与现有venue '{existing_venue}' 冲突")
                elif alias.lower() in [ea.lower() for ea in existing_aliases]:
                    conflicts.append(f"别名 '{alias}' 与 '{existing_venue}' 的别名冲突")

        if conflicts:
            await update.message.reply_text(
                f"❌ 发现冲突：\n" + "\n".join(f"• {conflict}" for conflict in conflicts) +
                f"\n\n💡 请使用不同的名称或别名"
            )
            return

        # 添加新venue
        if 'venues' not in alias_data:
            alias_data['venues'] = {}

        alias_data['venues'][venue_name] = aliases

        # 使用安全文件管理器保存文件
        SecureFileManager.safe_save_json(alias_data, Config.ALIAS_FILE, backup=True)

        # 成功消息
        aliases_str = "、".join(aliases)
        success_msg = (
            f"✅ venue添加成功！\n\n"
            f"🏢 venue名称：{venue_name}\n"
            f"🏷️ 别名：{aliases_str}\n\n"
            f"📝 aliases.json 文件已更新\n\n"
            f"🔧 **下一步：设置rebate比例**\n"
            f"请使用以下命令为新venue设置输返比例：\n"
            f"`/set_rebate {venue_name} 比例`\n\n"
            f"💡 示例：\n"
            f"• `/set_rebate {venue_name} 0.1` (设置默认10%输返)\n"
            f"• `/set_rebate {venue_name} 0.2` (设置默认20%输返)\n\n"
            f"⚠️ 注意：比例应为0-1之间的小数（如0.1表示10%）"
        )

        await update.message.reply_text(success_msg)
        logger.info(f"管理员 {update.effective_user.id} 添加新venue: {venue_name}")

    except Exception as e:
        error_msg = f"❌ 添加venue失败：{str(e)}"
        await update.message.reply_text(error_msg)
        logger.error(f"添加venue失败: {e}", exc_info=True)


def validate_rebate_config(config: dict) -> tuple[bool, str]:
    """验证 rebate 配置结构的完整性"""
    try:
        issues = []

        # 检查全局默认比例
        global_default = config.get("默认比例")
        if global_default is not None:
            if not isinstance(global_default, (int, float)) or not (0 <= global_default <= 1):
                issues.append(f"全局默认比例无效: {global_default}")

        # 检查场馆配置
        venue_count = 0
        person_count = 0

        for key, value in config.items():
            if key == "默认比例":
                continue

            venue_count += 1

            if not isinstance(value, dict):
                issues.append(f"场馆 '{key}' 配置格式错误: {type(value)}")
                continue

            for person_key, person_value in value.items():
                if not isinstance(person_value, (int, float)) or not (0 <= person_value <= 1):
                    issues.append(f"场馆 '{key}' 人员 '{person_key}' 比例无效: {person_value}")
                else:
                    person_count += 1

        if issues:
            return False, f"配置验证失败:\n" + "\n".join(f"• {issue}" for issue in issues[:5])

        return True, f"配置验证通过: {venue_count} 个场馆, {person_count} 个人员设置"

    except Exception as e:
        return False, f"配置验证异常: {e}"


async def load_rebate_config_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """从Google Sheet中加载rebate配置的命令处理器"""
    try:
        # 发送开始处理的消息
        await update.message.reply_text("🔄 正在从 Google Sheet 加载 rebate 配置...")

        # 从Google Sheet读取配置
        new_config = load_rebate_from_google_sheet()

        # 验证配置结构
        is_valid, validation_msg = validate_rebate_config(new_config)
        if not is_valid:
            error_msg = f"❌ 配置验证失败\n\n{validation_msg}\n\n请检查 Google Sheets 数据格式"
            await update.message.reply_text(error_msg)
            return

        # 保存到本地文件
        save_rebate_config(new_config)

        # 统计配置信息
        venue_count = len([k for k in new_config.keys() if k != "默认比例"])
        person_count = sum(len([pk for pk in v.keys() if pk != "默认比例"])
                           for v in new_config.values() if isinstance(v, dict))

        # 发送成功消息
        success_msg = (
            f"✅ Rebate 配置加载成功！\n\n"
            f"📊 配置统计：\n"
            f"• 场馆数量：{venue_count}\n"
            f"• 个人设置数量：{person_count}\n"
            f"• 全局默认比例：{new_config.get('默认比例', '未设置'):.1%}\n\n"
            f"🔍 {validation_msg}\n\n"
            f"💾 配置已保存到本地文件 rebate_config.json"
        )

        await update.message.reply_text(success_msg)

    except Exception as e:
        error_str = str(e)

        # 检查是否是Google API权限问题
        if "Google Drive API has not been used" in error_str or "403" in error_str:
            error_msg = (
                f"❌ Google Sheets 访问权限不足\n\n"
                f"🔧 解决方案：\n"
                f"1. 请联系管理员启用 Google Drive API\n"
                f"2. 或者手动更新 rebate_config.json 文件\n\n"
                f"📋 当前配置文件格式示例：\n"
                f"```json\n"
                f'{{\n'
                f'  "场馆名": {{\n'
                f'    "默认比例": 0.1,\n'
                f'    "人员名": 0.2\n'
                f'  }},\n'
                f'  "默认比例": 0.1\n'
                f'}}\n'
                f"```\n\n"
                f"💡 提示：可以使用 /set_rebate 命令单独设置各场馆的输返比例"
            )
        elif "未找到" in error_str and "工作表" in error_str:
            error_msg = (
                f"❌ 未找到 rebate 配置工作表\n\n"
                f"🔧 解决方案：\n"
                f"请在 Google Sheet 中创建名为 'rebate_ratio_config' 的工作表，\n"
                f"包含以下列：场子、人员、比例、备注\n\n"
                f"📋 表格格式示例：\n"
                f"场子 | 人员 | 比例 | 备注\n"
                f"GB | * | 0.1 | 默认比例\n"
                f"Iveria | 俊 | 0.2 | 个别设置\n"
                f"默认 | * | 0.1 | 全局默认"
            )
        else:
            error_msg = f"❌ 加载 rebate 配置失败：{error_str}"

        await update.message.reply_text(error_msg)


def validate_salary_config(config: dict) -> tuple[bool, str]:
    """验证 salary 配置结构的完整性"""
    try:
        issues = []
        rebate_count = 0
        total_rules = 0
        game_types = set()

        for rebate_key, rebate_value in config.items():
            rebate_count += 1

            if not isinstance(rebate_value, dict):
                issues.append(f"Rebate '{rebate_key}' 配置格式错误: {type(rebate_value)}")
                continue

            for game_key, game_value in rebate_value.items():
                game_types.add(game_key)

                if not isinstance(game_value, list):
                    issues.append(f"游戏 '{rebate_key}/{game_key}' 规则格式错误: {type(game_value)}")
                    continue

                for i, rule in enumerate(game_value):
                    if not isinstance(rule, dict):
                        issues.append(f"规则 '{rebate_key}/{game_key}[{i}]' 格式错误: {type(rule)}")
                        continue

                    required_fields = ["enabled", "profit_min", "profit_max", "salary"]
                    missing_fields = [field for field in required_fields if field not in rule]
                    if missing_fields:
                        issues.append(f"规则 '{rebate_key}/{game_key}[{i}]' 缺少字段: {missing_fields}")
                    else:
                        total_rules += 1

        if issues:
            return False, f"配置验证失败:\n" + "\n".join(f"• {issue}" for issue in issues[:5])

        return True, f"配置验证通过: {rebate_count} 个rebate类型, {len(game_types)} 种游戏, {total_rules} 条规则"

    except Exception as e:
        return False, f"配置验证异常: {e}"


async def load_salary_config_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """从Google Sheet中加载salary配置的命令处理器"""
    try:
        # 发送开始处理的消息
        await update.message.reply_text("🔄 正在从 Google Sheet 加载 salary 配置...")

        # 从Google Sheet读取配置
        new_config = load_salary_from_google_sheet()

        # 验证配置结构
        is_valid, validation_msg = validate_salary_config(new_config)
        if not is_valid:
            error_msg = f"❌ 配置验证失败\n\n{validation_msg}\n\n请检查 Google Sheets 数据格式"
            await update.message.reply_text(error_msg)
            return

        # 保存到本地文件
        save_salary_config(new_config)

        # 统计配置信息
        rebate_count = len(new_config.keys())
        total_rules = sum(len(games.values()) for games in new_config.values() if isinstance(games, dict))
        game_types = set()
        for rebate_config in new_config.values():
            if isinstance(rebate_config, dict):
                game_types.update(rebate_config.keys())

        # 发送成功消息
        success_msg = (
            f"✅ Salary 配置加载成功！\n\n"
            f"📊 配置统计：\n"
            f"• Rebate类型数量：{rebate_count}\n"
            f"• 游戏类型：{', '.join(sorted(game_types))}\n"
            f"• 总规则数量：{total_rules}\n\n"
            f"🔍 {validation_msg}\n\n"
            f"💾 配置已保存到本地文件 salary_config.json"
        )

        await update.message.reply_text(success_msg)

    except Exception as e:
        error_str = str(e)

        # 检查是否是Google API权限问题
        if "Google Drive API has not been used" in error_str or "403" in error_str:
            error_msg = (
                f"❌ Google Sheets 访问权限不足\n\n"
                f"🔧 解决方案：\n"
                f"1. 请联系管理员启用 Google Drive API\n"
                f"2. 或者手动更新 salary_config.json 文件\n\n"
                f"📋 当前配置文件格式示例：\n"
                f"```json\n"
                f'{{\n'
                f'  "0.2": {{\n'
                f'    "BJ": [\n'
                f'      {{\n'
                f'        "enabled": true,\n'
                f'        "profit_min": 200,\n'
                f'        "profit_max": 999,\n'
                f'        "salary": 20,\n'
                f'        "description": "BJ 赢 200~999"\n'
                f'      }}\n'
                f'    ]\n'
                f'  }}\n'
                f'}}\n'
                f"```\n\n"
                f"💡 提示：可以使用 /set_salary 命令单独设置工资规则"
            )
        elif "未找到" in error_str and "表格" in error_str:
            error_msg = (
                f"❌ 未找到 salary 配置表格\n\n"
                f"🔧 解决方案：\n"
                f"请在 Google Sheet 中创建名为 '{new_config.get('SALARY_CONFIG_SHEET_NAME', 'salary配置表格')}' 的表格，\n"
                f"包含以下列：启用、Rebate、游戏、游戏子类型、盈亏下限、盈亏上限、工资、备注\n\n"
                f"📋 表格格式示例：\n"
                f"启用 | Rebate | 游戏 | 游戏子类型 | 盈亏下限 | 盈亏上限 | 工资 | 备注\n"
                f"TRUE | 0.2 | BJ |  | 200 | 999 | 20 | BJ 赢 200~999\n"
                f"TRUE | 0.2 | BJ |  | 1000 | 1999 | 30 | BJ 赢 1000~1999"
            )
        else:
            error_msg = f"❌ 加载 salary 配置失败：{error_str}"

        await update.message.reply_text(error_msg)


async def set_salary_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """设置salary配置的命令处理器"""
    await set_salary_config(update, context)


async def cache_status_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看消息缓存状态的命令处理器"""
    try:
        stats = message_cache.get_cache_stats()

        total_cached = stats["total_cached"]
        pending_retry = stats["pending_retry"]
        oldest_message = stats["oldest_message"]
        retry_counts = stats["retry_counts"]

        if total_cached == 0:
            status_msg = "✅ 当前没有缓存的消息，所有消息都已成功同步到 Google Sheets。"
        else:
            status_msg = f"📊 消息缓存状态：\n\n"
            status_msg += f"• 总缓存消息数：{total_cached}\n"
            status_msg += f"• 待重试消息数：{pending_retry}\n"

            if oldest_message:
                oldest_str = oldest_message.strftime("%Y-%m-%d %H:%M:%S")
                status_msg += f"• 最早缓存时间：{oldest_str}\n"

            status_msg += f"\n📈 重试次数分布：\n"
            for retry_count, count in sorted(retry_counts.items()):
                if retry_count == 0:
                    status_msg += f"• 未重试：{count} 条\n"
                else:
                    status_msg += f"• 重试 {retry_count} 次：{count} 条\n"

            if pending_retry > 0:
                status_msg += f"\n⏰ 系统将自动重试同步缓存的消息"

        await update.message.reply_text(status_msg)

    except Exception as e:
        error_msg = f"❌ 获取缓存状态失败：{str(e)}"
        await update.message.reply_text(error_msg)


async def retry_sync_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """手动重试同步缓存消息的命令处理器"""
    try:
        await update.message.reply_text("🔄 正在重试同步缓存的消息...")

        result = await retry_sync_cached_messages()

        success = result["success"]
        failed = result["failed"]
        total = result["total"]

        if total == 0:
            result_msg = "✅ 当前没有需要重试的消息。"
        else:
            result_msg = f"📊 重试同步结果：\n\n"
            result_msg += f"• 总处理消息：{total} 条\n"
            result_msg += f"• 同步成功：{success} 条\n"
            result_msg += f"• 同步失败：{failed} 条\n"

            if success > 0:
                result_msg += f"\n✅ 成功同步的消息已写入 Google Sheets"

            if failed > 0:
                result_msg += f"\n⚠️ 失败的消息将继续自动重试"

        await update.message.reply_text(result_msg)

    except Exception as e:
        error_msg = f"❌ 手动重试同步失败：{str(e)}"
        await update.message.reply_text(error_msg)


async def config_cache_status_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看配置缓存状态的命令处理器"""
    try:
        stats = config_cache.get_cache_stats()

        if not stats['initialized']:
            status_msg = "❌ 配置缓存未初始化"
        else:
            status_msg = f"📊 配置缓存状态：\n\n"
            status_msg += f"• 初始化状态：{'✅ 已初始化' if stats['initialized'] else '❌ 未初始化'}\n"
            status_msg += f"• Rebate缓存：{'✅ 已缓存' if stats['rebate_cached'] else '❌ 未缓存'}\n"
            status_msg += f"• Salary缓存：{'✅ 已缓存' if stats['salary_cached'] else '❌ 未缓存'}\n"

            if stats['rebate_last_updated']:
                rebate_time = stats['rebate_last_updated'][:19].replace('T', ' ')
                status_msg += f"• Rebate更新时间：{rebate_time}\n"

            if stats['salary_last_updated']:
                salary_time = stats['salary_last_updated'][:19].replace('T', ' ')
                status_msg += f"• Salary更新时间：{salary_time}\n"

            status_msg += f"\n📈 使用统计：\n"
            status_msg += f"• Rebate访问次数：{stats['rebate_access_count']}\n"
            status_msg += f"• Salary访问次数：{stats['salary_access_count']}\n"

            status_msg += f"\n📋 配置统计：\n"
            status_msg += f"• Rebate配置项：{stats['rebate_config_count']}\n"
            status_msg += f"• Salary规则数：{stats['salary_rules_count']}\n"
            status_msg += f"• 内存占用：{stats['estimated_memory_kb']:.2f}KB\n"

        await update.message.reply_text(status_msg)

    except Exception as e:
        error_msg = f"❌ 获取配置缓存状态失败：{str(e)}"
        await update.message.reply_text(error_msg)


async def refresh_config_cache_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """手动刷新配置缓存的命令处理器"""
    try:
        await update.message.reply_text("🔄 正在刷新配置缓存...")

        result = await refresh_config_cache(force_google_sheet=True)

        if result['success']:
            result_msg = f"✅ 配置缓存刷新成功！\n\n"
            result_msg += f"📊 刷新结果：\n"
            result_msg += f"• Rebate配置：{'✅ 已刷新' if result.get('rebate_refreshed', False) else '❌ 刷新失败'}\n"
            result_msg += f"• Salary配置：{'✅ 已刷新' if result.get('salary_refreshed', False) else '❌ 刷新失败'}\n"
            result_msg += f"• Rebate配置项：{result.get('rebate_count', 0)}\n"
            result_msg += f"• Salary规则数：{result.get('salary_rules', 0)}\n"
            result_msg += f"• 刷新时间：{result.get('timestamp', '')[:19].replace('T', ' ')}\n"
        else:
            result_msg = f"❌ 配置缓存刷新失败：{result.get('error', 'Unknown error')}"

        await update.message.reply_text(result_msg)

    except Exception as e:
        error_msg = f"❌ 手动刷新配置缓存失败：{str(e)}"
        await update.message.reply_text(error_msg)


async def help_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """帮助命令处理器 - 显示所有可用命令的详细说明"""
    try:
        help_text = """🤖 **群组记录机器人帮助文档**

📊 **报表生成命令**
• `/daily_report` - 生成昨日盈亏汇总报告
  📈 显示昨天所有场子和人员的盈亏情况，包括总盈亏、工资统计等
  ⏰ 自动执行时间：每日中午12:05（UTC+4）

• `/monthly_report` - 生成每周盈亏汇总报告
  📊 显示本周的盈亏趋势和统计数据，支持多维度分析
  ⏰ 自动执行时间：每日中午12:10（UTC+4）

• `/trend_chart` - 生成盈亏趋势图
  📈 生成最近一段时间的盈亏变化趋势图表，可视化数据分析
  ⏰ 自动执行时间：每日中午12:15（UTC+4）

• `/venue_report` - 生成场子盈亏柱状图
  🏢 按场子分组显示盈亏情况的可视化图表
  ⏰ 自动执行时间：每月1日中午12:15（UTC+4）

• `/person_report` - 生成个人盈亏柱状图
  👤 按人员分组显示个人盈亏情况的可视化图表
  ⏰ 自动执行时间：每月1日中午12:20（UTC+4）

🔍 **数据查询命令**
• `/find` - 查找记录
  📋 格式：`/find 关键词`
  🔎 支持按场子、人员、游戏类型等条件查找历史记录
  💡 示例：`/find Otium` 或 `/find 张三` 或 `/find BJ`

• `/export_file` - 导出数据
  📁 将当前数据导出为Excel文件，方便离线分析和备份

⚙️ **配置管理命令**
• `/set_rebate` - 设置输返比例
  📋 格式：`/set_rebate 场子 [人员] 比例`
  🏢 示例：`/set_rebate Otium 0.2` (设置场子默认比例为20%)
  👤 示例：`/set_rebate Otium 张三 0.15` (设置个人比例为15%)
  ⚠️ 注意：个人比例优先级高于场子默认比例

• `/add_venue` - 添加新venue
  📋 格式：`/add_venue venue名称 别名1 [别名2] [别名3] ...`
  🏢 示例：`/add_venue 新赌场 nc newcasino 新场`
  ✅ 自动更新aliases.json文件并引导设置rebate比例
  ⚠️ 注意：venue名称和至少一个别名是必需的

• `/load_rebate_config` - 从Google Sheet加载rebate配置
  ☁️ 从云端同步最新的输返比例配置到本地
  🔄 会自动刷新内存缓存，立即生效

• `/set_salary` - 设置工资规则
  📋 格式：`/set_salary rebate比例 游戏 盈亏下限 盈亏上限 工资`
  💰 示例：`/set_salary 0.2 BJ 200 999 20` (20%rebate的BJ游戏，盈亏200-999时工资20)
  ⚠️ 注意：盈亏范围包含下限，不包含上限

• `/load_salary_config` - 从Google Sheet加载salary配置
  ☁️ 从云端同步最新的工资计算规则到本地
  🔄 会自动刷新内存缓存，立即生效

🗂️ **缓存管理命令**
• `/cache_status` - 查看消息缓存状态
  📊 显示未同步到Google Sheet的消息数量和重试状态
  🔄 显示重试队列和失败原因

• `/retry_sync` - 手动重试同步缓存消息
  🔄 手动触发重试同步失败的消息到Google Sheet
  ⚡ 适用于网络恢复后的批量重试

• `/config_cache_status` - 查看配置缓存状态
  💾 显示内存中配置缓存的状态、访问统计和内存占用
  📊 包含rebate和salary配置的详细统计信息

• `/refresh_config_cache` - 手动刷新配置缓存
  🔄 从Google Sheet重新加载配置到内存缓存
  ⚡ 立即生效，无需重启程序

ℹ️ **帮助命令**
• `/help` - 显示此帮助信息
  📚 显示所有可用命令的详细说明和使用示例

---

📝 **消息格式说明**
**标准格式**：`人员 场子 游戏 流水号 下注 赢亏 输返 工资`
**示例**：`张三 Otium BJ 12345 1000 500 0 20`

**字段说明**：
• 人员：操作员姓名
• 场子：游戏场所名称
• 游戏：游戏类型（BJ、UTH、俄罗斯、百家乐等）
• 流水号：唯一标识符
• 下注：下注金额（整数）
• 赢亏：盈亏金额（正数为盈利，负数为亏损）
• 输返：输返金额（通常为0，系统自动计算）
• 工资：工资金额（可为0，系统自动计算）

🔄 **自动功能**
• 🧮 自动计算工资（基于rebate比例、游戏类型和盈亏）
• ☁️ 自动同步到Google Sheet（失败时缓存并重试）
• 📊 定时生成报表（每日、每周、每月）
• 🔄 定时刷新配置（每天中午12:45）
• 💾 智能缓存管理（30分钟检查一次，每天11点重试）

💡 **使用提示**
1. 💰 所有金额单位均为整数
2. 📊 输返比例支持小数（如0.1表示10%）
3. 🌐 系统会自动处理网络异常和重试
4. ⚡ 配置更改会实时生效并同步到云端
5. 🎯 工资计算优先级：手动输入 > 自动计算
6. 🔍 查找功能支持模糊匹配和多关键词
7. 📈 报表数据基于迪拜时区（UTC+4）

🚨 **注意事项**
• 请确保消息格式正确，避免数据错误
• 网络异常时消息会自动缓存，恢复后重试
• 配置修改会影响后续的工资计算
• 定时任务基于UTC时间，显示为迪拜时间

如有问题，请联系管理员 👨‍💼"""

        await update.message.reply_text(help_text, parse_mode='Markdown')

    except Exception as e:
        error_msg = f"❌ 显示帮助信息失败：{str(e)}"
        await update.message.reply_text(error_msg)


async def monthly_salary_check_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """月度工资检查命令处理器"""
    try:
        from monthlyjob import monthly_salary_check_job

        # 发送开始消息
        await update.message.reply_text("🔄 开始执行月度工资检查...")

        # 执行月度工资检查任务
        await monthly_salary_check_job(context)

        # 发送完成消息
        await update.message.reply_text("✅ 月度工资检查完成！")

    except Exception as e:
        logger.error(f"月度工资检查命令执行失败: {e}")
        await update.message.reply_text(f"❌ 月度工资检查失败: {str(e)}")


@with_error_handling(operation_name="complete_monthly_job", notify_user=True)
async def complete_monthly_job_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """手动触发完整月度工作流的命令处理器。
    用法：/complete_monthly_job [YYYY-MM] 或 /complete_monthly_job [YYYY MM]
    不带参数时默认执行上个月。
    """
    try:
        await update.message.reply_text("🔄 开始执行完整月度工作流...")

        target_year = None
        target_month = None

        # 解析参数：YYYY-MM 或 YYYY MM
        args = context.args or []
        if len(args) == 2 and args[0].isdigit() and args[1].isdigit():
            target_year = int(args[0])
            target_month = int(args[1])
        elif len(args) == 1:
            m = re.match(r"^(\d{4})[-/](\d{1,2})$", args[0])
            if m:
                target_year = int(m.group(1))
                target_month = int(m.group(2))

        from monthlyjob import manual_complete_monthly_job
        result = manual_complete_monthly_job(target_year, target_month)

        success = result.get('success', False)
        period = result.get('target_period') or {}
        year = period.get('year')
        month = period.get('month')

        if success:
            msg = "✅ 完整月度工作流执行成功"
        else:
            msg = "❌ 完整月度工作流执行失败"

        if year and month:
            msg += f"\n📅 期间：{year}-{int(month):02d}"

        errors = result.get('errors') or []
        if not success and errors:
            preview = "; ".join(str(e) for e in errors[:3])
            msg += f"\n⚠️ 错误：{preview}"

        await update.message.reply_text(msg)

    except Exception as e:
        logger.error(f"完整月度工作流命令执行失败: {e}")
        await update.message.reply_text(f"❌ 执行失败: {str(e)}")


async def help_simple_command_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """简化版帮助命令处理器 - 显示常用命令的简要说明"""
    try:
        help_text = """🤖 **群组记录机器人 - 常用命令**

📊 **报表命令**
• `/daily_report` - 昨日盈亏汇总
• `/monthly_report` - 每周盈亏汇总
• `/trend_chart` - 盈亏趋势图
• `/venue_report` - 场子盈亏图
• `/person_report` - 个人盈亏图

🔄 **月度任务**
• `/monthly_salary_check` - 执行月度工资检查

🔍 **查询命令**
• `/find 关键词` - 查找记录
• `/export_file` - 导出Excel

⚙️ **配置命令**
• `/set_rebate 场子 比例` - 设置输返比例
• `/add_venue venue名称 别名1 别名2...` - 添加新venue
• `/load_rebate_config` - 加载rebate配置
• `/set_salary rebate 游戏 下限 上限 工资` - 设置工资
• `/load_salary_config` - 加载salary配置

🗂️ **缓存命令**
• `/cache_status` - 缓存状态
• `/retry_sync` - 重试同步
• `/config_cache_status` - 配置缓存状态
• `/refresh_config_cache` - 刷新配置缓存

📝 **消息格式**
`人员 场子 游戏 流水号 下注 赢亏 输返 工资`
示例：`张三 Otium BJ 12345 1000 500 0 20`

💡 **快速提示**
• 工资和输返可填0，系统自动计算
• 支持模糊查找：`/find Otium` 或 `/find 张三`
• 配置修改立即生效
• 网络异常时自动缓存重试

输入 `/help` 查看详细帮助 📚"""

        await update.message.reply_text(help_text, parse_mode='Markdown')

    except Exception as e:
        error_msg = f"❌ 显示简化帮助信息失败：{str(e)}"
        await update.message.reply_text(error_msg)
