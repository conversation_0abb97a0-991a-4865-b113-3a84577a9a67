#!/usr/bin/env python3
"""
安全工具模块
提供文件权限管理、输入验证等安全功能
"""

import os
import stat
import re
import json
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
import hashlib
import secrets

logger = logging.getLogger(__name__)

class SecurityUtils:
    """安全工具类"""
    
    # 敏感文件列表
    SENSITIVE_FILES = [
        'mysheetapp.json',
        'mysheetapp_new.json', 
        '.env',
        'rebate_config.json',
        'salary_config.json',
        'message_cache.txt',
        'retry_log.txt'
    ]
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'.json', '.txt', '.xlsx', '.py', '.md', '.bat', '.env'}
    
    @staticmethod
    def create_secure_file(filepath: str, content: str = "", is_sensitive: bool = None) -> bool:
        """
        创建具有安全权限的文件
        
        Args:
            filepath: 文件路径
            content: 文件内容
            is_sensitive: 是否为敏感文件，None时自动判断
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 确保目录存在
            directory = os.path.dirname(filepath)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, mode=0o755, exist_ok=True)
            
            # 判断是否为敏感文件
            if is_sensitive is None:
                filename = os.path.basename(filepath)
                is_sensitive = filename in SecurityUtils.SENSITIVE_FILES
            
            # 创建文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 设置文件权限
            if os.name == 'nt':  # Windows
                # Windows 下不设置隐藏属性，避免在 Windows Server 2012 R2 上出现权限问题
                # 敏感文件通过文件系统权限保护，而不是隐藏属性
                try:
                    # 确保文件可读写，移除只读属性
                    os.system(f'attrib -R -H "{filepath}"')
                    logger.debug(f"Windows 文件属性已设置为可读写: {filepath}")
                except Exception as e:
                    logger.warning(f"设置Windows文件属性失败: {e}")
            else:  # Unix/Linux
                if is_sensitive:
                    # 敏感文件：仅所有者可读写 (600)
                    os.chmod(filepath, stat.S_IRUSR | stat.S_IWUSR)
                else:
                    # 普通文件：所有者读写，组和其他用户只读 (644)
                    os.chmod(filepath, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
            
            logger.info(f"安全文件创建成功: {filepath} (敏感: {is_sensitive})")
            return True
            
        except Exception as e:
            logger.error(f"创建安全文件失败 {filepath}: {e}")
            return False

    @staticmethod
    def read_secure_file(filepath: str) -> str:
        """
        安全读取文件内容

        Args:
            filepath: 文件路径

        Returns:
            str: 文件内容，失败时返回空字符串
        """
        try:
            if not os.path.exists(filepath):
                logger.warning(f"文件不存在: {filepath}")
                return ""

            # 检查文件访问权限
            if not os.access(filepath, os.R_OK):
                logger.error(f"文件无读取权限: {filepath}")
                return ""

            # 尝试不同的编码方式
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'cp1252']
            content = ""

            for encoding in encodings:
                try:
                    with open(filepath, 'r', encoding=encoding) as f:
                        content = f.read()
                    logger.debug(f"安全读取文件成功: {filepath} (编码: {encoding})")
                    break
                except UnicodeDecodeError:
                    continue

            if not content:
                logger.warning(f"无法使用任何编码读取文件: {filepath}")

            return content

        except Exception as e:
            logger.error(f"安全读取文件失败 {filepath}: {e}")
            return ""

    @staticmethod
    def secure_existing_files() -> Dict[str, bool]:
        """
        为现有文件设置安全权限
        
        Returns:
            Dict[str, bool]: 文件路径和处理结果的映射
        """
        results = {}
        
        # 获取当前目录下的所有文件
        current_dir = Path('.')
        
        for file_path in current_dir.rglob('*'):
            if file_path.is_file():
                filepath_str = str(file_path)
                filename = file_path.name
                
                # 跳过 __pycache__ 和 .git 目录
                if '__pycache__' in filepath_str or '.git' in filepath_str:
                    continue
                
                # 检查文件扩展名
                if file_path.suffix not in SecurityUtils.ALLOWED_EXTENSIONS:
                    continue
                
                try:
                    is_sensitive = filename in SecurityUtils.SENSITIVE_FILES
                    
                    if os.name != 'nt':  # Unix/Linux
                        if is_sensitive:
                            os.chmod(filepath_str, stat.S_IRUSR | stat.S_IWUSR)
                        else:
                            os.chmod(filepath_str, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
                    else:  # Windows
                        # Windows 下确保文件可读写，移除隐藏和只读属性
                        try:
                            os.system(f'attrib -R -H "{filepath_str}"')
                        except Exception:
                            pass
                    
                    results[filepath_str] = True
                    logger.info(f"文件权限已更新: {filepath_str} (敏感: {is_sensitive})")
                    
                except Exception as e:
                    results[filepath_str] = False
                    logger.error(f"更新文件权限失败 {filepath_str}: {e}")
        
        return results
    
    @staticmethod
    def validate_file_access(filepath: str) -> bool:
        """
        验证文件访问权限
        
        Args:
            filepath: 文件路径
            
        Returns:
            bool: 是否有适当的访问权限
        """
        try:
            if not os.path.exists(filepath):
                return False
            
            # 检查读写权限
            if not os.access(filepath, os.R_OK | os.W_OK):
                logger.warning(f"文件权限不足: {filepath}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证文件访问权限失败 {filepath}: {e}")
            return False


class InputValidator:
    """输入验证器"""
    
    # 字段验证规则
    FIELD_RULES = {
        '人员': {
            'type': str,
            'min_length': 1,
            'max_length': 50,
            'pattern': r'^[\u4e00-\u9fa5a-zA-Z0-9_\-]+$',  # 中文、英文、数字、下划线、连字符
            'required': True
        },
        '场子': {
            'type': str,
            'min_length': 1,
            'max_length': 50,
            'pattern': r'^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$',
            'required': True
        },
        '游戏': {
            'type': str,
            'min_length': 1,
            'max_length': 20,
            'pattern': r'^[\u4e00-\u9fa5a-zA-Z0-9]+$',
            'required': True
        },
        '卡号': {
            'type': str,
            'min_length': 1,
            'max_length': 50,
            'pattern': r'^[a-zA-Z0-9_\-]+$',
            'required': False
        },
        '本金': {
            'type': int,
            'min_value': 0,
            'max_value': 1000000,
            'required': True
        },
        '点码': {
            'type': int,
            'min_value': 0,
            'max_value': 1000000,
            'required': True
        },
        '工资': {
            'type': int,
            'min_value': 0,
            'max_value': 100000,
            'required': False
        },
        '输反': {
            'type': int,
            'min_value': 0,
            'max_value': 1000000,
            'required': False
        },
        '盈利': {
            'type': int,
            'min_value': -1000000,
            'max_value': 1000000,
            'required': True
        },
        '日期': {
            'type': str,
            'pattern': r'^\d{4}-\d{2}-\d{2}$',
            'required': False
        }
    }
    
    @staticmethod
    def validate_field(field_name: str, value: Any) -> tuple[bool, str]:
        """
        验证单个字段
        
        Args:
            field_name: 字段名
            value: 字段值
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        if field_name not in InputValidator.FIELD_RULES:
            return True, ""  # 未知字段不验证
        
        rules = InputValidator.FIELD_RULES[field_name]
        
        # 检查必填字段
        if rules.get('required', False) and (value is None or value == ""):
            return False, f"字段 '{field_name}' 是必填项"
        
        # 如果值为空且非必填，跳过验证
        if value is None or value == "":
            return True, ""
        
        # 类型验证
        expected_type = rules.get('type')
        if expected_type and not isinstance(value, expected_type):
            if expected_type == int:
                try:
                    value = int(value)
                except (ValueError, TypeError):
                    return False, f"字段 '{field_name}' 必须是整数"
            elif expected_type == str:
                value = str(value)
        
        # 字符串验证
        if isinstance(value, str):
            # 长度验证
            min_len = rules.get('min_length')
            max_len = rules.get('max_length')
            
            if min_len and len(value) < min_len:
                return False, f"字段 '{field_name}' 长度不能少于 {min_len} 个字符"
            
            if max_len and len(value) > max_len:
                return False, f"字段 '{field_name}' 长度不能超过 {max_len} 个字符"
            
            # 正则表达式验证
            pattern = rules.get('pattern')
            if pattern and not re.match(pattern, value):
                return False, f"字段 '{field_name}' 格式不正确"
        
        # 数值验证
        if isinstance(value, (int, float)):
            min_val = rules.get('min_value')
            max_val = rules.get('max_value')
            
            if min_val is not None and value < min_val:
                return False, f"字段 '{field_name}' 不能小于 {min_val}"
            
            if max_val is not None and value > max_val:
                return False, f"字段 '{field_name}' 不能大于 {max_val}"
        
        return True, ""
    
    @staticmethod
    def validate_message_data(data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        验证消息数据
        
        Args:
            data: 消息数据字典
            
        Returns:
            tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []
        
        for field_name, value in data.items():
            if field_name == '备注':  # 备注字段不验证
                continue
                
            is_valid, error_msg = InputValidator.validate_field(field_name, value)
            if not is_valid:
                errors.append(error_msg)
        
        return len(errors) == 0, errors
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """
        清理输入文本
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not isinstance(text, str):
            text = str(text)
        
        # 移除危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        # 限制长度
        if len(text) > 1000:
            text = text[:1000]
        
        # 移除首尾空白
        text = text.strip()
        
        return text
    
    @staticmethod
    def validate_command_args(command: str, args: List[str]) -> tuple[bool, str]:
        """
        验证命令参数
        
        Args:
            command: 命令名
            args: 参数列表
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 命令参数验证规则
        command_rules = {
            'set_rebate': {
                'min_args': 2,
                'max_args': 3,
                'arg_types': [str, str, float]  # 场子, [人员], 比例
            },
            'set_salary': {
                'min_args': 5,
                'max_args': 5,
                'arg_types': [float, str, int, int, int]  # rebate, 游戏, 下限, 上限, 工资
            },
            'find': {
                'min_args': 1,
                'max_args': 1,
                'arg_types': [str]  # 关键词
            }
        }
        
        if command not in command_rules:
            return True, ""  # 未知命令不验证
        
        rules = command_rules[command]
        
        # 检查参数数量
        if len(args) < rules['min_args']:
            return False, f"命令 '{command}' 需要至少 {rules['min_args']} 个参数"
        
        if len(args) > rules['max_args']:
            return False, f"命令 '{command}' 最多接受 {rules['max_args']} 个参数"
        
        # 检查参数类型
        arg_types = rules.get('arg_types', [])
        for i, (arg, expected_type) in enumerate(zip(args, arg_types)):
            if expected_type == float:
                try:
                    float(arg)
                except ValueError:
                    return False, f"参数 {i+1} 必须是数字"
            elif expected_type == int:
                try:
                    int(arg)
                except ValueError:
                    return False, f"参数 {i+1} 必须是整数"
            elif expected_type == str:
                if not isinstance(arg, str) or len(arg.strip()) == 0:
                    return False, f"参数 {i+1} 不能为空"
        
        return True, ""


def initialize_security() -> Dict[str, Any]:
    """
    初始化安全设置
    
    Returns:
        Dict[str, Any]: 初始化结果
    """
    results = {
        'file_permissions': False,
        'validation_ready': False,
        'errors': [],
        'warnings': []
    }
    
    try:
        # 设置文件权限
        logger.info("开始设置文件安全权限...")
        permission_results = SecurityUtils.secure_existing_files()
        
        success_count = sum(1 for success in permission_results.values() if success)
        total_count = len(permission_results)
        
        if success_count == total_count:
            results['file_permissions'] = True
            logger.info(f"文件权限设置完成: {success_count}/{total_count} 成功")
        else:
            results['warnings'].append(f"部分文件权限设置失败: {success_count}/{total_count} 成功")
        
        # 验证器准备就绪
        results['validation_ready'] = True
        logger.info("输入验证器已准备就绪")
        
        return results
        
    except Exception as e:
        error_msg = f"安全初始化失败: {e}"
        results['errors'].append(error_msg)
        logger.error(error_msg)
        return results


if __name__ == "__main__":
    # 测试安全功能
    print("🔒 测试安全功能...")
    
    # 初始化安全设置
    init_results = initialize_security()
    print(f"初始化结果: {init_results}")
    
    # 测试输入验证
    test_data = {
        '人员': '张三',
        '场子': 'Otium',
        '游戏': 'BJ',
        '起始本金': 1000,
        '盈利': 500
    }
    
    is_valid, errors = InputValidator.validate_message_data(test_data)
    print(f"数据验证结果: 有效={is_valid}, 错误={errors}")
