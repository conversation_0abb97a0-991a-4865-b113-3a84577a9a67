#!/usr/bin/env python3
"""
错误处理模块
提供统一的错误处理、重试机制、错误恢复等功能
"""

import asyncio
import logging
import traceback
from typing import Any, Callable, Dict, List, Optional, Type, Union
from datetime import datetime, timedelta
from enum import Enum
from functools import wraps
import json

logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    FILE_ERROR = "file_error"
    GOOGLE_API_ERROR = "google_api_error"
    VALIDATION_ERROR = "validation_error"
    PERMISSION_ERROR = "permission_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class StorageError(Exception):
    """存储相关错误基类"""
    def __init__(self, message: str, error_type: ErrorType = ErrorType.UNKNOWN_ERROR, 
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM, details: Dict = None):
        super().__init__(message)
        self.error_type = error_type
        self.severity = severity
        self.details = details or {}
        self.timestamp = datetime.now()

class NetworkError(StorageError):
    """网络错误"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, ErrorType.NETWORK_ERROR, ErrorSeverity.HIGH, details)

class FileOperationError(StorageError):
    """文件操作错误"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, ErrorType.FILE_ERROR, ErrorSeverity.MEDIUM, details)

class GoogleAPIError(StorageError):
    """Google API 错误"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, ErrorType.GOOGLE_API_ERROR, ErrorSeverity.HIGH, details)

class ValidationError(StorageError):
    """验证错误"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, ErrorType.VALIDATION_ERROR, ErrorSeverity.LOW, details)

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.error_stats = {
            error_type: 0 for error_type in ErrorType
        }
        self.recent_errors = []
        self.max_recent_errors = 100
    
    def log_error(self, error: Exception, context: str = "", extra_data: Dict = None):
        """记录错误"""
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'error_type': type(error).__name__,
            'message': str(error),
            'context': context,
            'traceback': traceback.format_exc(),
            'extra_data': extra_data or {}
        }
        
        # 添加到最近错误列表
        self.recent_errors.append(error_info)
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors.pop(0)
        
        # 更新统计
        if isinstance(error, StorageError):
            self.error_stats[error.error_type] += 1
        else:
            self.error_stats[ErrorType.UNKNOWN_ERROR] += 1
        
        # 根据错误严重程度选择日志级别
        if isinstance(error, StorageError):
            if error.severity == ErrorSeverity.CRITICAL:
                logger.critical(f"[{context}] {str(error)}", extra=error_info)
            elif error.severity == ErrorSeverity.HIGH:
                logger.error(f"[{context}] {str(error)}", extra=error_info)
            elif error.severity == ErrorSeverity.MEDIUM:
                logger.warning(f"[{context}] {str(error)}", extra=error_info)
            else:
                logger.info(f"[{context}] {str(error)}", extra=error_info)
        else:
            logger.error(f"[{context}] {str(error)}", extra=error_info)
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        return {
            'error_counts': {error_type.value: count for error_type, count in self.error_stats.items()},
            'recent_errors_count': len(self.recent_errors),
            'last_error': self.recent_errors[-1] if self.recent_errors else None
        }
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict]:
        """获取最近的错误"""
        return self.recent_errors[-limit:]

class RetryConfig:
    """重试配置"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, exponential_base: float = 2.0,
                 retryable_exceptions: List[Type[Exception]] = None):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.retryable_exceptions = retryable_exceptions or [
            NetworkError, GoogleAPIError, FileOperationError
        ]
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """判断是否应该重试"""
        if attempt >= self.max_attempts:
            return False
        
        return any(isinstance(exception, exc_type) for exc_type in self.retryable_exceptions)
    
    def get_delay(self, attempt: int) -> float:
        """计算重试延迟"""
        delay = self.base_delay * (self.exponential_base ** (attempt - 1))
        return min(delay, self.max_delay)

def with_retry(config: RetryConfig = None):
    """重试装饰器"""
    if config is None:
        config = RetryConfig()
    
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if not config.should_retry(e, attempt):
                        break
                    
                    if attempt < config.max_attempts:
                        delay = config.get_delay(attempt)
                        logger.warning(f"函数 {func.__name__} 第 {attempt} 次尝试失败，{delay}秒后重试: {e}")
                        await asyncio.sleep(delay)
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {attempt} 次后仍然失败: {e}")
            
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if not config.should_retry(e, attempt):
                        break
                    
                    if attempt < config.max_attempts:
                        delay = config.get_delay(attempt)
                        logger.warning(f"函数 {func.__name__} 第 {attempt} 次尝试失败，{delay}秒后重试: {e}")
                        import time
                        time.sleep(delay)
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {attempt} 次后仍然失败: {e}")
            
            raise last_exception
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def with_error_handling(error_handler: ErrorHandler = None, context: str = ""):
    """错误处理装饰器"""
    if error_handler is None:
        error_handler = ErrorHandler()
    
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                error_handler.log_error(e, context or func.__name__)
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.log_error(e, context or func.__name__)
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60, 
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def __call__(self, func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                else:
                    raise StorageError("熔断器开启，服务暂时不可用", 
                                     ErrorType.NETWORK_ERROR, ErrorSeverity.HIGH)
            
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                self._on_success()
                return result
                
            except self.expected_exception as e:
                self._on_failure()
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                else:
                    raise StorageError("熔断器开启，服务暂时不可用", 
                                     ErrorType.NETWORK_ERROR, ErrorSeverity.HIGH)
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
                
            except self.expected_exception as e:
                self._on_failure()
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        if self.last_failure_time is None:
            return True
        
        return (datetime.now() - self.last_failure_time).total_seconds() > self.recovery_timeout
    
    def _on_success(self):
        """成功时的处理"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self):
        """失败时的处理"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"

class ErrorRecovery:
    """错误恢复机制"""
    
    def __init__(self):
        self.recovery_strategies = {}
    
    def register_strategy(self, error_type: ErrorType, strategy: Callable):
        """注册恢复策略"""
        self.recovery_strategies[error_type] = strategy
    
    async def attempt_recovery(self, error: StorageError) -> bool:
        """尝试错误恢复"""
        if error.error_type in self.recovery_strategies:
            try:
                strategy = self.recovery_strategies[error.error_type]
                if asyncio.iscoroutinefunction(strategy):
                    return await strategy(error)
                else:
                    return strategy(error)
            except Exception as e:
                logger.error(f"错误恢复策略执行失败: {e}")
                return False
        
        return False

# 全局错误处理器实例
_global_error_handler = ErrorHandler()
_global_error_recovery = ErrorRecovery()

def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器"""
    return _global_error_handler

def get_error_recovery() -> ErrorRecovery:
    """获取全局错误恢复器"""
    return _global_error_recovery

# 常用的重试配置
NETWORK_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=2.0,
    max_delay=30.0,
    retryable_exceptions=[NetworkError, GoogleAPIError]
)

FILE_RETRY_CONFIG = RetryConfig(
    max_attempts=2,
    base_delay=0.5,
    max_delay=5.0,
    retryable_exceptions=[FileOperationError]
)

GOOGLE_API_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=1.0,
    max_delay=60.0,
    exponential_base=2.0,
    retryable_exceptions=[GoogleAPIError, NetworkError]
)
