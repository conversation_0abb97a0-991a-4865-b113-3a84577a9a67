#!/usr/bin/env python3
"""
Process monitoring and health check utilities
"""

import asyncio
import logging
import time
import os
import sys
import signal
import atexit
from datetime import datetime
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class ProcessMonitor:
    """Monitor process health and detect interruptions"""
    
    def __init__(self, heartbeat_interval: int = 30):
        self.heartbeat_interval = heartbeat_interval
        self.heartbeat_file = "bot_heartbeat.txt"
        self.process_info_file = "bot_process_info.txt"
        self.last_heartbeat = time.time()
        self.is_running = False
        self._heartbeat_task: Optional[asyncio.Task] = None
        
        # Register cleanup handlers
        atexit.register(self.cleanup)
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle process termination signals"""
        logger.warning(f"收到终止信号 {signum}，正在清理...")
        self.cleanup()
        # 设置停止标志，让主循环优雅退出
        self.is_running = False
        
        # 尝试终止主事件循环
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.call_soon_threadsafe(loop.stop)
        except Exception as e:
            logger.error(f"停止事件循环失败: {e}")
    
    def start_monitoring(self):
        """Start process monitoring"""
        self.is_running = True
        self.last_heartbeat = time.time()
        
        # Write initial process info
        self._write_process_info()
        
        # Start heartbeat task
        if self._heartbeat_task is None or self._heartbeat_task.done():
            self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        
        logger.info(f"进程监控已启动，心跳间隔: {self.heartbeat_interval}秒")
    
    def stop_monitoring(self):
        """Stop process monitoring"""
        self.is_running = False
        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()
        logger.info("进程监控已停止")
    
    async def _heartbeat_loop(self):
        """Heartbeat loop to write periodic status"""
        while self.is_running:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                self._write_heartbeat()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳写入失败: {e}")
    
    def _write_heartbeat(self):
        """Write heartbeat timestamp"""
        try:
            self.last_heartbeat = time.time()
            heartbeat_data = {
                "timestamp": datetime.now().isoformat(),
                "unix_timestamp": self.last_heartbeat,
                "pid": os.getpid(),
                "status": "running"
            }
            
            with open(self.heartbeat_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(heartbeat_data, f, indent=2)
            
            logger.debug(f"心跳已更新: {heartbeat_data['timestamp']}")
            
        except Exception as e:
            logger.error(f"写入心跳文件失败: {e}")
    
    def _write_process_info(self):
        """Write process information"""
        try:
            process_info = {
                "pid": os.getpid(),
                "start_time": datetime.now().isoformat(),
                "start_unix_timestamp": time.time(),
                "python_executable": sys.executable,
                "working_directory": os.getcwd(),
                "command_line": " ".join(sys.argv)
            }
            
            with open(self.process_info_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(process_info, f, indent=2)
            
            logger.info(f"进程信息已写入: PID={process_info['pid']}")
            
        except Exception as e:
            logger.error(f"写入进程信息失败: {e}")
    
    def cleanup(self):
        """Cleanup monitoring files"""
        try:
            self.stop_monitoring()
            
            # Write final status
            if os.path.exists(self.heartbeat_file):
                try:
                    with open(self.heartbeat_file, 'r', encoding='utf-8') as f:
                        import json
                        data = json.load(f)
                    
                    data.update({
                        "status": "stopped",
                        "stop_time": datetime.now().isoformat(),
                        "stop_unix_timestamp": time.time()
                    })
                    
                    with open(self.heartbeat_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2)
                    
                    logger.info("进程监控清理完成")
                except Exception as e:
                    logger.error(f"清理心跳文件失败: {e}")
                    
        except Exception as e:
            logger.error(f"进程监控清理失败: {e}")
    
    @classmethod
    def check_previous_interruption(cls) -> Dict[str, Any]:
        """Check if previous process was interrupted"""
        result = {
            "was_interrupted": False,
            "last_heartbeat": None,
            "interruption_time": None,
            "process_info": None
        }
        
        heartbeat_file = "bot_heartbeat.txt"
        process_info_file = "bot_process_info.txt"
        
        try:
            # Check heartbeat file
            if os.path.exists(heartbeat_file):
                with open(heartbeat_file, 'r', encoding='utf-8') as f:
                    import json
                    heartbeat_data = json.load(f)
                
                result["last_heartbeat"] = heartbeat_data.get("timestamp")
                
                # If status is not "stopped", it was interrupted
                if heartbeat_data.get("status") != "stopped":
                    result["was_interrupted"] = True
                    result["interruption_time"] = heartbeat_data.get("timestamp")
                    logger.warning(f"检测到进程中断: 最后心跳时间 {result['last_heartbeat']}")
            
            # Check process info
            if os.path.exists(process_info_file):
                with open(process_info_file, 'r', encoding='utf-8') as f:
                    import json
                    result["process_info"] = json.load(f)
            
        except Exception as e:
            logger.error(f"检查进程中断状态失败: {e}")
        
        return result


# Global process monitor instance
process_monitor = ProcessMonitor()


def start_process_monitoring():
    """Start process monitoring"""
    process_monitor.start_monitoring()


def stop_process_monitoring():
    """Stop process monitoring"""
    process_monitor.stop_monitoring()


def check_previous_interruption() -> Dict[str, Any]:
    """Check if previous process was interrupted"""
    return ProcessMonitor.check_previous_interruption()


async def log_process_interruption_check():
    """Log process interruption check results"""
    interruption_info = check_previous_interruption()
    
    if interruption_info["was_interrupted"]:
        logger.warning("检测到上次进程被中断!")
        logger.warning(f"最后心跳时间: {interruption_info['last_heartbeat']}")
        logger.warning("可能有消息在处理过程中丢失，请检查缓存状态")
        
        # Check message cache for any pending messages
        try:
            from message_cache import message_cache
            stats = message_cache.get_cache_stats()
            if stats['total_cached'] > 0:
                logger.info(f"发现 {stats['total_cached']} 条缓存消息，将自动重试")
            else:
                logger.info("未发现缓存消息")
        except Exception as e:
            logger.error(f"检查消息缓存失败: {e}")
    else:
        logger.info("未检测到进程中断")
    
    return interruption_info
