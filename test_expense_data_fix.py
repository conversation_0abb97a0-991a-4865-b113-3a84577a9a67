#!/usr/bin/env python3
"""
测试开支数据获取修复
验证新的开支数据获取功能，包括Excel和Google Sheet数据源
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_expense_data_manager():
    """测试开支数据管理器的新功能"""
    try:
        print("测试开支数据管理器新功能")
        print("=" * 60)

        # 导入必要的模块
        from monthlyjob import ExpenseDataManager, MonthlyJobConfig
        from Parser import parse_datetime, get_last_month_range

        # 创建开支数据管理器
        expense_manager = ExpenseDataManager()

        print(f"配置信息:")
        print(f"  Excel文件: {expense_manager.excel_file}")
        print(f"  Google Sheet: {expense_manager.google_sheet_name}/{expense_manager.google_worksheet_name}")
        print(f"  数据源切换时间: {expense_manager.switch_time}")
        print()

        # 测试场景1：2025年8月的数据（应该使用Excel）
        print("测试场景1: 2025年8月数据（Excel数据源）")
        august_start = parse_datetime("2025-08-01 12:00:00")
        august_end = parse_datetime("2025-08-31 11:59:59")
        
        print(f"  查询时间范围: {august_start} 到 {august_end}")
        
        try:
            august_data = expense_manager.get_monthly_expenses(august_start, august_end)
            print(f"  获取到 {len(august_data)} 条记录")

            if august_data:
                print(f"  数据统计:")
                total_amount = sum(record.expense_amount for record in august_data)
                print(f"    总金额: ${total_amount:,.2f}")

                # 按类别统计
                categories = {}
                for record in august_data:
                    cat = record.expense_category
                    categories[cat] = categories.get(cat, 0) + record.expense_amount

                print(f"    开支类别:")
                for cat, amount in sorted(categories.items(), key=lambda x: x[1], reverse=True):
                    print(f"      {cat}: ${amount:,.2f}")

                # 显示前3条记录
                print(f"    前3条记录:")
                for i, record in enumerate(august_data[:3], 1):
                    print(f"      {i}. {record.expense_person} - {record.expense_category} - ${record.expense_amount}")

        except Exception as e:
            print(f"  获取失败: {e}")
        
        print()
        
        # 测试场景2：2025年9月的数据（应该使用Google Sheet）
        print("📊 测试场景2: 2025年9月数据（Google Sheet数据源）")
        september_start = parse_datetime("2025-09-01 12:00:00")
        september_end = parse_datetime("2025-09-30 23:59:59")
        
        print(f"  查询时间范围: {september_start} 到 {september_end}")
        
        try:
            september_data = expense_manager.get_monthly_expenses(september_start, september_end)
            print(f"  ✅ 获取到 {len(september_data)} 条记录")
            
            if september_data:
                print(f"  📈 数据统计:")
                total_amount = sum(record.expense_amount for record in september_data)
                print(f"    总金额: ${total_amount:,.2f}")
                
                # 显示前3条记录
                print(f"    前3条记录:")
                for i, record in enumerate(september_data[:3], 1):
                    print(f"      {i}. {record.expense_person} - {record.expense_category} - ${record.expense_amount}")
                    
        except Exception as e:
            print(f"  ❌ 获取失败: {e}")
        
        print()
        
        # 测试场景3：跨月查询（应该合并两个数据源）
        print("📊 测试场景3: 跨月查询（2025年8月15日到9月15日）")
        cross_start = parse_datetime("2025-08-15 12:00:00")
        cross_end = parse_datetime("2025-09-15 23:59:59")
        
        print(f"  查询时间范围: {cross_start} 到 {cross_end}")
        print(f"  预期：会从Excel和Google Sheet分别获取数据并合并")
        
        try:
            cross_data = expense_manager.get_monthly_expenses(cross_start, cross_end)
            print(f"  ✅ 获取到 {len(cross_data)} 条记录")
            
            if cross_data:
                print(f"  📈 数据统计:")
                total_amount = sum(record.expense_amount for record in cross_data)
                print(f"    总金额: ${total_amount:,.2f}")
                
                # 按时间分组统计
                august_count = sum(1 for r in cross_data if r.expense_date <= expense_manager.switch_time)
                september_count = len(cross_data) - august_count
                
                print(f"    时间分布:")
                print(f"      8月数据: {august_count} 条")
                print(f"      9月数据: {september_count} 条")
                
                # 验证数据是否按时间排序
                is_sorted = all(cross_data[i].expense_date <= cross_data[i+1].expense_date 
                              for i in range(len(cross_data)-1))
                print(f"    数据排序: {'✅ 正确' if is_sorted else '❌ 错误'}")
                    
        except Exception as e:
            print(f"  ❌ 获取失败: {e}")
        
        print()
        
        # 测试场景4：检查配置
        print("📊 测试场景4: 配置验证")
        print(f"  Excel字段映射:")
        for key, value in MonthlyJobConfig.EXCEL_FIELD_MAPPING.items():
            print(f"    {key}: {value}")
        
        print(f"  Google Sheet字段映射:")
        for key, value in MonthlyJobConfig.GOOGLE_SHEET_FIELD_MAPPING.items():
            print(f"    {key}: {value}")
        
        print()
        print("✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_monthly_job():
    """测试完整月度工作流"""
    try:
        print("🔧 测试完整月度工作流")
        print("=" * 60)
        
        from monthlyjob import manual_complete_monthly_job
        
        print("🔍 执行完整月度工作流...")
        result = manual_complete_monthly_job()
        
        print(f"\n📈 执行结果:")
        print(f"  成功: {result.get('success', False)}")
        
        if result.get('target_period'):
            period = result['target_period']
            print(f"  目标期间: {period.get('year')}-{period.get('month'):02d}")
        
        if result.get('salary_check_result'):
            salary_result = result['salary_check_result']
            print(f"  工资检查: 检查{salary_result.get('checked_records', 0)}条，更新{salary_result.get('updated_records', 0)}条")
        
        if result.get('expense_data'):
            expense_data = result['expense_data']
            print(f"  开支数据: {len(expense_data)}条记录")
            if expense_data:
                total_expense = sum(record.expense_amount for record in expense_data)
                print(f"    总开支: ${total_expense:,.2f}")
        
        errors = result.get('errors', [])
        if errors:
            print(f"  错误: {len(errors)}个")
            for i, error in enumerate(errors[:3], 1):
                print(f"    {i}. {error}")
        else:
            print(f"  错误: 无")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开支数据获取功能测试")
    print("=" * 70)
    print()

    # 测试1：开支数据管理器
    print("测试1: 开支数据管理器")
    success1 = test_expense_data_manager()
    print()

    # 测试2：完整月度工作流
    print("测试2: 完整月度工作流")
    success2 = test_complete_monthly_job()
    print()

    # 总结
    print("测试总结:")
    print(f"  开支数据管理器: {'通过' if success1 else '失败'}")
    print(f"  完整月度工作流: {'通过' if success2 else '失败'}")

    if success1 and success2:
        print("\n所有测试通过！开支数据获取功能已修复")
    else:
        print("\n部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
