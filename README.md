# 月度工作记录管理 Telegram Bot

一个功能强大的 Telegram 机器人，用于管理和分析月度工作记录，支持数据录入、查询、统计分析和自动报告生成。集成了薪资计算、返利配置、数据缓存等企业级功能。

## 🎯 主要功能

### 📝 数据管理
- **智能记录解析**：支持自然语言输入，自动解析工作记录
- **多格式支持**：兼容多种日期格式和别名系统
- **数据修正**：智能匹配算法，支持记录查找和修正
- **双存储系统**：本地 Excel 文件 + Google Sheets 云端同步
- **消息缓存**：网络异常时自动缓存，恢复后重试同步

### 📊 统计分析
- **实时查询**：按人员、场地、时间范围查询盈亏数据
- **图表生成**：
  - 人员盈亏柱状图
  - 场地盈亏柱状图  
  - 盈亏趋势图
- **多维度统计**：支持日、周、月度统计
- **数据导出**：支持Excel文件导出

### 🤖 自动化功能
- **定时报告**：自动生成日报、周报、月报
- **智能工资计算**：基于返利比例、游戏类型和盈亏自动计算工资
- **定时任务**：支持多种定时任务配置
- **健康监控**：内置应用监控和健康检查

### 🔧 管理工具
- **别名管理**：支持人员、场地、游戏类型别名配置
- **返利配置**：灵活的返利比例设置，支持个人和场地级别
- **薪资规则**：可配置的薪资计算规则
- **环境切换**：支持开发和生产环境
- **配置缓存**：内存缓存提升性能

## 🏗️ 项目结构

```
├── README.md                      # 项目说明文档
├── requirements.txt               # Python 依赖包
├── .cursorrules                   # Cursor IDE 规则配置
├── config.py                     # 主配置文件
├── config_manager.py             # 配置管理器
├── main_with_monitoring.py       # 主入口文件（带监控）
├── commander.py                  # 主要命令处理逻辑
├── Group_record.py               # 记录数据模型
├── data_models.py                # 数据模型定义
├── Parser.py                     # 数据解析器
├── storage.py                    # 数据存储管理
├── storage_service.py            # 存储服务
├── utils.py                      # 工具函数
├── handlers.py                   # 消息处理器
├── job.py                        # 定时任务
├── charts.py                     # 图表生成
├── report.py                     # 报告生成
├── salary.py                     # 工资计算
├── alias_manager.py              # 别名管理
├── error_handling.py             # 错误处理模块
├── error_strategy.py             # 错误策略
├── exceptions.py                 # 自定义异常
├── logging_config.py             # 日志配置
├── monitoring.py                 # 监控模块
├── health_check.py               # 健康检查
├── message_cache.py              # 消息缓存
├── config_cache.py               # 配置缓存
├── input_validator.py            # 输入验证
├── security_utils.py             # 安全工具
├── async_file_ops.py             # 异步文件操作
├── google_sheets_pool.py         # Google Sheets 连接池
├── aliases.json                  # 别名配置文件
├── rebate_config.json            # 返利配置文件
├── salary_config.json            # 薪资配置文件
├── group_records_dev.xlsx        # 开发环境数据文件
├── mysheetapp_new.json          # Google Sheets 凭证
├── docker/                       # Docker 配置
│   ├── Dockerfile
│   └── docker-compose.yml
├── tests/                        # 测试文件
│   ├── unit/
│   └── integration/
├── logs/                         # 日志文件目录
├── deploy.bat                    # Windows 部署脚本
├── quick_deploy.bat              # 快速部署脚本
└── *.md                          # 各种文档文件
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Telegram Bot Token
- Google Sheets API 凭证（可选）
- Windows/Linux/macOS 支持

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd monthlyjob_feature
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
创建 `.env` 文件：
```env
# 环境设置
ENV=dev  # 或 prod

# Telegram Bot Token
BOT_TOKEN_DEV=your_dev_bot_token_here
BOT_TOKEN_PROD=your_prod_bot_token_here

# 群组 Chat ID
GROUP_CHAT_ID_DEV=your_dev_group_chat_id
GROUP_CHAT_ID_PROD=your_prod_group_chat_id

# Google Sheets 配置
RECORD_SHEET_NAME_DEV=your_dev_sheet_name
RECORD_SHEET_NAME_PROD=your_prod_sheet_name
SALARY_CONFIG_SHEET_NAME_DEV=your_salary_sheet_name
SALARY_CONFIG_SHEET_NAME_PROD=your_salary_sheet_name
REBATE_CONFIG_SHEET_NAME_DEV=your_rebate_sheet_name
REBATE_CONFIG_SHEET_NAME_PROD=your_rebate_sheet_name

# Google Sheets 凭证文件路径
CREDENTIALS_FILE_DEV=mysheetapp_new.json
CREDENTIALS_FILE_PROD=prod_credentials.json

# 时区设置
TIME_ZONE=Asia/Dubai

# 日志配置（可选）
LOG_LEVEL=INFO
CONSOLE_LOG_LEVEL=INFO
FILE_LOG_LEVEL=DEBUG
JSON_LOGGING=false
TELEGRAM_LOGGING=false
```

4. **运行机器人**
```bash
# 使用监控版本（推荐）
python main_with_monitoring.py

# 或使用原版本
python commander.py
```

## 📱 使用指南

### 基本命令

#### 📊 报表生成命令
- `/daily_report` - 生成昨日盈亏汇总报告
- `/monthly_report` - 生成每周盈亏汇总报告
- `/trend_chart` - 生成盈亏趋势图
- `/venue_report` - 生成场子盈亏柱状图
- `/person_report` - 生成个人盈亏柱状图

#### 🔍 数据查询命令
- `/find 关键词` - 查找记录
- `/export_file` - 导出数据为Excel文件

#### ⚙️ 配置管理命令
- `/set_rebate 场子 [人员] 比例` - 设置输返比例
- `/add_venue venue名称 别名1 [别名2]...` - 添加新venue
- `/load_rebate_config` - 从Google Sheet加载rebate配置
- `/set_salary rebate 游戏 下限 上限 工资` - 设置工资规则
- `/load_salary_config` - 从Google Sheet加载salary配置

#### 🗂️ 缓存管理命令
- `/cache_status` - 查看消息缓存状态
- `/retry_sync` - 手动重试同步缓存消息
- `/config_cache_status` - 查看配置缓存状态
- `/refresh_config_cache` - 手动刷新配置缓存

#### ℹ️ 帮助命令
- `/help` - 显示详细帮助信息
- `/help_simple` - 显示简化帮助信息

### 记录格式示例

**标准格式**：`人员 场子 游戏 流水号 下注 赢亏 输返 工资`
**示例**：`张三 Otium BJ 12345 1000 500 0 20`

**字段说明**：
- 人员：操作员姓名
- 场子：游戏场所名称
- 游戏：游戏类型（BJ、UTH、俄罗斯、百家乐等）
- 流水号：唯一标识符
- 下注：下注金额（整数）
- 赢亏：盈亏金额（正数为盈利，负数为亏损）
- 输返：输返金额（通常为0，系统自动计算）
- 工资：工资金额（可为0，系统自动计算）

### 修正记录示例
```
更正：张三 Otium BJ 12345 1000 600 0 25
```
系统会智能匹配相关记录并提供选择。

## ⚙️ 配置说明

### 工资计算规则
系统支持基于返利比例、游戏类型和盈亏范围的灵活工资计算：

**默认规则**：
- **10% 输返且赢**：工资 20
- **20% 输返且赢**：工资 50  
- **10% 输返且输**：工资 10
- **20% 输返且输**：工资 20

**自定义规则**：可通过 `/set_salary` 命令或 Google Sheets 配置更复杂的规则。

### 返利比例配置
在 `rebate_config.json` 中配置各场地的返利比例：
```json
{
  "GB": {"默认比例": 0.1},
  "Otium": {"默认比例": 0.2},
  "Iveria": {
    "俊": 0.2,
    "敏": 0.2,
    "默认比例": 0.1
  },
  "默认比例": 0.1
}
```

### 薪资规则配置
在 `salary_config.json` 中配置薪资计算规则：
```json
{
  "0.1": {
    "BJ": [
      {
        "enabled": true,
        "profit_min": 200,
        "profit_max": 999,
        "salary": 20,
        "description": "BJ 赢 200~999"
      }
    ]
  },
  "0.2": {
    "BJ": [
      {
        "enabled": true,
        "profit_min": 200,
        "profit_max": 999,
        "salary": 50,
        "description": "BJ 赢 200~999"
      }
    ]
  }
}
```

### 别名系统
在 `aliases.json` 中配置别名：
```json
{
  "persons": {
    "张三": ["zs", "张", "zhangsan"]
  },
  "venues": {
    "GB": ["gb", "g b"],
    "Otium": ["ot", "otium"]
  },
  "games": {
    "俄罗斯": ["俄", "ru", "俄羅斯"],
    "BJ": ["bj", "blackjack", "21点"]
  }
}
```

## 🔄 定时任务

系统支持以下定时任务（基于迪拜时区 UTC+4）：
- **每日报告**：每天 08:04 自动生成昨日统计
- **周报**：每天 08:06 生成周度统计
- **月报**：每天 08:08 生成月度统计
- **趋势图**：每天 08:12 发送盈亏趋势分析
- **场子报告**：每月1日 08:15 生成场子盈亏柱状图
- **个人报告**：每月1日 08:20 生成个人盈亏柱状图
- **配置刷新**：每天 08:45 刷新配置缓存
- **消息重试**：每30分钟检查一次，每天 07:00 重试同步

## 🛠️ 开发指南

### 核心模块

#### main_with_monitoring.py
主入口文件，包含：
- 应用监控和健康检查
- 优雅关闭机制
- 信号处理和资源管理

#### commander.py
主要的命令处理逻辑，包含：
- 智能匹配算法（支持加权评分）
- 多格式日期解析
- 记录修正功能
- 错误处理和重试机制

#### data_models.py
数据模型定义：
- GameRecord：游戏记录模型
- RebateRule：返利规则模型
- SalaryRule：薪资规则模型
- 数据验证器

#### Parser.py  
数据解析器，支持：
- 自然语言解析
- 多种输入格式
- 时间范围计算

#### storage.py & storage_service.py
数据存储管理：
- Excel 文件操作
- Google Sheets 同步
- 数据缓存机制
- 异步文件操作

#### error_handling.py
错误处理模块：
- 自定义异常类
- 重试机制和熔断器
- 错误恢复策略

#### logging_config.py
日志系统：
- 结构化日志记录
- 多输出格式支持
- 性能监控

### 扩展开发

1. **添加新命令**：在 `commander.py` 中添加处理函数
2. **自定义图表**：在 `charts.py` 中添加新的图表类型
3. **扩展解析器**：在 `Parser.py` 中添加新的解析规则
4. **新增数据模型**：在 `data_models.py` 中定义新的数据结构
5. **自定义错误处理**：在 `error_handling.py` 中添加新的错误类型

## 🚀 部署

### Windows 部署
使用提供的部署脚本：
```batch
# 标准部署
deploy.bat

# 快速部署
quick_deploy.bat
```

### Docker 部署
使用 Docker 容器化部署：
```bash
# 构建镜像
docker build -t monthlyjob-bot .

# 运行容器
docker-compose up -d
```

### 服务化部署
使用 NSSM 将机器人注册为 Windows 服务：
```bash
nssm install monthlyjob-bot "C:\Python\python.exe" "C:\path\to\main_with_monitoring.py"
nssm start monthlyjob-bot
```

### 生产环境部署
1. 设置环境变量 `ENV=prod`
2. 配置生产环境的 Bot Token 和 Chat ID
3. 确保 Google Sheets API 凭证正确配置
4. 启用日志记录和监控
5. 设置自动重启机制

## 🐛 故障排除

### 常见问题

1. **机器人无响应**
   - 检查 Bot Token 是否正确
   - 确认机器人已添加到群组
   - 检查网络连接
   - 查看日志文件中的错误信息

2. **数据同步失败**
   - 验证 Google Sheets API 凭证
   - 检查表格权限设置
   - 确认表格名称配置
   - 使用 `/cache_status` 查看缓存状态
   - 使用 `/retry_sync` 手动重试同步

3. **图表生成失败**
   - 检查 matplotlib 字体配置
   - 确认数据文件存在
   - 验证时间范围设置
   - 检查中文字体支持

4. **配置加载失败**
   - 使用 `/config_cache_status` 查看配置状态
   - 使用 `/refresh_config_cache` 刷新配置
   - 检查 JSON 配置文件格式
   - 验证 Google Sheets 连接

5. **工资计算错误**
   - 检查薪资规则配置
   - 验证返利比例设置
   - 确认游戏类型匹配

### 日志调试
- 开发环境：详细调试信息输出到控制台和日志文件
- 生产环境：关键信息记录到日志文件
- 使用 `/help` 命令查看所有可用功能
- 检查 `logs/` 目录下的日志文件

### 健康检查
系统提供内置健康检查功能：
- 应用状态监控
- 内存使用监控
- 任务执行状态
- 网络连接状态

## 📄 更新日志

### 最新改进
- ✅ 企业级架构重构
- ✅ 异步编程模式
- ✅ 智能错误处理和重试机制
- ✅ 消息缓存和同步机制
- ✅ 配置缓存和热重载
- ✅ 健康监控和日志系统
- ✅ Docker 容器化支持
- ✅ 薪资规则灵活配置
- ✅ 返利比例多级设置
- ✅ 数据模型标准化
- ✅ 安全输入验证
- ✅ 性能优化和资源管理

### 详细改进记录
- [MONTHLYJOB_IMPROVEMENTS.md](MONTHLYJOB_IMPROVEMENTS.md) - 月度工作功能改进
- [ERROR_HANDLING_IMPROVEMENTS.md](ERROR_HANDLING_IMPROVEMENTS.md) - 错误处理改进
- [RESOURCE_MANAGEMENT_IMPROVEMENTS.md](RESOURCE_MANAGEMENT_IMPROVEMENTS.md) - 资源管理改进
- [SECURITY_FIXES_SUMMARY.md](SECURITY_FIXES_SUMMARY.md) - 安全修复总结
- [ENHANCED_DEPLOYMENT_SUMMARY.md](ENHANCED_DEPLOYMENT_SUMMARY.md) - 增强部署总结

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 遵循项目代码规范（参考 `.cursorrules` 文件）
4. 添加必要的测试用例
5. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
6. 推送到分支 (`git push origin feature/AmazingFeature`)
7. 开启 Pull Request

### 开发规范
- 使用 Python 3.8+ 语法特性
- 遵循 PEP 8 代码风格
- 使用类型注解
- 优先使用异步编程
- 添加适当的错误处理
- 编写单元测试
- 更新相关文档

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 创建 Issue
- 联系项目维护者
- 查看文档和示例
- 使用 `/help` 命令获取帮助

## 🔗 相关链接

- [Telegram Bot API](https://core.telegram.org/bots/api)
- [Google Sheets API](https://developers.google.com/sheets/api)
- [Python Telegram Bot](https://python-telegram-bot.readthedocs.io/)
- [Gspread](https://gspread.readthedocs.io/)

---

**注意**：本项目仅用于学习和研究目的，请遵守当地法律法规。