#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Excel reading functionality only
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_excel_reading():
    """Test Excel reading functionality"""
    try:
        print("Testing Excel reading functionality")
        print("=" * 50)
        
        # Import modules
        from monthlyjob import ExpenseDataManager, MonthlyJobConfig
        from Parser import parse_datetime
        
        # Create manager
        manager = ExpenseDataManager()
        
        print(f"Excel file: {manager.excel_file}")
        print(f"Switch time: {manager.switch_time}")
        
        # Check if Excel file exists
        if os.path.exists(manager.excel_file):
            print(f"Excel file exists: {manager.excel_file}")
        else:
            print(f"Excel file does not exist: {manager.excel_file}")
            return False
        
        # Test August 2025 data (should use Excel)
        start_date = parse_datetime("2025-08-01 00:00:00")
        end_date = parse_datetime("2025-08-31 23:59:59")
        
        print(f"Query time range: {start_date} to {end_date}")
        
        # Test Excel reading directly
        print("Testing Excel reading...")
        records = manager._get_expenses_from_excel(start_date, end_date)
        print(f"Got {len(records)} records from Excel")
        
        if records:
            print("First 3 records:")
            for i, record in enumerate(records[:3], 1):
                print(f"  {i}. {record.expense_person} - {record.expense_category} - ${record.expense_amount}")
                print(f"     Date: {record.expense_date}")
        
        # Test field mapping
        print("\nField mapping test:")
        print("Excel field mapping:")
        for key, value in MonthlyJobConfig.EXCEL_FIELD_MAPPING.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"Excel reading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_file_structure():
    """Test Excel file structure"""
    try:
        print("\nTesting Excel file structure")
        print("=" * 50)
        
        import openpyxl
        
        excel_file = "G国先锋开支明细.xlsx"
        workbook = openpyxl.load_workbook(excel_file)
        worksheet = workbook.active
        
        print(f"Excel file: {excel_file}")
        print(f"Max row: {worksheet.max_row}")
        print(f"Max column: {worksheet.max_column}")
        
        # Read headers
        print("Headers:")
        for col_idx in range(1, min(worksheet.max_column + 1, 15)):  # Limit to 15 columns
            cell_value = worksheet.cell(row=1, column=col_idx).value
            print(f"  Column {col_idx}: {cell_value}")
        
        # Read first few data rows
        print("\nFirst 3 data rows:")
        for row_idx in range(2, min(worksheet.max_row + 1, 5)):  # Rows 2-4
            row_data = []
            for col_idx in range(1, min(worksheet.max_column + 1, 8)):  # First 8 columns
                cell_value = worksheet.cell(row=row_idx, column=col_idx).value
                row_data.append(str(cell_value) if cell_value else "")
            print(f"  Row {row_idx}: {row_data}")
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"Excel structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Excel Reading Test")
    print("=" * 60)
    
    # Test 1: Excel file structure
    success1 = test_excel_file_structure()
    
    # Test 2: Excel reading functionality
    success2 = test_excel_reading()
    
    print("\nTest Summary:")
    print(f"Excel structure: {'PASS' if success1 else 'FAIL'}")
    print(f"Excel reading: {'PASS' if success2 else 'FAIL'}")
    
    if success1 and success2:
        print("\nAll tests passed!")
    else:
        print("\nSome tests failed")

if __name__ == "__main__":
    main()
