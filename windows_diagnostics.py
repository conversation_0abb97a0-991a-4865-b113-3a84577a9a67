#!/usr/bin/env python3
"""
Windows Server 2012 R2 诊断工具
专门诊断和解决配置文件权限问题
"""

import os
import sys
import json
import subprocess
import logging
from pathlib import Path
from datetime import datetime
import tempfile

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WindowsDiagnostics:
    """Windows 系统诊断工具"""
    
    def __init__(self):
        self.issues_found = []
        self.recommendations = []
        
    def check_system_info(self):
        """检查系统信息"""
        print("🖥️ 系统信息检查")
        print("-" * 40)
        
        try:
            # Windows 版本
            result = subprocess.run(['ver'], shell=True, capture_output=True, text=True)
            version = result.stdout.strip()
            print(f"Windows 版本: {version}")
            
            # Python 版本
            print(f"Python 版本: {sys.version}")
            
            # 当前用户
            username = os.getenv('USERNAME', 'Unknown')
            print(f"当前用户: {username}")
            
            # 工作目录
            print(f"工作目录: {os.getcwd()}")
            
            # 检查是否为管理员权限
            try:
                is_admin = os.getuid() == 0
            except AttributeError:
                # Windows 系统
                import ctypes
                is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
            
            print(f"管理员权限: {'是' if is_admin else '否'}")
            
            if not is_admin:
                self.issues_found.append("当前用户没有管理员权限")
                self.recommendations.append("建议以管理员身份运行应用程序")
                
        except Exception as e:
            print(f"系统信息检查失败: {e}")
    
    def check_file_permissions(self):
        """检查文件权限"""
        print("\n📁 文件权限检查")
        print("-" * 40)
        
        config_files = [
            'rebate_config.json',
            'salary_config.json',
            'mysheetapp.json',
            '.env'
        ]
        
        for filepath in config_files:
            print(f"\n检查文件: {filepath}")
            
            if not os.path.exists(filepath):
                print(f"  状态: 文件不存在")
                continue
            
            try:
                # 获取文件属性
                result = subprocess.run(['attrib', filepath], capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    attrs = result.stdout.strip()
                    print(f"  属性: {attrs}")
                    
                    # 检查问题属性
                    if 'H' in attrs:
                        print(f"  ⚠️ 文件被设置为隐藏")
                        self.issues_found.append(f"{filepath} 被设置为隐藏")
                        self.recommendations.append(f"运行 'attrib -H {filepath}' 移除隐藏属性")
                    
                    if 'R' in attrs:
                        print(f"  ⚠️ 文件被设置为只读")
                        self.issues_found.append(f"{filepath} 被设置为只读")
                        self.recommendations.append(f"运行 'attrib -R {filepath}' 移除只读属性")
                
                # 测试文件读取
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"  读取: 成功 ({len(content)} 字符)")
                except Exception as e:
                    print(f"  读取: 失败 - {e}")
                    self.issues_found.append(f"{filepath} 读取失败: {e}")
                
                # 测试文件写入
                try:
                    # 创建临时备份
                    backup_content = None
                    if os.path.exists(filepath):
                        with open(filepath, 'r', encoding='utf-8') as f:
                            backup_content = f.read()
                    
                    # 测试写入
                    test_content = backup_content or '{"test": true}'
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(test_content)
                    
                    print(f"  写入: 成功")
                    
                    # 恢复原内容
                    if backup_content:
                        with open(filepath, 'w', encoding='utf-8') as f:
                            f.write(backup_content)
                            
                except Exception as e:
                    print(f"  写入: 失败 - {e}")
                    self.issues_found.append(f"{filepath} 写入失败: {e}")
                    
            except Exception as e:
                print(f"  检查失败: {e}")
    
    def check_json_validity(self):
        """检查 JSON 文件有效性"""
        print("\n📄 JSON 文件有效性检查")
        print("-" * 40)
        
        json_files = ['rebate_config.json', 'salary_config.json']
        
        for filepath in json_files:
            print(f"\n检查 JSON: {filepath}")
            
            if not os.path.exists(filepath):
                print(f"  状态: 文件不存在")
                continue
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"  JSON 格式: 有效")
                print(f"  顶级键数量: {len(data)}")
                
                # 检查 rebate 配置特定问题
                if filepath == 'rebate_config.json':
                    if "默认比例" in data:
                        default_ratio = data["默认比例"]
                        print(f"  全局默认比例: {default_ratio}")
                        
                        # 检查是否有重复的默认比例
                        venue_defaults = []
                        for key, value in data.items():
                            if key != "默认比例" and isinstance(value, dict):
                                if "默认比例" in value:
                                    venue_defaults.append((key, value["默认比例"]))
                        
                        if venue_defaults:
                            print(f"  场馆默认比例: {len(venue_defaults)} 个")
                            for venue, ratio in venue_defaults[:3]:  # 显示前3个
                                print(f"    {venue}: {ratio}")
                
            except json.JSONDecodeError as e:
                print(f"  JSON 格式: 无效 - {e}")
                self.issues_found.append(f"{filepath} JSON 格式错误: {e}")
                self.recommendations.append(f"修复 {filepath} 的 JSON 格式错误")
            except Exception as e:
                print(f"  检查失败: {e}")
    
    def test_file_operations(self):
        """测试文件操作"""
        print("\n🧪 文件操作测试")
        print("-" * 40)
        
        test_file = "test_file_operations.json"
        test_data = {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "message": "这是一个测试文件"
        }
        
        try:
            # 测试创建文件
            print("测试创建文件...")
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            print("  创建文件: 成功")
            
            # 测试读取文件
            print("测试读取文件...")
            with open(test_file, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
            print("  读取文件: 成功")
            
            # 测试修改文件
            print("测试修改文件...")
            test_data["modified"] = True
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            print("  修改文件: 成功")
            
            # 测试文件属性
            print("测试文件属性...")
            result = subprocess.run(['attrib', test_file], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                attrs = result.stdout.strip()
                print(f"  文件属性: {attrs}")
            
            # 清理测试文件
            os.remove(test_file)
            print("  清理测试文件: 成功")
            
        except Exception as e:
            print(f"  文件操作测试失败: {e}")
            self.issues_found.append(f"文件操作测试失败: {e}")
            
            # 清理测试文件
            try:
                if os.path.exists(test_file):
                    os.remove(test_file)
            except:
                pass
    
    def generate_report(self):
        """生成诊断报告"""
        print("\n📋 诊断报告")
        print("=" * 50)
        
        if not self.issues_found:
            print("🎉 未发现问题！系统状态良好。")
        else:
            print(f"⚠️ 发现 {len(self.issues_found)} 个问题:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"  {i}. {issue}")
        
        if self.recommendations:
            print(f"\n💡 建议 ({len(self.recommendations)} 项):")
            for i, rec in enumerate(self.recommendations, 1):
                print(f"  {i}. {rec}")
        
        print("\n🔧 快速修复命令:")
        print("  python fix_windows_hidden_files.py")
        print("\n📞 如需进一步帮助:")
        print("  1. 检查 Windows 事件日志")
        print("  2. 确认防病毒软件设置")
        print("  3. 验证用户权限配置")

def main():
    """主函数"""
    print("🔍 Windows Server 2012 R2 诊断工具")
    print("=" * 50)
    print("专门诊断 Telegram Bot 配置文件权限问题")
    print()
    
    if os.name != 'nt':
        print("❌ 此工具仅适用于 Windows 系统")
        sys.exit(1)
    
    diagnostics = WindowsDiagnostics()
    
    try:
        diagnostics.check_system_info()
        diagnostics.check_file_permissions()
        diagnostics.check_json_validity()
        diagnostics.test_file_operations()
        diagnostics.generate_report()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 诊断被用户中断")
    except Exception as e:
        print(f"\n\n💥 诊断过程中发生错误: {e}")
        logger.exception("诊断工具异常")
    
    print("\n" + "=" * 50)
    print("诊断完成！")

if __name__ == "__main__":
    main()
