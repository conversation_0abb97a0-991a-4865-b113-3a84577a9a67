#!/usr/bin/env python3
"""
输入验证和清理模块 - 安全修复
提供统一的输入验证、清理和用户授权检查功能
"""

import re
import os
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
from telegram import Update
from telegram.ext import ContextTypes

from config import Config

logger = logging.getLogger(__name__)


class SecurityValidator:
    """安全验证器 - 处理输入验证、路径安全和用户授权"""
    
    # 允许的字符集定义
    ALLOWED_PATTERNS = {
        '人员': re.compile(r'^[a-zA-Z0-9\u4e00-\u9fa5_\-\.]{1,50}$'),  # 中英文、数字、下划线、横线、点
        '场子': re.compile(r'^[a-zA-Z0-9\u4e00-\u9fa5_\-\.]{1,50}$'),  # 同上
        '游戏': re.compile(r'^[a-zA-Z0-9\u4e00-\u9fa5_\-\.]{1,30}$'),  # 游戏名称
        '卡号': re.compile(r'^[a-zA-Z0-9]{1,20}$'),                    # 只允许字母数字
        '备注': re.compile(r'^[^<>\"\'&;]{0,200}$'),                   # 禁止HTML/SQL危险字符
        'filename': re.compile(r'^[a-zA-Z0-9_\-\.]{1,100}$'),         # 文件名
        'number': re.compile(r'^-?\d+$'),                             # 整数
        'decimal': re.compile(r'^-?\d*\.?\d+$'),                      # 小数
        'date': re.compile(r'^\d{4}-\d{2}-\d{2}$'),                  # 日期格式
    }
    
    # 危险字符模式
    DANGEROUS_PATTERNS = [
        re.compile(r'[<>"\'\&;]'),           # HTML/SQL注入字符
        re.compile(r'\.\.[\\/]'),            # 路径遍历
        re.compile(r'[|&;$`\\]'),            # Shell注入字符
        re.compile(r'(script|eval|exec)'),    # 危险函数名
    ]
    
    # 授权用户列表（从环境变量读取）
    AUTHORIZED_USERS = set()
    ADMIN_USERS = set()
    
    @classmethod
    def initialize(cls):
        """初始化授权用户列表"""
        try:
            # 从环境变量读取授权用户
            allowed_users = os.getenv("ALLOWED_USERS", "").strip()
            if allowed_users:
                cls.AUTHORIZED_USERS = set(user.strip() for user in allowed_users.split(",") if user.strip())
            
            # 从环境变量读取管理员用户
            admin_users = os.getenv("ADMIN_USERS", "").strip()
            if admin_users:
                cls.ADMIN_USERS = set(user.strip() for user in admin_users.split(",") if user.strip())
            
            logger.info(f"安全验证器初始化完成: {len(cls.AUTHORIZED_USERS)} 个授权用户, {len(cls.ADMIN_USERS)} 个管理员")
            
        except Exception as e:
            logger.error(f"安全验证器初始化失败: {e}")

    @classmethod
    def sanitize_input(cls, value: Any, max_length: int = 200) -> str:
        """
        清理输入数据
        
        Args:
            value: 输入值
            max_length: 最大长度限制
            
        Returns:
            清理后的字符串
        """
        if value is None:
            return ""
            
        # 转换为字符串
        text = str(value).strip()
        
        # 长度限制
        if len(text) > max_length:
            text = text[:max_length]
            logger.warning(f"输入超长被截断: {len(str(value))} -> {max_length}")
        
        # 移除危险字符
        for pattern in cls.DANGEROUS_PATTERNS:
            if pattern.search(text):
                text = pattern.sub('', text)
                logger.warning(f"输入包含危险字符，已清理: {value}")
        
        return text

    @classmethod
    def validate_field(cls, field_type: str, value: str) -> Tuple[bool, str]:
        """
        验证字段值
        
        Args:
            field_type: 字段类型
            value: 字段值
            
        Returns:
            (是否有效, 错误信息)
        """
        if not value:
            return True, ""  # 空值允许
            
        # 获取对应的验证模式
        pattern = cls.ALLOWED_PATTERNS.get(field_type)
        if not pattern:
            return False, f"未知字段类型: {field_type}"
        
        if not pattern.match(value):
            return False, f"字段格式不符合要求，只允许字母、数字、中文、下划线、横线和点"
        
        return True, ""

    @classmethod
    def validate_number(cls, value: str, min_val: Optional[int] = None, max_val: Optional[int] = None) -> Tuple[bool, str]:
        """验证数字输入"""
        try:
            num = int(value)
            if min_val is not None and num < min_val:
                return False, f"数值不能小于 {min_val}"
            if max_val is not None and num > max_val:
                return False, f"数值不能大于 {max_val}"
            return True, ""
        except ValueError:
            return False, "必须是有效的整数"

    @classmethod
    def validate_decimal(cls, value: str, min_val: Optional[float] = None, max_val: Optional[float] = None) -> Tuple[bool, str]:
        """验证小数输入"""
        try:
            num = float(value)
            if min_val is not None and num < min_val:
                return False, f"数值不能小于 {min_val}"
            if max_val is not None and num > max_val:
                return False, f"数值不能大于 {max_val}"
            return True, ""
        except ValueError:
            return False, "必须是有效的数字"

    @classmethod
    def validate_file_path(cls, file_path: str, allowed_dirs: Optional[List[str]] = None) -> Tuple[bool, str]:
        """
        验证文件路径安全性
        
        Args:
            file_path: 文件路径
            allowed_dirs: 允许的目录列表
            
        Returns:
            (是否安全, 错误信息)
        """
        try:
            # 规范化路径
            path = Path(file_path).resolve()
            
            # 检查路径遍历攻击
            if '..' in str(path) or str(path).startswith('/'):
                return False, "路径包含非法字符"
            
            # 检查是否在允许的目录内
            if allowed_dirs:
                current_dir = Path.cwd()
                allowed_paths = [current_dir / allowed_dir for allowed_dir in allowed_dirs]
                
                if not any(path.is_relative_to(allowed_path) for allowed_path in allowed_paths):
                    return False, f"文件路径不在允许的目录内: {allowed_dirs}"
            
            # 检查文件名
            filename = path.name
            if not cls.ALLOWED_PATTERNS['filename'].match(filename):
                return False, "文件名包含非法字符"
            
            return True, ""
            
        except Exception as e:
            return False, f"路径验证失败: {e}"

    @classmethod
    def check_user_authorization(cls, update: Update, required_level: str = "user") -> Tuple[bool, str]:
        """
        检查用户授权
        
        Args:
            update: Telegram更新对象
            required_level: 需要的权限级别 ("user" 或 "admin")
            
        Returns:
            (是否授权, 错误信息)
        """
        try:
            user = update.effective_user
            chat = update.effective_chat
            
            if not user:
                return False, "无法获取用户信息"
            
            user_id = str(user.id)
            username = user.username or ""
            chat_id = str(chat.id)
            
            # 记录访问尝试
            logger.info(f"用户访问检查: ID={user_id}, Username={username}, Chat={chat_id}, Level={required_level}")
            
            # 检查群组授权
            if chat_id != str(Config.GROUP_CHAT_ID):
                return False, f"未授权的群组 (ID: {chat_id})"
            
            # 如果没有配置用户限制，只检查群组
            if not cls.AUTHORIZED_USERS and not cls.ADMIN_USERS:
                return True, ""
            
            # 检查管理员权限
            if required_level == "admin":
                if user_id in cls.ADMIN_USERS or username in cls.ADMIN_USERS:
                    return True, ""
                return False, "需要管理员权限"
            
            # 检查用户权限
            if (user_id in cls.AUTHORIZED_USERS or username in cls.AUTHORIZED_USERS or
                user_id in cls.ADMIN_USERS or username in cls.ADMIN_USERS):
                return True, ""
            
            return False, f"用户未授权 (ID: {user_id})"
            
        except Exception as e:
            logger.error(f"用户授权检查失败: {e}")
            return False, f"授权检查失败: {e}"

    @classmethod
    def validate_message_data(cls, fields: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证消息数据的完整性和安全性
        
        Args:
            fields: 消息字段字典
            
        Returns:
            (是否有效, 错误列表)
        """
        errors = []
        
        # 验证各个字段
        field_types = {
            '人员': '人员',
            '场子': '场子', 
            '游戏': '游戏',
            '卡号': '卡号',
            '备注': '备注'
        }
        
        for field_name, field_type in field_types.items():
            value = fields.get(field_name)
            if value is not None:
                is_valid, error = cls.validate_field(field_type, str(value))
                if not is_valid:
                    errors.append(f"{field_name}: {error}")
        
        # 验证数字字段
        try:
            from alias_manager import get_numeric_field_names
            number_fields = get_numeric_field_names()
        except Exception:
            # fallback到硬编码
            number_fields = ['起始本金', '点码', '工资', '输反', '盈利']
        for field_name in number_fields:
            value = fields.get(field_name)
            if value is not None:
                is_valid, error = cls.validate_number(str(value), min_val=-999999999, max_val=999999999)
                if not is_valid:
                    errors.append(f"{field_name}: {error}")
        
        return len(errors) == 0, errors


class SecureFileManager:
    """安全文件管理器"""
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'.json', '.xlsx', '.xls', '.csv', '.txt', '.log'}
    
    # 允许操作的目录
    ALLOWED_DIRS = ['.', 'data', 'logs', 'backups']
    
    @classmethod
    def safe_open(cls, file_path: str, mode: str = 'r', encoding: str = 'utf-8', **kwargs):
        """
        安全的文件打开操作
        
        Args:
            file_path: 文件路径
            mode: 打开模式
            encoding: 编码
            **kwargs: 其他参数
            
        Returns:
            文件对象
            
        Raises:
            SecurityError: 当文件路径不安全时
            FileNotFoundError: 当文件不存在时
        """
        # 验证文件路径
        is_safe, error = SecurityValidator.validate_file_path(file_path, cls.ALLOWED_DIRS)
        if not is_safe:
            raise SecurityError(f"文件路径不安全: {error}")
        
        # 检查文件扩展名
        path = Path(file_path)
        if path.suffix.lower() not in cls.ALLOWED_EXTENSIONS:
            raise SecurityError(f"不允许的文件类型: {path.suffix}")
        
        # 如果是读取模式，检查文件是否存在
        if 'r' in mode and not path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 创建目录（如果需要）
        if 'w' in mode or 'a' in mode:
            path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"安全打开文件: {file_path}, 模式: {mode}")
        return open(file_path, mode, encoding=encoding, **kwargs)

    @classmethod
    def safe_load_json(cls, file_path: str) -> Dict[str, Any]:
        """
        安全加载JSON文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            JSON数据
        """
        import json
        
        try:
            with cls.safe_open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.debug(f"成功加载JSON文件: {file_path}")
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON文件格式错误: {file_path}, 错误: {e}")
            raise SecurityError(f"JSON文件格式错误: {e}")
        except Exception as e:
            logger.error(f"加载JSON文件失败: {file_path}, 错误: {e}")
            raise

    @classmethod
    def safe_save_json(cls, data: Dict[str, Any], file_path: str, backup: bool = True) -> None:
        """
        安全保存JSON文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            backup: 是否创建备份
        """
        import json
        from datetime import datetime
        
        try:
            # 创建备份
            if backup and Path(file_path).exists():
                backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                Path(file_path).rename(backup_path)
                logger.info(f"创建备份文件: {backup_path}")
            
            # 保存新文件
            with cls.safe_open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存JSON文件: {file_path}")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {file_path}, 错误: {e}")
            raise


class SecurityError(Exception):
    """安全相关异常"""
    pass


# 初始化安全验证器
SecurityValidator.initialize()

# 向后兼容的别名
InputValidator = SecurityValidator