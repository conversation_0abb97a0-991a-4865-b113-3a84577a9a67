#!/usr/bin/env python3
"""
配置管理模块
处理 Rebate 和 Salary 配置的读取、写入、同步等操作
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from config import Config
from async_file_ops import get_file_manager
from google_sheets_pool import get_sheets_manager
from security_utils import InputValidator

logger = logging.getLogger(__name__)

class ConfigurationError(Exception):
    """配置相关错误"""
    pass

class RebateConfigManager:
    """Rebate 配置管理器"""
    
    def __init__(self):
        self.file_manager = get_file_manager()
        self.config_file = Config.REBATE_FILE
        self.sheet_name = Config.REBATE_CONFIG_SHEET_NAME
    
    async def load_local_config(self) -> Dict[str, Any]:
        """异步加载本地 rebate 配置"""
        try:
            config = await self.file_manager.read_json_async(self.config_file)
            logger.debug(f"本地 rebate 配置加载成功，包含 {len(config)} 项")
            return config
        except Exception as e:
            logger.error(f"加载本地 rebate 配置失败: {e}")
            return {}
    
    async def save_local_config(self, config: Dict[str, Any]) -> bool:
        """异步保存本地 rebate 配置"""
        try:
            logger.info(f"开始保存本地 rebate 配置到 {self.config_file}，包含 {len(config)} 项")

            # 清理配置数据
            cleaned_config = self._clean_rebate_config(config)
            logger.debug(f"配置数据清理完成，清理后包含 {len(cleaned_config)} 项")

            # 异步写入文件
            logger.debug(f"调用 file_manager.write_json_async 写入文件: {self.config_file}")
            success = await self.file_manager.write_json_async(
                self.config_file, cleaned_config, is_sensitive=True
            )

            if success:
                logger.info(f"本地 rebate 配置保存成功: {self.config_file}，包含 {len(config)} 项")
            else:
                logger.error(f"本地 rebate 配置保存失败: {self.config_file}")

            return success
        except Exception as e:
            logger.error(f"保存本地 rebate 配置异常 {self.config_file}: {e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return False

    def _clean_rebate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """清理和验证 rebate 配置结构"""
        cleaned_config = {}
        global_default = None

        for key, value in config.items():
            if key == "默认比例":
                # 保存全局默认比例
                if isinstance(value, (int, float)) and 0 <= value <= 1:
                    global_default = value
                continue

            # 处理场馆配置
            if isinstance(value, dict):
                cleaned_venue = {}
                for person_key, person_value in value.items():
                    if isinstance(person_value, (int, float)) and 0 <= person_value <= 1:
                        cleaned_venue[person_key] = person_value

                if cleaned_venue:  # 只有当场馆有有效配置时才添加
                    cleaned_config[key] = cleaned_venue
            elif isinstance(value, (int, float)) and 0 <= value <= 1:
                # 处理直接的比例值（可能是错误格式）
                logger.warning(f"发现可能的格式错误，将 {key}:{value} 转换为场馆默认配置")
                cleaned_config[key] = {"默认比例": value}

        # 添加全局默认比例（如果存在）
        if global_default is not None:
            cleaned_config["默认比例"] = global_default

        return cleaned_config
    
    async def load_from_google_sheet(self) -> Dict[str, Any]:
        """从 Google Sheet 加载 rebate 配置"""
        try:
            sheets_manager = await get_sheets_manager()
            sheet_data = await sheets_manager.read_sheet(self.sheet_name)
            
            if not sheet_data:
                raise ConfigurationError("rebate_ratio_config 表格为空")
            
            # 解析表格数据
            rebate_config = {}
            headers = sheet_data[0] if sheet_data else []
            
            # 验证表头
            expected_headers = ["场子", "人员", "比例", "备注"]
            if not all(header in headers for header in expected_headers[:3]):
                raise ConfigurationError(f"表格格式错误，期望的列: {expected_headers}")
            
            # 获取列索引
            venue_col = headers.index("场子")
            person_col = headers.index("人员")
            ratio_col = headers.index("比例")
            
            for row in sheet_data[1:]:  # 跳过标题行
                if len(row) <= max(venue_col, person_col, ratio_col):
                    continue
                
                venue = row[venue_col].strip()
                person = row[person_col].strip()
                ratio_str = row[ratio_col].strip()
                
                if not venue or not ratio_str:
                    continue
                
                try:
                    ratio = float(ratio_str)
                    if not (0 <= ratio <= 1):
                        logger.warning(f"忽略无效比例: {venue}/{person} = {ratio}")
                        continue
                except ValueError:
                    logger.warning(f"忽略无效比例格式: {venue}/{person} = {ratio_str}")
                    continue
                
                # 处理全局默认比例
                if venue == "默认" and (person == "*" or person == ""):
                    rebate_config["默认比例"] = ratio
                    continue

                # 跳过空的场子名称
                if not venue or venue == "默认":
                    continue

                # 处理场子配置
                if venue not in rebate_config:
                    rebate_config[venue] = {}

                if person == "*" or person == "":
                    # 场子默认比例
                    rebate_config[venue]["默认比例"] = ratio
                else:
                    # 个人比例
                    rebate_config[venue][person] = ratio
            
            logger.info(f"从 Google Sheet 加载 rebate 配置成功，包含 {len(rebate_config)} 项")
            return rebate_config
            
        except Exception as e:
            logger.error(f"从 Google Sheet 加载 rebate 配置失败: {e}")
            raise ConfigurationError(f"读取 Google Sheet 失败: {e}")
    
    async def update_google_sheet(self, venue: str, person: Optional[str] = None, 
                                 ratio: float = None) -> Tuple[bool, str]:
        """更新 Google Sheet 中的 rebate 配置"""
        try:
            # 输入验证
            if venue:
                is_valid, error = InputValidator.validate_field('场子', venue)
                if not is_valid:
                    return False, f"场子名称无效: {error}"
            
            if person:
                is_valid, error = InputValidator.validate_field('人员', person)
                if not is_valid:
                    return False, f"人员名称无效: {error}"
            
            if ratio is not None and not (0 <= ratio <= 1):
                return False, "比例必须在 0-1 之间"
            
            sheets_manager = await get_sheets_manager()
            sheet_data = await sheets_manager.read_sheet(self.sheet_name)
            
            if not sheet_data:
                return False, "rebate_ratio_config 表格为空"
            
            # 查找要更新的行
            target_venue = venue
            target_person = person if person else "*"
            found_row = None
            
            for i, row in enumerate(sheet_data[1:], start=2):  # 跳过标题行，从第2行开始
                if len(row) >= 3:
                    row_venue = row[0].strip()
                    row_person = row[1].strip()
                    
                    if row_venue == target_venue and row_person == target_person:
                        found_row = i
                        break
            
            if found_row:
                # 更新现有行
                await sheets_manager.update_cell(self.sheet_name, found_row, 3, str(ratio))
                action = "更新"
            else:
                # 添加新行
                new_row = [target_venue, target_person, str(ratio), "通过机器人设置"]
                await sheets_manager.append_row(self.sheet_name, new_row)
                action = "添加"
            
            logger.info(f"Google Sheet rebate 配置{action}成功: {venue}/{person} = {ratio}")
            return True, f"{action}成功"
            
        except Exception as e:
            logger.error(f"更新 Google Sheet rebate 配置失败: {e}")
            return False, f"更新失败: {e}"
    
    def get_ratio(self, config: Dict[str, Any], venue: str, person: str) -> float:
        """从配置中获取 rebate 比例"""
        try:
            # 查找具体的场馆配置
            if venue in config:
                venue_config = config[venue]

                # 如果场馆配置是字典，查找具体人员或默认比例
                if isinstance(venue_config, dict):
                    # 1. 优先查找具体人员配置
                    if person in venue_config:
                        person_ratio = venue_config[person]
                        if isinstance(person_ratio, (int, float)):
                            logger.debug(f"找到具体人员配置: {venue}/{person} = {person_ratio}")
                            return float(person_ratio)

                    # 2. 查找场馆默认比例（"*" 或 "默认比例"）
                    for default_key in ["默认比例", "*"]:
                        if default_key in venue_config:
                            venue_default = venue_config[default_key]
                            if isinstance(venue_default, (int, float)):
                                logger.debug(f"找到场馆默认配置: {venue}/{default_key} = {venue_default}")
                                return float(venue_default)

                # 如果场馆配置直接是数字（旧格式兼容）
                elif isinstance(venue_config, (int, float)):
                    logger.debug(f"找到场馆直接配置: {venue} = {venue_config}")
                    return float(venue_config)

            # 3. 返回全局默认比例
            default_ratio = config.get("默认比例", 0.1)
            logger.debug(f"使用全局默认比例: {default_ratio}")
            return float(default_ratio)

        except (ValueError, TypeError) as e:
            logger.warning(f"获取 rebate 比例失败: {e}")
            return 0.1


class SalaryConfigManager:
    """Salary 配置管理器"""
    
    def __init__(self):
        self.file_manager = get_file_manager()
        self.config_file = Config.SALARY_FILE
        self.sheet_name = Config.SALARY_CONFIG_SHEET_NAME
    
    async def load_local_config(self) -> Dict[str, Any]:
        """异步加载本地 salary 配置"""
        try:
            config = await self.file_manager.read_json_async(self.config_file)
            logger.debug(f"本地 salary 配置加载成功")
            return config
        except Exception as e:
            logger.error(f"加载本地 salary 配置失败: {e}")
            return {}
    
    async def save_local_config(self, config: Dict[str, Any]) -> bool:
        """异步保存本地 salary 配置"""
        try:
            cleaned_config = self._clean_salary_config(config)
            success = await self.file_manager.write_json_async(
                self.config_file, cleaned_config, is_sensitive=True
            )

            if success:
                logger.info("本地 salary 配置保存成功")
            return success
        except Exception as e:
            logger.error(f"保存本地 salary 配置失败: {e}")
            return False

    def _clean_salary_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """清理和验证 salary 配置结构"""
        cleaned_config = {}

        for rebate_key, rebate_value in config.items():
            if not isinstance(rebate_value, dict):
                logger.warning(f"忽略无效的 salary 配置项: {rebate_key} = {rebate_value}")
                continue

            cleaned_rebate = {}
            for game_key, game_value in rebate_value.items():
                if not isinstance(game_value, list):
                    logger.warning(f"忽略无效的游戏配置: {rebate_key}/{game_key} = {game_value}")
                    continue

                cleaned_rules = []
                for rule in game_value:
                    if isinstance(rule, dict) and all(key in rule for key in ["enabled", "profit_min", "profit_max", "salary"]):
                        cleaned_rules.append(rule)
                    else:
                        logger.warning(f"忽略无效的规则: {rule}")

                if cleaned_rules:
                    cleaned_rebate[game_key] = cleaned_rules

            if cleaned_rebate:
                cleaned_config[rebate_key] = cleaned_rebate

        return cleaned_config
    
    async def load_from_google_sheet(self) -> Dict[str, Any]:
        """从 Google Sheet 加载 salary 配置"""
        try:
            sheets_manager = await get_sheets_manager()
            sheet_data = await sheets_manager.read_sheet(self.sheet_name)
            
            if not sheet_data:
                raise ConfigurationError("salary配置表格为空")
            
            # 解析表格数据
            salary_config = {}
            headers = sheet_data[0] if sheet_data else []
            
            # 验证表头
            expected_headers = ["启用", "Rebate", "游戏", "游戏子类型", "盈亏下限", "盈亏上限", "工资", "备注"]
            if not all(header in headers for header in expected_headers[:7]):
                raise ConfigurationError(f"表格格式错误，期望的列: {expected_headers}")
            
            # 获取列索引
            enabled_col = headers.index("启用")
            rebate_col = headers.index("Rebate")
            game_col = headers.index("游戏")
            profit_min_col = headers.index("盈亏下限")
            profit_max_col = headers.index("盈亏上限")
            salary_col = headers.index("工资")
            desc_col = headers.index("备注") if "备注" in headers else -1
            
            for row in sheet_data[1:]:  # 跳过标题行
                if len(row) <= max(enabled_col, rebate_col, game_col, profit_min_col, profit_max_col, salary_col):
                    continue
                
                try:
                    # 解析各字段
                    enabled = row[enabled_col].strip().upper() in ["TRUE", "1", "是", "启用"]
                    rebate = float(row[rebate_col].strip())
                    game = row[game_col].strip()
                    profit_min_str = row[profit_min_col].strip()
                    profit_max_str = row[profit_max_col].strip()
                    salary = int(row[salary_col].strip())
                    description = row[desc_col].strip() if desc_col >= 0 and len(row) > desc_col else ""
                    
                    # 解析盈亏范围
                    profit_min = None if not profit_min_str or profit_min_str.lower() == "null" else int(profit_min_str)
                    profit_max = None if not profit_max_str or profit_max_str.lower() == "null" else int(profit_max_str)
                    
                    # 验证数据
                    if not (0 <= rebate <= 1):
                        logger.warning(f"忽略无效 rebate: {rebate}")
                        continue
                    
                    if not game:
                        logger.warning("忽略空游戏名称的规则")
                        continue
                    
                    # 构建配置结构
                    rebate_key = str(rebate)
                    if rebate_key not in salary_config:
                        salary_config[rebate_key] = {}
                    
                    if game not in salary_config[rebate_key]:
                        salary_config[rebate_key][game] = []
                    
                    # 添加规则
                    rule = {
                        "enabled": enabled,
                        "profit_min": profit_min,
                        "profit_max": profit_max,
                        "salary": salary,
                        "description": description or f"{game} {profit_min_str}~{profit_max_str}"
                    }
                    
                    salary_config[rebate_key][game].append(rule)
                    
                except (ValueError, IndexError) as e:
                    logger.warning(f"忽略无效的 salary 配置行: {row}, 错误: {e}")
                    continue
            
            logger.info(f"从 Google Sheet 加载 salary 配置成功")
            return salary_config
            
        except Exception as e:
            logger.error(f"从 Google Sheet 加载 salary 配置失败: {e}")
            raise ConfigurationError(f"读取 Google Sheet 失败: {e}")
    
    async def update_google_sheet(self, rebate: float, game: str, profit_min: Optional[int] = None,
                                 profit_max: Optional[int] = None, salary: int = None) -> Tuple[bool, str]:
        """更新 Google Sheet 中的 salary 配置"""
        try:
            # 输入验证
            if not (0 <= rebate <= 1):
                return False, "rebate 必须在 0-1 之间"
            
            if game:
                is_valid, error = InputValidator.validate_field('游戏', game)
                if not is_valid:
                    return False, f"游戏名称无效: {error}"
            
            if salary is not None and salary < 0:
                return False, "工资不能为负数"
            
            sheets_manager = await get_sheets_manager()
            sheet_data = await sheets_manager.read_sheet(self.sheet_name)
            
            if not sheet_data:
                return False, "salary配置表格为空"
            
            # 查找要更新的行
            found_row = None
            for i, row in enumerate(sheet_data[1:], start=2):  # 跳过标题行
                if len(row) >= 7:
                    try:
                        row_rebate = float(row[1].strip())
                        row_game = row[2].strip()
                        row_profit_min_str = row[4].strip()
                        row_profit_max_str = row[5].strip()
                        
                        row_profit_min = None if not row_profit_min_str else int(row_profit_min_str)
                        row_profit_max = None if not row_profit_max_str else int(row_profit_max_str)
                        
                        if (row_rebate == rebate and row_game == game and 
                            row_profit_min == profit_min and row_profit_max == profit_max):
                            found_row = i
                            break
                    except (ValueError, IndexError):
                        continue
            
            if found_row:
                # 更新现有行
                await sheets_manager.update_cell(self.sheet_name, found_row, 7, str(salary))
                action = "更新"
            else:
                # 添加新行
                profit_min_str = str(profit_min) if profit_min is not None else ""
                profit_max_str = str(profit_max) if profit_max is not None else ""
                description = f"{game} {profit_min_str}~{profit_max_str} 工资{salary}"
                
                new_row = ["TRUE", str(rebate), game, "", profit_min_str, profit_max_str, str(salary), description]
                await sheets_manager.append_row(self.sheet_name, new_row)
                action = "添加"
            
            logger.info(f"Google Sheet salary 配置{action}成功: {rebate}/{game}")
            return True, f"{action}成功"
            
        except Exception as e:
            logger.error(f"更新 Google Sheet salary 配置失败: {e}")
            return False, f"更新失败: {e}"
    
    def find_salary_rule(self, config: Dict[str, Any], rebate_ratio: float, 
                        game: str, profit: int) -> Optional[int]:
        """从配置中查找工资规则"""
        try:
            rebate_key = str(rebate_ratio)
            if rebate_key not in config:
                return None
            
            if game not in config[rebate_key]:
                return None
            
            rules = config[rebate_key][game]
            
            # 遍历规则，找到匹配的盈利范围
            for rule in rules:
                if not rule.get("enabled", True):
                    continue
                
                profit_min = rule.get("profit_min")
                profit_max = rule.get("profit_max")
                
                # 检查盈利是否在范围内
                if profit_min is not None and profit < profit_min:
                    continue
                if profit_max is not None and profit > profit_max:
                    continue
                
                # 找到匹配的规则
                return rule.get("salary", 0)
            
            return None
            
        except Exception as e:
            logger.error(f"查找工资规则失败: {e}")
            return None


# 全局配置管理器实例
_rebate_manager = None
_salary_manager = None

def get_rebate_manager() -> RebateConfigManager:
    """获取全局 Rebate 配置管理器实例"""
    global _rebate_manager
    
    if _rebate_manager is None:
        _rebate_manager = RebateConfigManager()
    
    return _rebate_manager

def get_salary_manager() -> SalaryConfigManager:
    """获取全局 Salary 配置管理器实例"""
    global _salary_manager
    
    if _salary_manager is None:
        _salary_manager = SalaryConfigManager()
    
    return _salary_manager
