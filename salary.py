# salary.py
import json
import os
from typing import Optional, Dict, List, Any
from charts import ENV
from config import Config
from storage import load_rebate_config, load_rebate_ratio, load_salary_config
from config_cache import get_cached_salary_config, find_cached_salary_rule, get_cached_rebate_ratio

# 内存缓存
_salary_config_cache: Optional[Dict[str, Any]] = None
_salary_config_last_modified: Optional[float] = None

SALARY_FILE = Config.SALARY_FILE
BASE_WAGER = Config.BASE_WAGER  # 基准注码

# 保留原有配置以兼容旧代码
SALARY_AT_WIN_WITH_REBATE_1 = Config.SALARY_AT_WIN_WITH_REBATE_1
SALARY_AT_LOSE_WITH_REBATE_1 = Config.SALARY_AT_LOSE_WITH_REBATE_1
SALARY_AT_WIN_WITH_REBATE_2 = Config.SALARY_AT_WIN_WITH_REBATE_2
SALARY_AT_LOSE_WITH_REBATE_2 = Config.SALARY_AT_LOSE_WITH_REBATE_2
REBATE_RATIO_1 = Config.REBATE_RATIO_1  # 10% 输返
REBATE_RATIO_2 = Config.REBATE_RATIO_2  # 20% 输返


# 自动计算工资
# 填报时间	工作日期	人员	场子	游戏	卡号	起始本金	点码	工资	输返	盈利	备注
# 2025/6/24 22:54	6月21日	敏	Otium	BJ	81247	2000	500	10	300	-1200	大小没散，一个小时内拿到3把一样的牌，剩余21790

###
# 工资策略配置：
# 如果rebate为20%：
# 游戏为BJ: 盈利 200~999；工资20
# 游戏为BJ: 赢 1000~1999；工资30
# 游戏为BJ： 赢 2000~2599；工资40
# 游戏为BJ： 赢 2600以上；工资50

# BJ： 输 -100~-1999；工资10
# BJ： 输 -2000以上：工资20

# UTH: 赢 1500以上；工资 30
# UTH： 赢 200~1499；工资20

# UTH: 输 -1500以上；工资 15
# UTH： 输 -200~-1499；工资10

# 俄罗斯： 赢 200~999；工资30
# 俄罗斯: 赢 1000~1999；工资40
# 俄罗斯： 赢 2000~2599；工资50
# 俄罗斯： 赢 2600以上；工资60

# 俄罗斯：输 -100~-1999；工资15
# 俄罗斯：输 -2000以上：工资30

# 百家乐： 赢 200~999；工资10
# 百家乐: 赢 1000~1999；工资20
# 百家乐： 赢 2000~2999；工资30
# 百家乐： 赢 3000以上；工资40

# 百家乐： 输 -200~-999；工资5
# 百家乐: 输 -1000~-1999；工资10
# 百家乐： 输 -2000以上；工资20

# 如果输返10%：
# BJ: 赢 200~999；工资10
# BJ: 赢 1000以上；工资20
# BJ： 赢 2000以上，工资40

# BJ： 输 -200以上；工资10

# UTH: 赢 600以上；工资 20
# UTH： 赢 200~599；工资 10

# UTH: 输 -600以上；工资 10
# UTH： 输 -10~-599；工资0

# 俄罗斯： 赢 200~999；工资20
# 俄罗斯: 赢 1000~1999；工资30
# 俄罗斯： 赢 2000~2599；工资40
# 俄罗斯： 赢 2600以上；工资50

# 俄罗斯：输 -100~-1999；工资10
# 俄罗斯：输 -2000以上：工资20

# 百家乐： 赢 200~999；工资0
# 百家乐: 赢 1000~1999；工资10
# 百家乐： 赢 2000~2999；工资20
# 百家乐： 赢 3000以上；工资30

# 百家乐： 输 -200~-1999；工资10
# 百家乐： 输 -2000以上；工资20
###

def get_salary_config_cached() -> Dict[str, Any]:
    """获取缓存的salary配置，如果文件有更新则重新加载"""
    global _salary_config_cache, _salary_config_last_modified

    if not os.path.exists(SALARY_FILE):
        return {}

    # 获取文件修改时间
    current_modified = os.path.getmtime(SALARY_FILE)

    # 如果缓存为空或文件已更新，重新加载
    if (_salary_config_cache is None or
        _salary_config_last_modified is None or
        current_modified > _salary_config_last_modified):

        _salary_config_cache = load_salary_config()
        _salary_config_last_modified = current_modified
        print(f"🔄 Salary配置已重新加载到内存缓存")

    return _salary_config_cache


def find_salary_by_game_and_profit(rebate_ratio: float, game: str, profit: int) -> Optional[int]:
    """根据rebate比例、游戏类型和盈利查找对应的工资"""
    try:
        # 优先使用缓存
        salary = find_cached_salary_rule(rebate_ratio, game, profit)
        if salary is not None:
            print(f"💰 从缓存找到工资规则: rebate={rebate_ratio}, game={game}, profit={profit}, 工资={salary}")
            return salary

        # 缓存未命中，使用原有逻辑
        salary_config = get_salary_config_cached()

        # 转换rebate_ratio为字符串键
        rebate_key = str(rebate_ratio)

        if rebate_key not in salary_config:
            print(f"⚠️ 未找到rebate比例 {rebate_ratio} 的配置")
            return None

        if game not in salary_config[rebate_key]:
            print(f"⚠️ 未找到游戏 {game} 在rebate {rebate_ratio} 下的配置")
            return None

        rules = salary_config[rebate_key][game]

        # 遍历规则，找到匹配的盈利范围
        for rule in rules:
            if not rule.get("enabled", True):
                continue

            profit_min = rule.get("profit_min")
            profit_max = rule.get("profit_max")

            # 检查盈利是否在范围内
            if profit_min is not None and profit < profit_min:
                continue
            if profit_max is not None and profit > profit_max:
                continue

            # 找到匹配的规则
            salary = rule.get("salary", 0)
            description = rule.get("description", "")
            print(f"💰 找到匹配规则: {description}, 工资: {salary}")
            return salary

        print(f"⚠️ 未找到匹配的工资规则: rebate={rebate_ratio}, game={game}, profit={profit}")
        return None

    except Exception as e:
        print(f"❌ 查找工资规则时出错: {e}")
        return None


def calculate_salary_new(venue: str, person: str, game: str, profit: int) -> tuple[bool, int]:
    """
    新的工资计算函数，使用salary_config.json中的配置
    :param venue: 场馆名称
    :param person: 人员名称
    :param game: 游戏类型
    :param profit: 盈利金额
    :return: (是否符合条件, 工资金额)
    """
    try:
        # 通过ConfigCache获取rebate比例（自动三层策略）
        from config_cache import config_cache
        rebate_ratio = config_cache.get_rebate_ratio(venue, person)

        if ENV == "dev":
            print(f"🎯 计算工资: venue={venue}, person={person}, game={game}, profit={profit}, rebate={rebate_ratio}")

        # 根据游戏类型和盈利查找工资
        salary = find_salary_by_game_and_profit(rebate_ratio, game, profit)

        if salary is not None and salary > 0:
            return True, salary
        else:
            return False, 0

    except Exception as e:
        print(f"❌ 计算工资时出错: {e}")
        return False, 0


def calculate_salary(venue: str, person: str, rebate_amount: int, profit: int, game: Optional[str] = None) -> tuple[bool, int]:
    """
    计算工资（保持向后兼容性，同时支持新的配置系统）
    :param venue: 场馆名称
    :param person: 人员名称
    :param rebate_amount: 输返金额
    :param profit: 盈利金额
    :param game: 游戏类型（可选，如果提供则使用新的配置系统）
    :return: (是否符合条件, 工资金额)
    """
    # 如果提供了游戏类型，使用新的配置系统
    if game is not None:
        return calculate_salary_new(venue, person, game, profit)

    # 否则使用原有的逻辑（保持向后兼容性）
    is_qualified = False

    # 读取输返比例
    rebate_ratio = load_rebate_ratio(venue, person)
    if rebate_ratio is None:
        rebate_config = load_rebate_config()
        rebate_ratio = rebate_config.get("默认比例", 0.1)

    # 使用原有的计算逻辑
    if rebate_amount >= BASE_WAGER:
        if rebate_amount >= BASE_WAGER * 3 and rebate_ratio == REBATE_RATIO_2:
            is_qualified = True
            return is_qualified, int(SALARY_AT_LOSE_WITH_REBATE_2)
        elif rebate_amount >= BASE_WAGER and rebate_ratio == REBATE_RATIO_1:
            is_qualified = True
            return is_qualified, int(SALARY_AT_LOSE_WITH_REBATE_1)

    elif rebate_amount == 0:
        if rebate_ratio == REBATE_RATIO_1 and profit >= BASE_WAGER * 1:
            is_qualified = True
            return is_qualified, int(SALARY_AT_WIN_WITH_REBATE_1)
        if rebate_ratio == REBATE_RATIO_2 and profit >= BASE_WAGER * 2:
            is_qualified = True
            return is_qualified, int(SALARY_AT_WIN_WITH_REBATE_2)

    return is_qualified, 0


def clear_salary_config_cache():
    """清除salary配置缓存，强制下次重新加载"""
    global _salary_config_cache, _salary_config_last_modified
    _salary_config_cache = None
    _salary_config_last_modified = None
    print("🗑️ Salary配置缓存已清除")


def get_salary_config_info() -> Dict[str, Any]:
    """获取salary配置的统计信息"""
    try:
        config = get_salary_config_cached()

        info = {
            "rebate_types": list(config.keys()),
            "total_rebate_types": len(config),
            "games_by_rebate": {},
            "total_rules": 0
        }

        for rebate, games in config.items():
            if isinstance(games, dict):
                game_list = list(games.keys())
                info["games_by_rebate"][rebate] = game_list

                # 统计规则数量
                for game_rules in games.values():
                    if isinstance(game_rules, list):
                        info["total_rules"] += len(game_rules)

        return info

    except Exception as e:
        print(f"❌ 获取salary配置信息时出错: {e}")
        return {}
