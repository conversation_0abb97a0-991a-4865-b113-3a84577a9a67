@echo off
setlocal

:: 设置路径
set REPO_DIR=C:\py
set VENV_PATH=%REPO_DIR%\myenv
set PYTHON=%VENV_PATH%\Scripts\python.exe
set GIT_EXE=C:\Git\bin\git.exe

echo.
echo [1/5] activate virtual ENV and restore code...
cd /d %REPO_DIR%
"%GIT_EXE%" pull origin master

echo.
echo [2/5] dealing requirements...
"%PYTHON%" -m pip install --upgrade pip
"%PYTHON%" -m pip install -r requirements.txt

echo.
echo [3/5] restart service...
nssm stop telebot
nssm start telebot

echo.
echo deploy complete.. enjoy...
pause
endlocal