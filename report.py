# report.py 示例内容
import os
from datetime import datetime, timedelta

from config import Config
import salary
from storage import load_excel_data
from utils import get_report_period, send_error

TIME_ZONE = Config.TIME_ZONE  # 使用 zoneinfo 模块处理时区
EXCEL_FILE = Config.EXCEL_FILE
GROUP_CHAT_ID = Config.GROUP_CHAT_ID
ENV = Config.ENV


# 公共报告生成函数
def generate_report(start_time, end_time):
    # 使用 load_excel_data 来加载时间范围内的记录
    records = load_excel_data(start_time=start_time, end_time=end_time, full_load=False)
    if ENV == "dev":
        print(f"records: {records}")
    summary = {}
    total_profit = 0

    for row in records:
        name = row["person"]
        profit = row["profit"]
        salary = row['salary']
        is_win = profit > 0

        if name not in summary:
            summary[name] = {"profit": 0, "salary":0, "count": 0, "win": 0}

        summary[name]["profit"] += profit
        summary[name]["salary"] += salary
        summary[name]["count"] += 1
        if is_win:
            summary[name]["win"] += 1

        total_profit += profit

    return summary, total_profit


async def generate_and_send_report(context, start_time=None, end_time=None, period="monthly"):
    now = datetime.now(TIME_ZONE)
    if period not in ["daily", "weekly", "monthly", "specific"]:
        await send_error(context.bot, f"错误：无效的报告周期: {period}")
        return
    title = f"{period} 报表"
    if not start_time or not end_time:
        if ENV == "dev":
            print(f"没有指定报告起始日期，使用默认日期")
        start_time, end_time, title = get_report_period(period=period)
    if ENV == "dev":
        try:
            print(f"[DEV][report] period={period} window_start={start_time} window_end={end_time} excel_exists={os.path.exists(EXCEL_FILE)}")
        except Exception:
            pass
    if not start_time:
        await send_error(context.bot, f"错误：未指定报告起始日期: {period}")
        return
    if not os.path.exists(EXCEL_FILE):
        await send_error(context.bot, f"无法生成{title}，未找到记录文件。")
        return
    if ENV == "dev":
        print(f"生成{title}，起始日期: {start_time}, 结束日期: {end_time}")
    summary, total_profit = generate_report(start_time, end_time)
    if ENV == "dev":
        try:
            print(f"[DEV][report] summary_keys={list(summary.keys())} total_profit={total_profit:+.0f}")
        except Exception:
            pass
    if ENV == "dev":
        print(f"summary: {summary}")
    if not summary:
        friendly = {"daily": "昨日", "weekly": "本周", "monthly": "本月"}.get(period, period)
        await context.bot.send_message(chat_id=GROUP_CHAT_ID, text=f"📄 {friendly}无有效记录，未生成{title}。")
        return

    text = format_summary(summary, total_profit, title, start_time, end_time)
    await context.bot.send_message(chat_id=GROUP_CHAT_ID, text=text, parse_mode="HTML")


def format_summary(summary, total_profit, title="报表", start_time=None, end_time=None):
    lines = [f"📊 <b>{title}</b>"]
    if start_time and end_time:
        lines[0] += f"（{start_time.strftime('%Y-%m-%d')} ～ {(end_time - timedelta(days=1)).strftime('%Y-%m-%d')}，UTC+4）"
    lines.append("\n<b>名次｜人员｜盈亏｜次数｜工资｜胜率</b>")

    sorted_summary = sorted(summary.items(), key=lambda x: -x[1]["profit"])
    medals = ["🥇", "🥈", "🥉"]

    for idx, (name, info) in enumerate(sorted_summary):
        medal = medals[idx] if idx < len(medals) else "   "
        profit = info["profit"]
        count = info["count"]
        salary = info["salary"]
        win = info["win"]
        win_rate = f"{(win / count * 100):.1f}%" if count else "0.0%"
        sign = "🟢" if profit > 0 else ("🔴" if profit < 0 else "⚪")
        lines.append(f"{medal} {name}｜{sign} {profit:+.0f}｜{count}次｜{salary}| {win_rate}")

    lines.append(f"\n📦 <b>团队总盈亏</b>: {total_profit:+.0f}")
    return "\n".join(lines)
