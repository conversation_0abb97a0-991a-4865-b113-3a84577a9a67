@echo off
REM Telegram Bot 统一部署脚本 (Windows 版本)
REM 支持 Docker 部署和传统部署两种方式

setlocal enabledelayedexpansion

REM 全局变量
set "DEPLOYMENT_METHOD="
set "PYTHON_CMD="
set "VENV_PATH=venv"

REM 颜色定义 (Windows 10+ 支持 ANSI 颜色)
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "RED=%ESC%[31m"
set "GREEN=%ESC%[32m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "PURPLE=%ESC%[35m"
set "CYAN=%ESC%[36m"
set "NC=%ESC%[0m"

REM 选择部署方法
:select_deployment_method
echo.
echo ? Telegram Bot 统一部署脚本 (Windows)
echo ==========================================
echo.
echo 请选择部署方式：
echo 1) ? Docker 部署 (推荐生产环境)
echo    - 容器化部署，易于管理
echo    - 包含监控和健康检查
echo    - 需要 Docker 和 Docker Compose
echo.
echo 2) ? 传统部署 (Python 虚拟环境)
echo    - 直接在系统上运行
echo    - 更灵活的配置选项
echo    - 需要 Python 3.9+
echo.

:deployment_choice_loop
set /p choice="请输入选择 (1 或 2): "
if "%choice%"=="1" (
    set "DEPLOYMENT_METHOD=docker"
    echo %CYAN%[HIGHLIGHT]%NC% 选择了 Docker 部署方式
    goto :deployment_selected
) else if "%choice%"=="2" (
    set "DEPLOYMENT_METHOD=traditional"
    echo %CYAN%[HIGHLIGHT]%NC% 选择了传统部署方式
    goto :deployment_selected
) else (
    echo %YELLOW%[WARNING]%NC% 无效选择，请输入 1 或 2
    goto :deployment_choice_loop
)

:deployment_selected

REM Docker 部署前置检查
:check_docker_requirements
if not "%DEPLOYMENT_METHOD%"=="docker" goto :check_traditional_requirements

echo %PURPLE%[STEP]%NC% 检查 Docker 环境...

REM 检查 Docker 是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker 未安装，请先安装 Docker Desktop
    echo 安装指南: https://docs.docker.com/get-docker/
    pause
    exit /b 1
)

REM 检查 Docker Compose 是否安装
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker Compose 未安装，请先安装 Docker Compose
    echo 安装指南: https://docs.docker.com/compose/install/
    pause
    exit /b 1
)

REM 检查 Docker 是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker 服务未运行，请启动 Docker Desktop
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Docker 环境检查完成
goto :check_common_files

REM 传统部署前置检查
:check_traditional_requirements
if not "%DEPLOYMENT_METHOD%"=="traditional" goto :check_common_files

echo %PURPLE%[STEP]%NC% 检查 Python 环境...

REM 查找合适的 Python 命令
call :find_python
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 需要 Python 3.9 或更高版本
    echo 请安装 Python 3.9+ 后重试
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Python 环境检查完成
goto :check_common_files

REM 查找合适的 Python 命令
:find_python
set "python_commands=python python3 py python3.9 python3.10 python3.11 python3.12"
for %%p in (%python_commands%) do (
    %%p --version >nul 2>&1
    if not errorlevel 1 (
        for /f "tokens=2" %%v in ('%%p --version 2^>^&1') do (
            call :check_python_version "%%v" "%%p"
            if not errorlevel 1 (
                set "PYTHON_CMD=%%p"
                echo %GREEN%[SUCCESS]%NC% 找到合适的 Python: %%p
                exit /b 0
            )
        )
    )
)
exit /b 1

REM 检查 Python 版本
:check_python_version
set "version=%~1"
set "cmd=%~2"
for /f "tokens=1,2 delims=." %%a in ("%version%") do (
    set "major=%%a"
    set "minor=%%b"
)
if %major% geq 3 if %minor% geq 9 (
    exit /b 0
)
exit /b 1

REM 通用文件检查
:check_common_files
echo %PURPLE%[STEP]%NC% 检查项目文件...

REM 检查配置文件
if not exist ".env" (
    if exist ".env.template" (
        echo %YELLOW%[WARNING]%NC% .env 文件不存在，从模板创建...
        copy ".env.template" ".env" >nul
        echo %YELLOW%[WARNING]%NC% 请编辑 .env 文件，填入你的配置信息
        echo 主要需要配置的项目：
        echo   - BOT_TOKEN: 你的 Telegram Bot Token
        echo   - CHAT_ID: 你的群组 Chat ID
        echo   - GOOGLE_SHEET_NAME: 你的 Google Sheets 名称
        pause
    ) else (
        echo %RED%[ERROR]%NC% .env.template 文件不存在
        pause
        exit /b 1
    )
)

REM 检查 Google Sheets 凭据
if not exist "mysheetapp.json" (
    echo %RED%[ERROR]%NC% Google Sheets 凭据文件 mysheetapp.json 不存在
    echo %RED%[ERROR]%NC% 请将你的 Google Sheets API 凭据文件重命名为 mysheetapp.json 并放到项目根目录
    pause
    exit /b 1
)

REM 检查特定部署方式的文件
if "%DEPLOYMENT_METHOD%"=="docker" (
    if not exist "docker\docker-compose.yml" (
        echo %RED%[ERROR]%NC% Docker Compose 配置文件不存在: docker\docker-compose.yml
        pause
        exit /b 1
    )
    echo %GREEN%[SUCCESS]%NC% Docker 配置文件检查完成
)

echo %GREEN%[SUCCESS]%NC% 项目文件检查完成

REM 创建必要目录
:create_directories
echo %PURPLE%[STEP]%NC% 创建数据目录...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
echo %GREEN%[SUCCESS]%NC% 数据目录创建完成

REM 根据部署方法执行相应流程
if "%DEPLOYMENT_METHOD%"=="docker" goto :deploy_docker
if "%DEPLOYMENT_METHOD%"=="traditional" goto :deploy_traditional
echo %RED%[ERROR]%NC% 未知的部署方法: %DEPLOYMENT_METHOD%
pause
exit /b 1

REM 传统部署流程
:deploy_traditional
echo %CYAN%[HIGHLIGHT]%NC% 开始传统部署流程...

REM 创建虚拟环境
echo %PURPLE%[STEP]%NC% 创建 Python 虚拟环境...
if exist "%VENV_PATH%" (
    echo %BLUE%[INFO]%NC% 虚拟环境已存在，跳过创建
) else (
    %PYTHON_CMD% -m venv "%VENV_PATH%"
    echo %GREEN%[SUCCESS]%NC% 虚拟环境创建完成
)

REM 激活虚拟环境并安装依赖
echo %PURPLE%[STEP]%NC% 安装 Python 依赖...
call "%VENV_PATH%\Scripts\activate.bat"

REM 升级 pip
pip install --upgrade pip
echo %BLUE%[INFO]%NC% pip 已升级到最新版本

REM 安装依赖
if exist "requirements.txt" (
    pip install -r requirements.txt
    echo %GREEN%[SUCCESS]%NC% 依赖安装完成
) else (
    echo %RED%[ERROR]%NC% requirements.txt 文件不存在
    pause
    exit /b 1
)

REM 询问是否运行测试
echo.
set /p run_test="是否运行测试？(y/N): "
if /i "%run_test%"=="y" (
    echo %PURPLE%[STEP]%NC% 运行测试...
    if exist "run_tests.py" (
        python run_tests.py --fast
        echo %GREEN%[SUCCESS]%NC% 测试通过
    ) else (
        echo %YELLOW%[WARNING]%NC% 测试脚本不存在，跳过测试
    )
)

echo %GREEN%[SUCCESS]%NC% 传统部署完成！

echo.
echo ? 部署完成！
echo ===============
echo.
echo ? 启动应用:
echo   %VENV_PATH%\Scripts\activate.bat
echo   python Group_record.py
echo.
echo ? 或者使用增强版本（包含监控）:
echo   %VENV_PATH%\Scripts\activate.bat
echo   python main_with_monitoring.py
echo.
echo ? 监控端点 (如果使用增强版本):
echo   ? 健康检查: http://localhost:8080/health
echo   ? 应用状态: http://localhost:8080/status
echo   ? 指标数据: http://localhost:8080/metrics

REM 询问是否立即启动
echo.
set /p start_app="是否立即启动应用？(y/N): "
if /i "%start_app%"=="y" (
    echo.
    echo 选择启动方式：
    echo 1) 基础版本 (Group_record.py)
    echo 2) 增强版本 (main_with_monitoring.py - 包含监控)
    set /p app_choice="请选择 (1 或 2): "

    if "!app_choice!"=="1" (
        echo %BLUE%[INFO]%NC% 启动基础版本...
        python Group_record.py
    ) else if "!app_choice!"=="2" (
        echo %BLUE%[INFO]%NC% 启动增强版本...
        python main_with_monitoring.py
    ) else (
        echo %BLUE%[INFO]%NC% 启动基础版本...
        python Group_record.py
    )
)
goto :end

REM Docker 部署流程
:deploy_docker
echo %CYAN%[HIGHLIGHT]%NC% 开始 Docker 部署流程...

REM 询问部署模式
echo.
echo 请选择 Docker 部署模式：
echo 1) 基础部署 (仅 Telegram Bot)
echo 2) 完整部署 (包含监控: Prometheus + Grafana)
set /p deploy_mode="请输入选择 (1 或 2): "

REM 进入 Docker 目录
cd docker

REM 根据选择部署
if "%deploy_mode%"=="1" (
    echo %BLUE%[INFO]%NC% 开始基础部署...
    docker-compose up -d telegram-bot
) else if "%deploy_mode%"=="2" (
    echo %BLUE%[INFO]%NC% 开始完整部署（包含监控）...
    docker-compose --profile monitoring up -d
) else (
    echo %YELLOW%[WARNING]%NC% 无效选择，使用基础部署...
    docker-compose up -d telegram-bot
)

REM 等待服务启动
echo %BLUE%[INFO]%NC% 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo %BLUE%[INFO]%NC% 检查服务状态...
docker-compose ps

REM 检查健康状态
echo %BLUE%[INFO]%NC% 检查应用健康状态...
set health_check_passed=0
for /l %%i in (1,1,15) do (
    curl -f http://localhost:8080/health >nul 2>&1
    if not errorlevel 1 (
        echo %GREEN%[SUCCESS]%NC% 应用健康检查通过
        set health_check_passed=1
        goto :docker_health_check_done
    )
    echo 检查中...
    timeout /t 2 /nobreak >nul
)

:docker_health_check_done
if !health_check_passed!==0 (
    echo %YELLOW%[WARNING]%NC% 健康检查超时，请手动检查服务状态
)

REM 显示部署结果
echo.
echo ? Docker 部署完成！
echo ====================

echo ? 服务状态:
docker-compose ps

echo.
echo ? 可用的服务端点:
echo   ? 健康检查: http://localhost:8080/health
echo   ? 详细健康检查: http://localhost:8080/health/detailed
echo   ? 应用状态: http://localhost:8080/status
echo   ? 指标数据: http://localhost:8080/metrics

if "%deploy_mode%"=="2" (
    echo   ? Grafana 监控面板: http://localhost:3000 (admin/admin123)
    echo   ? Prometheus: http://localhost:9090
)

echo.
echo ? 常用命令:
echo   ? 查看日志: docker-compose logs -f telegram-bot
echo   ? 重启服务: docker-compose restart telegram-bot
echo   ? 停止服务: docker-compose stop
echo   ? 进入容器: docker-compose exec telegram-bot bash

echo.
echo ? 更多信息请查看: DOCKER_DEPLOYMENT_GUIDE.md

REM 询问是否查看日志
echo.
set /p show_logs="是否查看实时日志？(y/N): "
if /i "%show_logs%"=="y" (
    echo %BLUE%[INFO]%NC% 显示实时日志 (Ctrl+C 退出)...
    docker-compose logs -f telegram-bot
)

:end
echo.
echo 部署完成！按任意键退出...
pause >nul
