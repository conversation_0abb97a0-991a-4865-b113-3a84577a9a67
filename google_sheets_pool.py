#!/usr/bin/env python3
"""
Google Sheets 连接池模块
提供连接池管理、自动重连、错误处理等功能
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from retrying import retry
import threading

from config import Config

logger = logging.getLogger(__name__)

class GoogleSheetsConnectionPool:
    """Google Sheets 连接池管理器"""
    
    def __init__(self, max_connections: int = 5, connection_timeout: int = 300):
        """
        初始化连接池

        Args:
            max_connections: 最大连接数
            connection_timeout: 连接超时时间（秒）
        """
        # 确保参数类型正确
        self.max_connections = int(max_connections)
        self.connection_timeout = int(connection_timeout)
        self.credentials_file = Config.CREDENTIALS_FILE
        
        # 连接池
        self._pool = asyncio.Queue(maxsize=max_connections)
        self._active_connections = 0
        self._connection_stats = {
            'created': 0,
            'reused': 0,
            'expired': 0,
            'errors': 0
        }
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 初始化标志
        self._initialized = False
    
    async def initialize(self) -> bool:
        """初始化连接池"""
        try:
            if self._initialized:
                return True

            logger.info("初始化 Google Sheets 连接池...")

            # 尝试预创建连接，但不要求全部成功
            initial_connections = min(2, self.max_connections)
            successful_connections = 0

            for i in range(initial_connections):
                try:
                    connection = await self._create_connection()
                    if connection:
                        await self._pool.put(connection)
                        self._active_connections += 1
                        successful_connections += 1
                        logger.debug(f"成功创建连接 {i+1}/{initial_connections}")
                except Exception as e:
                    logger.warning(f"预创建连接 {i+1} 失败: {e}")
                    # 继续尝试其他连接，不要因为一个连接失败就整体失败
                    continue

            # 即使没有预创建成功任何连接，也标记为已初始化
            # 连接将在需要时懒加载创建
            self._initialized = True

            if successful_connections > 0:
                logger.info(f"Google Sheets 连接池初始化完成，成功预创建 {successful_connections}/{initial_connections} 个连接")
            else:
                logger.warning("Google Sheets 连接池初始化完成，但未能预创建任何连接（将在需要时创建）")

            return True

        except Exception as e:
            logger.error(f"Google Sheets 连接池初始化失败: {e}")
            # 即使初始化过程中出现异常，也尝试标记为已初始化，允许懒加载
            self._initialized = True
            logger.warning("连接池标记为已初始化，将尝试懒加载连接")
            return True  # 返回 True 以允许服务继续运行
    
    async def _create_connection(self) -> Optional[Dict[str, Any]]:
        """创建新的 Google Sheets 连接"""
        try:
            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]
            
            creds = ServiceAccountCredentials.from_json_keyfile_name(
                self.credentials_file, scope
            )
            client = gspread.authorize(creds)
            
            connection = {
                'client': client,
                'created_at': datetime.now(),
                'last_used': datetime.now(),
                'use_count': 0
            }
            
            with self._lock:
                self._connection_stats['created'] += 1
            
            logger.debug("创建新的 Google Sheets 连接")
            return connection
            
        except Exception as e:
            with self._lock:
                self._connection_stats['errors'] += 1
            logger.error(f"创建 Google Sheets 连接失败: {e}")
            return None
    
    async def get_connection(self) -> Optional[gspread.Client]:
        """从连接池获取连接"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # 尝试从池中获取连接
            try:
                connection_info = await asyncio.wait_for(
                    self._pool.get(), timeout=5.0
                )
                
                # 检查连接是否过期
                if self._is_connection_expired(connection_info):
                    logger.debug("连接已过期，创建新连接")
                    with self._lock:
                        self._connection_stats['expired'] += 1
                        self._active_connections -= 1
                    
                    # 创建新连接
                    new_connection = await self._create_connection()
                    if new_connection:
                        self._active_connections += 1
                        return self._prepare_connection(new_connection)
                    else:
                        return None
                
                # 连接有效，更新使用信息
                connection_info['last_used'] = datetime.now()
                connection_info['use_count'] += 1
                
                with self._lock:
                    self._connection_stats['reused'] += 1
                
                return connection_info['client']
                
            except asyncio.TimeoutError:
                # 池中没有可用连接，创建新连接
                if self._active_connections < self.max_connections:
                    logger.debug("池中无可用连接，创建新连接")
                    connection = await self._create_connection()
                    if connection:
                        with self._lock:
                            self._active_connections += 1
                        return self._prepare_connection(connection)
                
                logger.warning("连接池已满，无法获取连接")
                return None
                
        except Exception as e:
            logger.error(f"获取 Google Sheets 连接失败: {e}")
            return None
    
    async def return_connection(self, client: gspread.Client):
        """将连接返回到池中"""
        if client is None:
            return
            
        try:
            # 检查连接是否还有效
            if not self._is_connection_valid(client):
                logger.debug("连接已失效，不返回池中")
                with self._lock:
                    self._active_connections -= 1
                return
            
            # 查找对应的连接信息（这里需要改进，应该保持连接的元数据）
            connection_info = {
                'client': client,
                'created_at': datetime.now(),  # 实际应该保持原创建时间
                'last_used': datetime.now(),
                'use_count': 0
            }
            
            # 如果池未满，将连接放回池中
            if self._pool.qsize() < self.max_connections:
                await self._pool.put(connection_info)
                logger.debug("连接已返回池中")
            else:
                # 池已满，安全关闭连接
                await self._close_connection(client)
                with self._lock:
                    self._active_connections -= 1
                logger.debug("连接池已满，连接已关闭")
                
        except Exception as e:
            logger.error(f"返回连接到池失败: {e}")
            # 确保连接计数正确
            with self._lock:
                self._active_connections = max(0, self._active_connections - 1)
    
    def _is_connection_valid(self, client: gspread.Client) -> bool:
        """检查连接是否有效"""
        if client is None:
            return False
        
        try:
            # 简单的健康检查 - 尝试获取用户信息
            # 这是一个轻量级的API调用来验证连接
            return True  # 暂时总是返回True，可以添加实际的健康检查
        except Exception as e:
            logger.debug(f"连接健康检查失败: {e}")
            return False
    
    async def _close_connection(self, client: gspread.Client):
        """安全关闭连接"""
        try:
            if client:
                # gspread客户端没有显式的关闭方法，但我们可以清理引用
                client = None
                logger.debug("连接已清理")
        except Exception as e:
            logger.error(f"关闭连接失败: {e}")
    
    def _is_connection_expired(self, connection_info: Dict[str, Any]) -> bool:
        """检查连接是否过期"""
        now = datetime.now()
        created_at = connection_info['created_at']
        last_used = connection_info['last_used']
        
        # 连接超时检查
        if (now - created_at).total_seconds() > self.connection_timeout:
            return True
        
        # 长时间未使用检查
        if (now - last_used).total_seconds() > 60:  # 1分钟未使用
            return True
        
        return False
    
    def _prepare_connection(self, connection_info: Dict[str, Any]) -> gspread.Client:
        """准备连接供使用"""
        connection_info['last_used'] = datetime.now()
        connection_info['use_count'] += 1
        return connection_info['client']
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            return {
                'active_connections': self._active_connections,
                'pool_size': self._pool.qsize(),
                'max_connections': self.max_connections,
                'stats': self._connection_stats.copy(),
                'initialized': self._initialized
            }
    
    async def close(self):
        """关闭连接池"""
        logger.info("关闭 Google Sheets 连接池...")
        
        with self._lock:
            if not self._initialized:
                logger.info("连接池已关闭")
                return
        
        # 标记为正在关闭，防止新连接创建
        with self._lock:
            self._initialized = False
        
        # 清空连接池并正确关闭每个连接
        closed_connections = 0
        while True:
            try:
                # 使用超时避免无限等待
                connection_info = await asyncio.wait_for(
                    self._pool.get(), timeout=1.0
                )
                
                # 安全关闭连接
                if connection_info and 'client' in connection_info:
                    await self._close_connection(connection_info['client'])
                
                closed_connections += 1
                
            except asyncio.TimeoutError:
                # 队列为空或超时，退出循环
                break
            except asyncio.QueueEmpty:
                # 队列为空，退出循环
                break
            except Exception as e:
                logger.error(f"关闭连接时出错: {e}")
                break
        
        # 重置连接计数
        with self._lock:
            original_count = self._active_connections
            self._active_connections = 0
            self._connection_stats['closed'] = closed_connections
        
        logger.info(f"Google Sheets 连接池已关闭，清理了 {closed_connections} 个连接（原计数: {original_count}）")


class GoogleSheetsManager:
    """Google Sheets 管理器，提供高级操作接口"""
    
    def __init__(self, connection_pool: GoogleSheetsConnectionPool):
        self.pool = connection_pool
    
    @retry(stop_max_attempt_number=3, wait_fixed=2000)
    async def execute_operation(self, operation_func, *args, **kwargs):
        """
        执行 Google Sheets 操作，自动处理连接管理和重试
        
        Args:
            operation_func: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作结果
        """
        # 使用连接上下文管理器确保连接正确释放
        async with get_connection_context(self.pool) as client:
            try:
                # 检查操作函数是否是协程函数
                import asyncio
                if asyncio.iscoroutinefunction(operation_func):
                    # 异步操作函数
                    result = await operation_func(client, *args, **kwargs)
                else:
                    # 同步操作函数
                    result = operation_func(client, *args, **kwargs)

                # 检查结果是否是Response对象
                if hasattr(result, 'status_code'):
                    logger.debug(f"收到Response对象: {result}, status_code: {result.status_code}")
                    if result.status_code == 200:
                        logger.debug("Google Sheets操作成功完成（HTTP 200）")
                        return True  # 操作成功，返回True
                    else:
                        logger.error(f"Google Sheets操作失败，状态码: {result.status_code}")
                        raise Exception(f"Google Sheets操作失败，状态码: {result.status_code}")

                return result

            except Exception as e:
                # 检查是否是Response对象被误当作异常
                if hasattr(e, '__str__') and "Response [200]" in str(e):
                    logger.debug("Google Sheets操作成功（Response [200]）")
                    return True  # 操作成功
                else:
                    logger.error(f"Google Sheets 操作失败: {e}")
                    raise
    
    async def read_sheet(self, sheet_name: str, worksheet_index: int = 0) -> list:
        """读取工作表数据"""
        def _read_operation(client, sheet_name, worksheet_index):
            sheet = client.open(sheet_name)
            if worksheet_index == 0:
                worksheet = sheet.sheet1
            else:
                worksheet = sheet.get_worksheet(worksheet_index)
            return worksheet.get_all_values()

        return await self.execute_operation(_read_operation, sheet_name, worksheet_index)
    
    async def append_row(self, sheet_name: str, row_data: list, worksheet_index: int = 0):
        """向工作表添加行"""
        def _append_operation(client, sheet_name, row_data, worksheet_index):
            sheet = client.open(sheet_name)
            if worksheet_index == 0:
                worksheet = sheet.sheet1
            else:
                worksheet = sheet.get_worksheet(worksheet_index)
            worksheet.append_row(row_data)

        await self.execute_operation(_append_operation, sheet_name, row_data, worksheet_index)
    
    async def update_cell(self, sheet_name: str, row: int, col: int, value: str, worksheet_index: int = 0):
        """更新单元格"""
        def _update_operation(client, sheet_name, row, col, value, worksheet_index):
            sheet = client.open(sheet_name)
            if worksheet_index == 0:
                worksheet = sheet.sheet1
            else:
                worksheet = sheet.get_worksheet(worksheet_index)
            worksheet.update_cell(row, col, value)

        await self.execute_operation(_update_operation, sheet_name, row, col, value, worksheet_index)


# 全局连接池实例和线程安全锁
_connection_pool = None
_sheets_manager = None
_global_lock = threading.RLock()  # 使用可重入锁
_cleanup_lock = threading.Lock()  # 清理操作专用锁

class ConnectionContextManager:
    """连接上下文管理器，确保连接正确释放"""
    
    def __init__(self, pool: GoogleSheetsConnectionPool):
        self.pool = pool
        self.connection = None
    
    async def __aenter__(self):
        self.connection = await self.pool.get_connection()
        if self.connection is None:
            raise ConnectionError("无法获取Google Sheets连接")
        return self.connection
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.connection:
            await self.pool.return_connection(self.connection)
            self.connection = None

async def get_sheets_manager() -> GoogleSheetsManager:
    """获取全局 Google Sheets 管理器实例（线程安全）"""
    global _connection_pool, _sheets_manager, _global_lock
    
    # 双重检查锁定模式，避免不必要的锁竞争
    if _sheets_manager is not None:
        return _sheets_manager
    
    # 使用 asyncio 的线程安全锁
    async with asyncio.Lock():
        # 再次检查，防止多个协程同时进入
        if _sheets_manager is not None:
            return _sheets_manager
        
        try:
            if _connection_pool is None:
                logger.info("创建新的Google Sheets连接池")
                _connection_pool = GoogleSheetsConnectionPool()
                await _connection_pool.initialize()
            
            if _sheets_manager is None:
                logger.info("创建新的Google Sheets管理器")
                _sheets_manager = GoogleSheetsManager(_connection_pool)
            
            return _sheets_manager
            
        except Exception as e:
            logger.error(f"创建Google Sheets管理器失败: {e}")
            # 清理失败的资源
            if _connection_pool:
                try:
                    await _connection_pool.close()
                except:
                    pass
                _connection_pool = None
            _sheets_manager = None
            raise

async def close_sheets_manager():
    """关闭全局 Google Sheets 管理器（线程安全）"""
    global _connection_pool, _sheets_manager, _cleanup_lock
    
    async with asyncio.Lock():
        logger.info("开始关闭Google Sheets管理器")
        
        # 首先设置为None，防止新的请求
        manager = _sheets_manager
        pool = _connection_pool
        _sheets_manager = None
        _connection_pool = None
        
        # 然后安全关闭资源
        if pool:
            try:
                await pool.close()
                logger.info("Google Sheets连接池已关闭")
            except Exception as e:
                logger.error(f"关闭连接池失败: {e}")
        
        logger.info("Google Sheets管理器已关闭")

def get_connection_context(pool: GoogleSheetsConnectionPool) -> ConnectionContextManager:
    """获取连接上下文管理器"""
    return ConnectionContextManager(pool)
