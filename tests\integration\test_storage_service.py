#!/usr/bin/env python3
"""
Integration tests for storage service
"""

import pytest
import asyncio
import os
import json
from unittest.mock import patch, AsyncMock, Mock
from datetime import datetime

from storage_service import StorageService, get_storage_service
from data_models import GameRecord, OperationResult, RecordStatus

@pytest.mark.integration
class TestStorageServiceIntegration:
    """Integration tests for StorageService"""
    
    @pytest.mark.asyncio
    async def test_storage_service_initialization(self, mock_config, temp_dir):
        """Test storage service initialization"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager, \
             patch('storage_service.get_sheets_manager') as mock_get_sheets_manager:
            
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            mock_sheets_manager = AsyncMock()
            mock_get_sheets_manager.return_value = mock_sheets_manager
            
            service = StorageService()
            success = await service.initialize()
            
            assert success
            assert service._initialized
            mock_file_manager.create_excel_async.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_write_record_success(self, mock_config, sample_game_record):
        """Test successful record writing"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager, \
             patch('storage_service.get_sheets_manager') as mock_get_sheets_manager, \
             patch('storage_service.get_excel_processor') as mock_get_excel_processor:
            
            # Setup mocks
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            mock_sheets_manager = AsyncMock()
            mock_sheets_manager.append_row = AsyncMock()
            mock_get_sheets_manager.return_value = mock_sheets_manager
            
            mock_excel_processor = AsyncMock()
            mock_excel_processor.write_record_async.return_value = True
            mock_get_excel_processor.return_value = mock_excel_processor
            
            # Test
            service = StorageService()
            await service.initialize()
            
            result = await service.write_record(sample_game_record, "2025-01-01 12:00:00")
            
            assert result.success
            assert "记录写入成功" in result.message
            assert result.data is not None
            
            # Verify calls
            mock_excel_processor.write_record_async.assert_called_once()
            mock_sheets_manager.append_row.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_write_record_excel_only(self, mock_config, sample_game_record):
        """Test record writing when Google Sheets fails"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager, \
             patch('storage_service.get_sheets_manager') as mock_get_sheets_manager, \
             patch('storage_service.get_excel_processor') as mock_get_excel_processor:
            
            # Setup mocks - Excel succeeds, Sheets fails
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            mock_sheets_manager = AsyncMock()
            mock_sheets_manager.append_row.side_effect = Exception("Sheets error")
            mock_get_sheets_manager.return_value = mock_sheets_manager
            
            mock_excel_processor = AsyncMock()
            mock_excel_processor.write_record_async.return_value = True
            mock_get_excel_processor.return_value = mock_excel_processor
            
            # Test
            service = StorageService()
            await service.initialize()
            
            result = await service.write_record(sample_game_record, "2025-01-01 12:00:00")
            
            assert result.success
            assert "已写入本地" in result.message
            assert "Google Sheets 同步失败" in result.message
    
    @pytest.mark.asyncio
    async def test_write_record_validation_failure(self, mock_config):
        """Test record writing with validation failure"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager:
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            # Invalid record data
            invalid_data = {
                'person': '',  # Empty person name
                'venue': 'TestVenue',
                'game': 'BJ',
                'principal': -100,  # Negative principal
                'profit': 500
            }
            
            service = StorageService()
            await service.initialize()
            
            result = await service.write_record(invalid_data, "2025-01-01 12:00:00")
            
            assert not result.success
            assert "人员名称不能为空" in result.message
    
    @pytest.mark.asyncio
    async def test_load_records(self, mock_config):
        """Test loading records"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager, \
             patch('storage_service.get_excel_processor') as mock_get_excel_processor:
            
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            # Mock loaded records
            mock_records = [
                {'person': '张三', 'venue': 'TestVenue', 'profit': 500},
                {'person': '李四', 'venue': 'AnotherVenue', 'profit': 300}
            ]
            
            mock_excel_processor = AsyncMock()
            mock_excel_processor.load_data_async.return_value = mock_records
            mock_get_excel_processor.return_value = mock_excel_processor
            
            # Test
            service = StorageService()
            await service.initialize()
            
            records = await service.load_records()
            
            assert len(records) == 2
            assert records[0]['person'] == '张三'
            assert records[1]['person'] == '李四'
            
            mock_excel_processor.load_data_async.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_search_records(self, mock_config):
        """Test searching records"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager, \
             patch('storage_service.get_excel_processor') as mock_get_excel_processor:
            
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            # Mock all records
            mock_all_records = [
                {'person': '张三', 'venue': 'TestVenue', 'game': 'BJ', 'profit': 500},
                {'person': '李四', 'venue': 'TestVenue', 'game': 'UTH', 'profit': 300},
                {'person': '王五', 'venue': 'AnotherVenue', 'game': 'BJ', 'profit': 200}
            ]
            
            mock_excel_processor = AsyncMock()
            mock_excel_processor.load_data_async.return_value = mock_all_records
            mock_get_excel_processor.return_value = mock_excel_processor
            
            # Test
            service = StorageService()
            await service.initialize()
            
            # Search for "张三" and "BJ"
            matches = await service.search_records(['张三', 'BJ'])
            
            assert len(matches) == 1
            assert matches[0]['person'] == '张三'
            assert matches[0]['game'] == 'BJ'
    
    @pytest.mark.asyncio
    async def test_rebate_config_operations(self, mock_config, sample_rebate_config):
        """Test rebate configuration operations"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager, \
             patch('storage_service.get_rebate_manager') as mock_get_rebate_manager:
            
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            mock_rebate_manager = AsyncMock()
            mock_rebate_manager.load_local_config.return_value = sample_rebate_config
            mock_rebate_manager.save_local_config.return_value = True
            mock_rebate_manager.update_google_sheet.return_value = (True, "更新成功")
            mock_get_rebate_manager.return_value = mock_rebate_manager
            
            # Test
            service = StorageService()
            await service.initialize()
            
            # Test load config
            config = await service.load_rebate_config()
            assert config == sample_rebate_config
            
            # Test save config
            success = await service.save_rebate_config(sample_rebate_config)
            assert success
            
            # Test update ratio
            result = await service.update_rebate_ratio("TestVenue", "张三", 0.18)
            assert result.success
            assert "设置成功" in result.message
    
    @pytest.mark.asyncio
    async def test_salary_config_operations(self, mock_config, sample_salary_config):
        """Test salary configuration operations"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager, \
             patch('storage_service.get_salary_manager') as mock_get_salary_manager:
            
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            mock_salary_manager = AsyncMock()
            mock_salary_manager.load_local_config.return_value = sample_salary_config
            mock_salary_manager.save_local_config.return_value = True
            mock_salary_manager.update_google_sheet.return_value = (True, "更新成功")
            mock_get_salary_manager.return_value = mock_salary_manager
            
            # Test
            service = StorageService()
            await service.initialize()
            
            # Test load config
            config = await service.load_salary_config()
            assert config == sample_salary_config
            
            # Test save config
            success = await service.save_salary_config(sample_salary_config)
            assert success
            
            # Test update rule
            result = await service.update_salary_rule(0.2, "BJ", 500, 1500, 35)
            assert result.success
            assert "设置成功" in result.message
    
    @pytest.mark.asyncio
    async def test_service_stats(self, mock_config):
        """Test service statistics"""
        with patch('storage_service.get_file_manager') as mock_get_file_manager:
            mock_file_manager = AsyncMock()
            mock_file_manager.create_excel_async.return_value = True
            mock_get_file_manager.return_value = mock_file_manager
            
            service = StorageService()
            await service.initialize()
            
            stats = service.get_service_stats()
            
            assert 'initialized' in stats
            assert 'error_stats' in stats
            assert 'excel_file' in stats
            assert 'google_sheet_name' in stats
            assert stats['initialized'] is True

@pytest.mark.integration
class TestStorageServiceSingleton:
    """Test storage service singleton behavior"""
    
    @pytest.mark.asyncio
    async def test_get_storage_service_singleton(self, mock_config):
        """Test that get_storage_service returns singleton"""
        with patch('storage_service.StorageService') as MockStorageService:
            mock_instance = AsyncMock()
            mock_instance.initialize.return_value = True
            MockStorageService.return_value = mock_instance
            
            # First call
            service1 = await get_storage_service()
            
            # Second call
            service2 = await get_storage_service()
            
            # Should be the same instance
            assert service1 is service2
            
            # StorageService should only be instantiated once
            MockStorageService.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_service_initialization_failure(self, mock_config):
        """Test service initialization failure handling"""
        with patch('storage_service.StorageService') as MockStorageService:
            mock_instance = AsyncMock()
            mock_instance.initialize.side_effect = Exception("Initialization failed")
            MockStorageService.return_value = mock_instance
            
            with pytest.raises(Exception, match="Initialization failed"):
                await get_storage_service()
