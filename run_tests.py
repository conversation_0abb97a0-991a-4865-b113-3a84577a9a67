#!/usr/bin/env python3
"""
Test runner script with different test suites
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print("Output:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print("Error:", e.stderr)
        if e.stdout:
            print("Output:", e.stdout)
        return False
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0]}")
        print("Please install the required dependencies")
        return False

def install_test_dependencies():
    """Install test dependencies"""
    dependencies = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "pytest-xdist>=3.0.0",
        "pytest-timeout>=2.1.0",
        "coverage>=7.0.0"
    ]
    
    cmd = [sys.executable, "-m", "pip", "install"] + dependencies
    return run_command(cmd, "Installing test dependencies")

def run_unit_tests():
    """Run unit tests only"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/unit/",
        "-m", "unit",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "Running unit tests")

def run_integration_tests():
    """Run integration tests only"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/integration/",
        "-m", "integration",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "Running integration tests")

def run_all_tests():
    """Run all tests"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "Running all tests")

def run_fast_tests():
    """Run fast tests only (exclude slow and network tests)"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-m", "not slow and not network",
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, "Running fast tests")

def run_coverage_tests():
    """Run tests with coverage report"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "--cov=.",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-report=xml",
        "--tb=short",
        "-v"
    ]
    success = run_command(cmd, "Running tests with coverage")
    
    if success:
        print("\n📊 Coverage report generated:")
        print("• HTML report: htmlcov/index.html")
        print("• XML report: coverage.xml")
    
    return success

def run_specific_test(test_path):
    """Run a specific test file or test function"""
    cmd = [
        sys.executable, "-m", "pytest",
        test_path,
        "--tb=short",
        "-v"
    ]
    return run_command(cmd, f"Running specific test: {test_path}")

def lint_code():
    """Run code linting"""
    print("\n🔍 Running code linting...")
    
    # Try to run flake8
    flake8_cmd = [sys.executable, "-m", "flake8", ".", "--max-line-length=100", "--ignore=E203,W503"]
    flake8_success = run_command(flake8_cmd, "Running flake8")
    
    # Try to run black check
    black_cmd = [sys.executable, "-m", "black", "--check", "--diff", "."]
    black_success = run_command(black_cmd, "Running black check")
    
    return flake8_success and black_success

def format_code():
    """Format code with black"""
    cmd = [sys.executable, "-m", "black", "."]
    return run_command(cmd, "Formatting code with black")

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("\n📦 Checking dependencies...")
    
    required_modules = [
        "pytest",
        "pytest_asyncio",
        "pytest_cov",
        "coverage"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} is installed")
        except ImportError:
            print(f"❌ {module} is missing")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {', '.join(missing_modules)}")
        print("Run with --install-deps to install them")
        return False
    
    print("✅ All dependencies are installed")
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test runner for the Telegram bot project")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--fast", action="store_true", help="Run fast tests only")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    parser.add_argument("--lint", action="store_true", help="Run code linting")
    parser.add_argument("--format", action="store_true", help="Format code")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--check-deps", action="store_true", help="Check dependencies")
    parser.add_argument("--test", type=str, help="Run specific test file or function")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    
    args = parser.parse_args()
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    success = True
    
    if args.install_deps:
        success &= install_test_dependencies()
    
    if args.check_deps:
        success &= check_dependencies()
    
    if args.format:
        success &= format_code()
    
    if args.lint:
        success &= lint_code()
    
    if args.unit:
        success &= run_unit_tests()
    elif args.integration:
        success &= run_integration_tests()
    elif args.fast:
        success &= run_fast_tests()
    elif args.coverage:
        success &= run_coverage_tests()
    elif args.test:
        success &= run_specific_test(args.test)
    elif args.all:
        success &= run_all_tests()
    else:
        # Default: run fast tests
        print("No specific test suite specified, running fast tests...")
        success &= run_fast_tests()
    
    if success:
        print("\n🎉 All operations completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some operations failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
