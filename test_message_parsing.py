#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试消息解析功能
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

def test_message_parsing():
    """测试用户原始消息的解析"""

    # 用户的原始消息
    test_message = """日期：9月24日
人员：吴风
场子： ChamadaP
游戏：俄罗斯
卡号:  1277182
本金：1400
点码:  3475
工资：0
输反：0
赢亏:  2075
备注：中了个炸弹。盈利里包含9月18日未记录的输返75"""

    print("=== Message Parsing Test ===")
    print("Test message:")
    print(test_message)
    print("\n" + "="*50)

    try:
        from Parser import parse_message

        # 解析消息
        parsed, missing = parse_message(test_message)

        print("Parsing Result:")
        print(f"Missing fields: {missing}")
        print("\nParsed data:")
        for key, value in parsed.items():
            if value is not None:
                print(f"  {key}: {value}")

        # 检查关键字段
        print("\n" + "="*50)
        print("Key field verification:")
        print(f"起始本金 (expected 1400): {parsed.get('起始本金', 'NOT FOUND')}")
        print(f"盈利 (expected 2075): {parsed.get('盈利', 'NOT FOUND')}")
        print(f"人员 (expected 吴风): {parsed.get('人员', 'NOT FOUND')}")

        # 验证修复是否成功
        success = (
            parsed.get('起始本金') == 1400 and
            parsed.get('盈利') == 2075 and
            parsed.get('人员') == '吴风'
        )

        print(f"\nFIX VERIFICATION: {'SUCCESS' if success else 'FAILED'}")

        if success:
            print("The field mapping fix is working correctly!")
        else:
            print("The field mapping fix needs more work.")

    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_message_parsing()