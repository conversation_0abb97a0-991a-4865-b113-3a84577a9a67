#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 设置UTF-8编码输出
sys.stdout.reconfigure(encoding='utf-8')

def test_all():
    """简化的兼容性测试"""

    print("=== 字段动态化兼容性测试 ===")

    try:
        # 1. 测试alias_manager API
        print("1. 测试alias_manager...")
        from alias_manager import get_all_field_names, get_numeric_field_names, is_numeric_field

        all_fields = get_all_field_names()
        numeric_fields = get_numeric_field_names()

        print(f"   所有字段: {all_fields}")
        print(f"   数字字段: {numeric_fields}")
        print(f"   起始本金是数字字段: {is_numeric_field('起始本金')}")
        print("   [PASS] alias_manager API")

        # 2. 测试data_models
        print("2. 测试data_models...")
        from data_models import FieldConstants
        field_constants = FieldConstants()

        print(f"   延迟加载字段数: {len(field_constants.FIELDS_CHINESE)}")
        print(f"   延迟加载数字字段数: {len(field_constants.NUMERIC_FIELDS)}")
        print("   [PASS] data_models延迟初始化")

        # 3. 测试Parser
        print("3. 测试Parser...")
        from Parser import parse_message

        test_msg = '''工作日期：12月24日
人员：吴风
场子：GB
游戏：BJ
卡号：12345
起始本金：1400
点码：10000
工资：50
输反：0
盈利：2075
备注：测试'''

        fields, missing = parse_message(test_msg, strict=False)
        print(f"   解析字段数: {len(fields)}")
        print(f"   起始本金值: {fields.get('起始本金')}")
        print(f"   盈利值: {fields.get('盈利')}")
        print("   [PASS] Parser动态字段")

        # 4. 测试向后兼容性
        print("4. 测试向后兼容性...")
        from data_models import FIELDS_CHINESE, NUMERIC_FIELDS, REQUIRED_FIELDS

        print(f"   FIELDS_CHINESE可访问: {len(FIELDS_CHINESE)} 个字段")
        print(f"   NUMERIC_FIELDS可访问: {len(NUMERIC_FIELDS)} 个字段")
        print(f"   REQUIRED_FIELDS可访问: {len(REQUIRED_FIELDS)} 个字段")
        print("   [PASS] 向后兼容性")

        print("\n=== 所有测试通过! ===")
        return True

    except Exception as e:
        print(f"\n[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_all()
    print(f"\n测试结果: {'成功' if success else '失败'}")