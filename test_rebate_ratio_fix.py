#!/usr/bin/env python3
"""
测试 rebate 比例查找修复的脚本
验证场馆特定比例是否正确查找
"""

import json
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_get_ratio(config: Dict[str, Any], venue: str, person: str) -> float:
    """测试版本的 get_ratio 函数"""
    try:
        # 查找具体的场馆配置
        if venue in config:
            venue_config = config[venue]
            
            # 如果场馆配置是字典，查找具体人员或默认比例
            if isinstance(venue_config, dict):
                # 1. 优先查找具体人员配置
                if person in venue_config:
                    person_ratio = venue_config[person]
                    if isinstance(person_ratio, (int, float)):
                        logger.debug(f"找到具体人员配置: {venue}/{person} = {person_ratio}")
                        return float(person_ratio)
                
                # 2. 查找场馆默认比例（"*" 或 "默认比例"）
                for default_key in ["默认比例", "*"]:
                    if default_key in venue_config:
                        venue_default = venue_config[default_key]
                        if isinstance(venue_default, (int, float)):
                            logger.debug(f"找到场馆默认配置: {venue}/{default_key} = {venue_default}")
                            return float(venue_default)
            
            # 如果场馆配置直接是数字（旧格式兼容）
            elif isinstance(venue_config, (int, float)):
                logger.debug(f"找到场馆直接配置: {venue} = {venue_config}")
                return float(venue_config)
        
        # 3. 返回全局默认比例
        default_ratio = config.get("默认比例", 0.1)
        logger.debug(f"使用全局默认比例: {default_ratio}")
        return float(default_ratio)
        
    except (ValueError, TypeError) as e:
        logger.warning(f"获取 rebate 比例失败: {e}")
        return 0.1

def create_test_config():
    """创建测试配置"""
    return {
        "Mandarin": {
            "默认比例": 0.2
        },
        "Sheraton": {
            "默认比例": 0.2
        },
        "Billionaire": {
            "默认比例": 0.2
        },
        "Otium": {
            "*": 0.2,
            "默认比例": 0.2
        },
        "Cosmos": {
            "默认比例": 0.1
        },
        "TestVenue": {
            "Alice": 0.3,
            "默认比例": 0.15
        },
        "OldFormatVenue": 0.25,  # 旧格式兼容测试
        "默认比例": 0.1
    }

def run_tests():
    """运行测试用例"""
    print("🧪 Rebate 比例查找修复测试")
    print("=" * 50)
    
    config = create_test_config()
    
    # 测试用例
    test_cases = [
        # (venue, person, expected_ratio, description)
        ("Otium", "任意用户", 0.2, "Otium 场馆默认比例（使用 * 键）"),
        ("Mandarin", "任意用户", 0.2, "Mandarin 场馆默认比例"),
        ("TestVenue", "Alice", 0.3, "TestVenue 特定用户 Alice"),
        ("TestVenue", "Bob", 0.15, "TestVenue 默认用户 Bob"),
        ("OldFormatVenue", "任意用户", 0.25, "旧格式场馆配置"),
        ("不存在的场馆", "任意用户", 0.1, "不存在场馆使用全局默认"),
        ("Cosmos", "任意用户", 0.1, "Cosmos 场馆默认比例"),
    ]
    
    print(f"📋 测试配置:")
    print(json.dumps(config, ensure_ascii=False, indent=2))
    print()
    
    print("🔍 测试结果:")
    print("-" * 50)
    
    all_passed = True
    
    for i, (venue, person, expected, description) in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {description}")
        print(f"  输入: venue='{venue}', person='{person}'")
        print(f"  期望: {expected}")
        
        actual = test_get_ratio(config, venue, person)
        print(f"  实际: {actual}")
        
        if abs(actual - expected) < 0.001:  # 浮点数比较
            print(f"  结果: ✅ 通过")
        else:
            print(f"  结果: ❌ 失败")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！Rebate 比例查找修复成功。")
    else:
        print("💥 部分测试失败，需要进一步检查。")
    
    return all_passed

def test_with_real_config():
    """使用真实配置文件测试"""
    print("\n🔍 使用真实配置文件测试")
    print("-" * 30)
    
    try:
        with open("rebate_config.json", 'r', encoding='utf-8') as f:
            real_config = json.load(f)
        
        print("📁 真实配置文件加载成功")
        print(f"配置包含 {len(real_config)} 个项目")
        
        # 测试 Otium 场馆
        otium_ratio = test_get_ratio(real_config, "Otium", "测试用户")
        print(f"\n🎯 Otium 场馆测试:")
        print(f"  查找结果: {otium_ratio}")
        print(f"  期望结果: 0.2")
        
        if abs(otium_ratio - 0.2) < 0.001:
            print(f"  ✅ Otium 场馆比例查找正确")
        else:
            print(f"  ❌ Otium 场馆比例查找错误")
            
        # 显示 Otium 的实际配置
        if "Otium" in real_config:
            print(f"  Otium 实际配置: {real_config['Otium']}")
        else:
            print(f"  ⚠️ 配置文件中未找到 Otium")
            
    except FileNotFoundError:
        print("⚠️ rebate_config.json 文件不存在，跳过真实配置测试")
    except Exception as e:
        print(f"❌ 真实配置测试失败: {e}")

def main():
    """主函数"""
    success = run_tests()
    test_with_real_config()
    
    print(f"\n📊 总结:")
    if success:
        print("✅ 修复验证成功，rebate 比例查找逻辑已正确实现")
        print("💡 建议:")
        print("  1. 重启 Telegram Bot 应用")
        print("  2. 测试 Otium 场馆的消息处理")
        print("  3. 验证工资计算是否使用正确的比例")
    else:
        print("❌ 修复验证失败，需要进一步调试")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
