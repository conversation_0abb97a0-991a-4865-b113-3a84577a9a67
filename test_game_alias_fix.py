#!/usr/bin/env python3
"""
测试游戏类型别名转换修复
验证月度工资检查模块中的游戏类型别名转换是否正常工作
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接加载别名数据，避免依赖telegram模块
def load_game_aliases():
    """直接从aliases.json加载游戏别名"""
    try:
        with open('aliases.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('games', {})
    except Exception as e:
        print(f"加载别名文件失败: {e}")
        return {}

def resolve_with_aliases_simple(value, alias_dict, fallback=None):
    """简化版别名解析函数"""
    if not value:
        return fallback

    value_clean = value.strip().lower()
    if not value_clean:
        return fallback

    # 构建映射表
    mapping = {std.lower(): std for std in alias_dict}

    for std, aliases in alias_dict.items():
        for alias in aliases:
            clean_alias = alias.strip().lower()
            if clean_alias:
                mapping[clean_alias] = std

    if value_clean in mapping:
        return mapping[value_clean]

    return value_clean

GAME_ALIASES = load_game_aliases()

def test_game_alias_resolution():
    """测试游戏类型别名解析"""
    print("🎮 测试游戏类型别名解析")
    print("=" * 50)

    # 测试用例：小写的uth应该被解析为大写的UTH
    test_cases = [
        ("uth", "UTH"),
        ("UTH", "UTH"),
        ("bj", "BJ"),
        ("BJ", "BJ"),
        ("21点", "BJ"),
        ("俄", "俄罗斯"),
        ("ru", "俄罗斯"),
        ("百家", "百家乐"),
        ("baccarat", "百家乐"),
        ("unknown_game", "unknown_game")  # 未知游戏应该返回原值
    ]

    for input_game, expected_output in test_cases:
        resolved_game = resolve_with_aliases_simple(input_game, GAME_ALIASES, input_game)
        status = "✅" if resolved_game == expected_output else "❌"
        print(f"{status} {input_game:15} -> {resolved_game:15} (期望: {expected_output})")

    print()

def test_process_excel_row_simulation():
    """模拟process_excel_row函数中的游戏类型处理"""
    print("📊 模拟Excel行处理中的游戏类型转换")
    print("=" * 50)

    # 模拟从Excel读取的数据（小写游戏类型）
    excel_data = [
        {"person": "吴风", "venue": "Merit北塞", "game": "uth", "profit": -1600},
        {"person": "俊", "venue": "Iveria", "game": "bj", "profit": 500},
        {"person": "敏", "venue": "Otium", "game": "俄", "profit": 200},
    ]

    for data in excel_data:
        original_game = data["game"]

        # 应用别名转换（这是我们添加的修复）
        resolved_game = resolve_with_aliases_simple(original_game, GAME_ALIASES, original_game)

        print(f"人员: {data['person']}, 场子: {data['venue']}")
        print(f"  原始游戏类型: '{original_game}' -> 转换后: '{resolved_game}'")
        print(f"  盈利: {data['profit']}")
        print()

def main():
    """主测试函数"""
    print("🔧 游戏类型别名转换修复测试")
    print("=" * 60)
    print()

    try:
        # 测试别名解析
        test_game_alias_resolution()

        # 模拟Excel行处理
        test_process_excel_row_simulation()

        print("✅ 所有测试完成")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
