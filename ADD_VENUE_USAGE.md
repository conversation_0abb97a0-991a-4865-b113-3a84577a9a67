# Add Venue 命令使用说明

## 功能概述

`/add_venue` 命令用于向系统中添加新的venue（场子），并自动更新 `aliases.json` 文件。添加成功后，系统会引导用户设置该venue的rebate比例。

## 命令格式

```
/add_venue venue名称 别名1 [别名2] [别名3] ...
```

### 参数说明

- **venue名称**: 新venue的标准名称（必需）
- **别名1**: 第一个别名（必需）
- **别名2, 别名3...**: 额外的别名（可选）

⚠️ **注意**: venue名称和至少一个别名是必需的

## 使用示例

### 基本用法
```
/add_venue 新赌场 nc newcasino 新场
```
这将添加一个名为"新赌场"的venue，包含三个别名："nc"、"newcasino"、"新场"

### 更多示例
```
/add_venue 皇家赌场 royal 皇家 rc
/add_venue 金沙娱乐 sands 金沙 js
/add_venue 威尼斯人 venetian 威尼斯 vnsr
```

## 功能特性

### 1. 冲突检测
系统会自动检测以下冲突：
- venue名称与现有venue名称冲突
- venue名称与现有别名冲突
- 新别名与现有venue名称冲突
- 新别名与现有别名冲突

### 2. 输入验证
- 验证venue名称和别名的有效性
- 过滤特殊字符和恶意输入
- 检查空值和空白字符

### 3. 自动引导设置rebate
添加venue成功后，系统会自动提示用户设置rebate比例：

```
✅ venue添加成功！

🏢 venue名称：新赌场
🏷️ 别名：nc、newcasino、新场

📝 aliases.json 文件已更新

🔧 **下一步：设置rebate比例**
请使用以下命令为新venue设置输返比例：
`/set_rebate 新赌场 比例`

💡 示例：
• `/set_rebate 新赌场 0.1` (设置默认10%输返)
• `/set_rebate 新赌场 0.2` (设置默认20%输返)

⚠️ 注意：比例应为0-1之间的小数（如0.1表示10%）
```

## 错误处理

### 常见错误及解决方案

1. **格式错误**
   ```
   ❗ 格式错误：
   📋 格式：`/add_venue venue名称 别名1 [别名2] [别名3] ...`
   💡 示例：`/add_venue 新赌场 nc newcasino 新场`
   
   ⚠️ 注意：venue名称和至少一个别名是必需的
   ```

2. **venue已存在**
   ```
   ⚠️ venue '新赌场' 已存在
   当前别名：["nc", "newcasino", "新场"]
   
   💡 如需修改别名，请手动编辑 aliases.json 文件
   ```

3. **名称冲突**
   ```
   ❌ 发现冲突：
   • 别名 'nc' 与现有venue '老赌场' 冲突
   • venue名称 '新赌场' 与 '其他赌场' 的别名冲突
   
   💡 请使用不同的名称或别名
   ```

4. **文件错误**
   ```
   ❌ 找不到 aliases.json 文件
   请确保文件存在于：aliases.json
   ```

## 工作流程

1. **输入验证**: 检查命令格式和参数有效性
2. **冲突检测**: 检查venue名称和别名是否与现有数据冲突
3. **文件更新**: 将新venue添加到 `aliases.json` 文件
4. **成功反馈**: 显示添加结果并引导设置rebate比例

## 注意事项

1. **备份**: 系统会自动备份 `aliases.json` 文件（如果测试脚本运行）
2. **权限**: 确保机器人有读写 `aliases.json` 文件的权限
3. **格式**: 添加的venue会立即生效，可用于消息解析
4. **rebate设置**: 建议添加venue后立即设置rebate比例，否则会使用系统默认值

## 相关命令

- `/set_rebate venue名称 比例` - 设置venue的默认rebate比例
- `/set_rebate venue名称 人员 比例` - 设置特定人员在该venue的rebate比例
- `/help` - 查看完整帮助文档
- `/help_simple` - 查看简化帮助文档

## 技术实现

该功能通过以下方式实现：
1. 使用 `InputValidator` 进行输入验证和清理
2. 直接操作 `aliases.json` 文件进行数据更新
3. 提供详细的错误信息和用户引导
4. 集成到现有的命令系统中

添加venue后，新的别名会立即在消息解析中生效，用户可以使用任何定义的别名来引用该venue。
