#!/usr/bin/env python3
"""
修复 Windows 隐藏文件问题的工具脚本
专门解决 Windows Server 2012 R2 上配置文件被设置为隐藏导致的权限问题
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WindowsFileAttributeFixer:
    """Windows 文件属性修复器"""
    
    # 需要修复的配置文件
    CONFIG_FILES = [
        'rebate_config.json',
        'salary_config.json',
        'mysheetapp.json',
        'mysheetapp_new.json',
        '.env',
        'message_cache.txt',
        'retry_log.txt'
    ]
    
    def __init__(self):
        self.fixed_files = []
        self.failed_files = []
        self.skipped_files = []
    
    def check_windows_version(self) -> str:
        """检查 Windows 版本"""
        try:
            result = subprocess.run(['ver'], shell=True, capture_output=True, text=True)
            version_info = result.stdout.strip()
            logger.info(f"Windows 版本: {version_info}")
            return version_info
        except Exception as e:
            logger.warning(f"无法获取 Windows 版本信息: {e}")
            return "Unknown"
    
    def get_file_attributes(self, filepath: str) -> dict:
        """获取文件属性信息"""
        try:
            result = subprocess.run(
                ['attrib', filepath], 
                capture_output=True, 
                text=True, 
                shell=True
            )
            
            if result.returncode == 0:
                attrs_line = result.stdout.strip()
                attrs = {
                    'hidden': 'H' in attrs_line,
                    'readonly': 'R' in attrs_line,
                    'system': 'S' in attrs_line,
                    'archive': 'A' in attrs_line,
                    'raw_output': attrs_line
                }
                return attrs
            else:
                return {'error': result.stderr.strip()}
        except Exception as e:
            return {'error': str(e)}
    
    def fix_file_attributes(self, filepath: str) -> bool:
        """修复单个文件的属性"""
        try:
            if not os.path.exists(filepath):
                logger.info(f"文件不存在，跳过: {filepath}")
                self.skipped_files.append(filepath)
                return True
            
            # 获取当前属性
            current_attrs = self.get_file_attributes(filepath)
            if 'error' in current_attrs:
                logger.error(f"无法获取文件属性 {filepath}: {current_attrs['error']}")
                self.failed_files.append(filepath)
                return False
            
            logger.info(f"文件当前属性 {filepath}: {current_attrs['raw_output']}")
            
            # 检查是否需要修复
            needs_fix = current_attrs.get('hidden', False) or current_attrs.get('readonly', False)
            
            if not needs_fix:
                logger.info(f"文件属性正常，无需修复: {filepath}")
                return True
            
            # 移除隐藏和只读属性
            logger.info(f"修复文件属性: {filepath}")
            result = subprocess.run(
                ['attrib', '-H', '-R', filepath],
                capture_output=True,
                text=True,
                shell=True
            )
            
            if result.returncode == 0:
                # 验证修复结果
                new_attrs = self.get_file_attributes(filepath)
                if 'error' not in new_attrs:
                    logger.info(f"文件属性修复成功 {filepath}: {new_attrs['raw_output']}")
                    self.fixed_files.append(filepath)
                    return True
                else:
                    logger.error(f"修复后验证失败 {filepath}: {new_attrs['error']}")
                    self.failed_files.append(filepath)
                    return False
            else:
                logger.error(f"修复文件属性失败 {filepath}: {result.stderr.strip()}")
                self.failed_files.append(filepath)
                return False
                
        except Exception as e:
            logger.error(f"修复文件属性异常 {filepath}: {e}")
            self.failed_files.append(filepath)
            return False
    
    def fix_all_config_files(self) -> bool:
        """修复所有配置文件"""
        logger.info("开始修复配置文件属性...")
        
        success_count = 0
        total_count = len(self.CONFIG_FILES)
        
        for filepath in self.CONFIG_FILES:
            logger.info(f"\n处理文件: {filepath}")
            logger.info("-" * 40)
            
            if self.fix_file_attributes(filepath):
                success_count += 1
        
        # 统计结果
        logger.info(f"\n修复结果统计:")
        logger.info(f"总文件数: {total_count}")
        logger.info(f"修复成功: {len(self.fixed_files)}")
        logger.info(f"修复失败: {len(self.failed_files)}")
        logger.info(f"跳过文件: {len(self.skipped_files)}")
        
        if self.fixed_files:
            logger.info(f"已修复的文件: {', '.join(self.fixed_files)}")
        
        if self.failed_files:
            logger.warning(f"修复失败的文件: {', '.join(self.failed_files)}")
        
        if self.skipped_files:
            logger.info(f"跳过的文件: {', '.join(self.skipped_files)}")
        
        return len(self.failed_files) == 0
    
    def create_batch_script(self) -> str:
        """创建批处理脚本用于定期维护"""
        batch_content = f"""@echo off
REM Windows 配置文件属性维护脚本
REM 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

echo 正在修复配置文件属性...

"""
        
        for filepath in self.CONFIG_FILES:
            batch_content += f'if exist "{filepath}" (\n'
            batch_content += f'    echo 修复文件: {filepath}\n'
            batch_content += f'    attrib -H -R "{filepath}"\n'
            batch_content += f')\n\n'
        
        batch_content += """echo 配置文件属性修复完成！
pause
"""
        
        batch_file = "fix_config_attributes.bat"
        try:
            with open(batch_file, 'w', encoding='gbk') as f:
                f.write(batch_content)
            logger.info(f"批处理脚本已创建: {batch_file}")
            return batch_file
        except Exception as e:
            logger.error(f"创建批处理脚本失败: {e}")
            return None

def main():
    """主函数"""
    print("🔧 Windows 配置文件属性修复工具")
    print("=" * 50)
    print("专门解决 Windows Server 2012 R2 配置文件隐藏属性问题")
    print()
    
    if os.name != 'nt':
        print("❌ 此工具仅适用于 Windows 系统")
        sys.exit(1)
    
    fixer = WindowsFileAttributeFixer()
    
    # 显示系统信息
    print("📋 系统信息:")
    version_info = fixer.check_windows_version()
    print()
    
    # 修复文件属性
    print("🔧 开始修复配置文件属性...")
    print("=" * 50)
    
    success = fixer.fix_all_config_files()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有配置文件属性修复完成！")
    else:
        print("⚠️ 部分文件修复失败，请检查日志")
    
    # 创建维护脚本
    print("\n📝 创建维护脚本...")
    batch_file = fixer.create_batch_script()
    if batch_file:
        print(f"✅ 维护脚本已创建: {batch_file}")
        print("   可以定期运行此脚本来维护文件属性")
    
    print("\n💡 建议:")
    print("1. 重启 Telegram Bot 应用")
    print("2. 测试 /load_rebate_config 和 /load_salary_config 命令")
    print("3. 如果问题持续，请运行维护脚本")
    print("4. 考虑将维护脚本添加到定时任务中")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
