#!/usr/bin/env python3
"""
错误处理策略模块
提供统一的错误处理策略和恢复机制
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, Union, List
from telegram import Update
from telegram.ext import ContextTypes
from datetime import datetime, timedelta

from exceptions import (
    BaseCustomException, ErrorSeverity, ErrorCategory,
    ValidationError, NetworkError, GoogleSheetsError,
    FileSystemError, BusinessLogicError, SystemError,
    exception_tracker, get_user_friendly_message,
    is_recoverable_error, convert_standard_exception, ExceptionTracker
)

logger = logging.getLogger(__name__)


class RetryConfig:
    """重试配置"""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter


class ErrorHandlingStrategy:
    """错误处理策略基类"""
    
    def __init__(self, name: str):
        self.name = name
    
    async def handle_error(
        self,
        error: BaseCustomException,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """处理错误的抽象方法"""
        raise NotImplementedError
    
    def should_retry(self, error: BaseCustomException, attempt: int) -> bool:
        """判断是否应该重试"""
        return (
            error.recoverable and
            attempt < 3 and
            error.category in [ErrorCategory.NETWORK, ErrorCategory.EXTERNAL_API]
        )


class SilentFailureStrategy(ErrorHandlingStrategy):
    """静默失败策略（仅记录日志）"""
    
    def __init__(self):
        super().__init__("silent_failure")
    
    async def handle_error(
        self,
        error: BaseCustomException,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """静默处理错误，仅记录日志"""
        logger.warning(f"Silent failure: {error.message}", extra=error.to_dict())
        
        return {
            'handled': True,
            'action': 'logged',
            'user_notified': False,
            'retry_scheduled': False
        }


class UserNotificationStrategy(ErrorHandlingStrategy):
    """用户通知策略"""
    
    def __init__(self, update: Update = None):
        super().__init__("user_notification")
        self.update = update
    
    async def handle_error(
        self,
        error: BaseCustomException,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """向用户发送错误通知"""
        if not self.update or not self.update.message:
            logger.warning("Cannot notify user: no update or message object")
            return {'handled': False, 'reason': 'no_update_object'}
        
        # 根据错误严重程度决定通知内容
        if error.severity in [ErrorSeverity.LOW, ErrorSeverity.MEDIUM]:
            message = f"⚠️ {error.user_message}"
            if error.recoverable:
                message += "\n💡 请稍后重试"
        else:
            message = f"❌ {error.user_message}\n📞 如问题持续，请联系管理员"
        
        try:
            await self.update.message.reply_text(message)
            user_notified = True
        except Exception as e:
            logger.error(f"Failed to notify user: {e}")
            user_notified = False
        
        return {
            'handled': True,
            'action': 'user_notified',
            'user_notified': user_notified,
            'message_sent': message if user_notified else None
        }


class RetryStrategy(ErrorHandlingStrategy):
    """重试策略"""
    
    def __init__(self, retry_config: RetryConfig = None):
        super().__init__("retry")
        self.retry_config = retry_config or RetryConfig()
        self._retry_queue = {}
    
    async def handle_error(
        self,
        error: BaseCustomException,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """处理需要重试的错误"""
        if not self.should_retry(error, context.get('attempt', 1)):
            return {'handled': False, 'reason': 'not_retryable'}
        
        operation_id = context.get('operation_id', f"{error.error_code}_{datetime.now().timestamp()}")
        attempt = context.get('attempt', 1)
        
        # 计算延迟时间
        delay = min(
            self.retry_config.base_delay * (self.retry_config.exponential_base ** (attempt - 1)),
            self.retry_config.max_delay
        )
        
        # 添加抖动
        if self.retry_config.jitter:
            import random
            delay *= (0.5 + random.random())
        
        # 调度重试
        retry_time = datetime.now() + timedelta(seconds=delay)
        self._retry_queue[operation_id] = {
            'retry_time': retry_time,
            'attempt': attempt + 1,
            'context': context,
            'error': error
        }
        
        logger.info(f"Scheduled retry for operation {operation_id} in {delay:.2f} seconds (attempt {attempt + 1})")
        
        return {
            'handled': True,
            'action': 'retry_scheduled',
            'retry_time': retry_time,
            'attempt': attempt + 1,
            'delay': delay
        }
    
    def should_retry(self, error: BaseCustomException, attempt: int) -> bool:
        """判断是否应该重试"""
        if not error.recoverable or attempt >= self.retry_config.max_attempts:
            return False
        
        # 特定类型的错误可以重试
        retryable_categories = [
            ErrorCategory.NETWORK,
            ErrorCategory.EXTERNAL_API,
            ErrorCategory.DATABASE
        ]
        
        return error.category in retryable_categories
    
    def get_pending_retries(self) -> List[Dict[str, Any]]:
        """获取待重试的操作"""
        now = datetime.now()
        return [
            {**retry_info, 'operation_id': op_id}
            for op_id, retry_info in self._retry_queue.items()
            if retry_info['retry_time'] <= now
        ]
    
    def remove_retry(self, operation_id: str):
        """移除重试操作"""
        self._retry_queue.pop(operation_id, None)


class CacheStrategy(ErrorHandlingStrategy):
    """缓存策略（用于网络失败时缓存操作）"""
    
    def __init__(self):
        super().__init__("cache")
        self._cached_operations = []
    
    async def handle_error(
        self,
        error: BaseCustomException,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """缓存失败的操作"""
        if not self._should_cache(error):
            return {'handled': False, 'reason': 'not_cacheable'}
        
        operation_data = {
            'timestamp': datetime.now(),
            'operation': context.get('operation'),
            'data': context.get('data'),
            'error': error.to_dict(),
            'retry_count': 0
        }
        
        self._cached_operations.append(operation_data)
        
        logger.info(f"Cached operation due to error: {error.error_code}")
        
        return {
            'handled': True,
            'action': 'cached',
            'cache_size': len(self._cached_operations)
        }
    
    def _should_cache(self, error: BaseCustomException) -> bool:
        """判断是否应该缓存操作"""
        cacheable_categories = [
            ErrorCategory.NETWORK,
            ErrorCategory.EXTERNAL_API
        ]
        return error.category in cacheable_categories and error.recoverable
    
    def get_cached_operations(self) -> List[Dict[str, Any]]:
        """获取缓存的操作"""
        return self._cached_operations.copy()
    
    def clear_cache(self):
        """清空缓存"""
        self._cached_operations.clear()


class CompositeStrategy(ErrorHandlingStrategy):
    """组合策略（同时使用多个策略）"""
    
    def __init__(self, strategies: List[ErrorHandlingStrategy]):
        super().__init__("composite")
        self.strategies = strategies
    
    async def handle_error(
        self,
        error: BaseCustomException,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """依次执行所有策略"""
        results = []
        
        for strategy in self.strategies:
            try:
                result = await strategy.handle_error(error, context)
                results.append({
                    'strategy': strategy.name,
                    'result': result
                })
            except Exception as e:
                logger.error(f"Strategy {strategy.name} failed: {e}")
                results.append({
                    'strategy': strategy.name,
                    'result': {'handled': False, 'error': str(e)}
                })
        
        return {
            'handled': any(r['result'].get('handled', False) for r in results),
            'strategies_executed': results
        }


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self._strategies = {}
        self._default_strategy = SilentFailureStrategy()
        self._category_strategies = {}
        self._severity_strategies = {}
        
        # 注册默认策略
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """注册默认策略"""
        # 为不同错误类别设置不同策略
        self._category_strategies = {
            ErrorCategory.VALIDATION: UserNotificationStrategy(),
            ErrorCategory.AUTHENTICATION: UserNotificationStrategy(),
            ErrorCategory.NETWORK: CompositeStrategy([
                RetryStrategy(),
                CacheStrategy()
            ]),
            ErrorCategory.EXTERNAL_API: CompositeStrategy([
                RetryStrategy(),
                CacheStrategy()
            ]),
            ErrorCategory.FILE_SYSTEM: RetryStrategy(RetryConfig(max_attempts=2)),
            ErrorCategory.BUSINESS_LOGIC: UserNotificationStrategy(),
            ErrorCategory.CONFIGURATION: SilentFailureStrategy(),
            ErrorCategory.SYSTEM: SilentFailureStrategy()
        }
        
        # 为不同严重级别设置策略
        self._severity_strategies = {
            ErrorSeverity.LOW: SilentFailureStrategy(),
            ErrorSeverity.MEDIUM: UserNotificationStrategy(),
            ErrorSeverity.HIGH: UserNotificationStrategy(),
            ErrorSeverity.CRITICAL: UserNotificationStrategy()
        }
    
    def register_strategy(
        self,
        strategy: ErrorHandlingStrategy,
        category: ErrorCategory = None,
        severity: ErrorSeverity = None,
        error_code: str = None
    ):
        """注册错误处理策略"""
        if error_code:
            self._strategies[error_code] = strategy
        elif category:
            self._category_strategies[category] = strategy
        elif severity:
            self._severity_strategies[severity] = strategy
    
    def get_strategy(self, error: BaseCustomException) -> ErrorHandlingStrategy:
        """获取适合的错误处理策略"""
        # 优先级：错误代码 > 错误类别 > 严重级别 > 默认策略
        
        if error.error_code in self._strategies:
            return self._strategies[error.error_code]
        
        if error.category in self._category_strategies:
            return self._category_strategies[error.category]
        
        if error.severity in self._severity_strategies:
            return self._severity_strategies[error.severity]
        
        return self._default_strategy
    
    async def handle_error(
        self,
        error: Union[Exception, BaseCustomException],
        context: Dict[str, Any] = None,
        update: Update = None
    ) -> Dict[str, Any]:
        """处理错误的主入口"""
        # 转换为自定义异常
        if not isinstance(error, BaseCustomException):
            error = convert_standard_exception(error)
        
        # 跟踪异常
        exception_tracker.track_exception(error)
        
        # 更新上下文
        context = context or {}
        if update and 'update' not in context:
            context['update'] = update
        
        # 获取处理策略
        strategy = self.get_strategy(error)
        
        # 如果策略需要 update 对象，更新它
        if isinstance(strategy, UserNotificationStrategy) and update:
            strategy.update = update
        elif isinstance(strategy, CompositeStrategy):
            for sub_strategy in strategy.strategies:
                if isinstance(sub_strategy, UserNotificationStrategy) and update:
                    sub_strategy.update = update
        
        # 执行错误处理
        try:
            result = await strategy.handle_error(error, context)
            
            # 记录处理结果
            logger.info(f"Error handled by strategy '{strategy.name}': {result}")
            
            return {
                'error': error.to_dict(),
                'strategy': strategy.name,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as handling_error:
            # 错误处理本身失败了
            logger.error(f"Error handling failed: {handling_error}", exc_info=True)
            
            # 使用默认策略作为后备
            try:
                fallback_result = await self._default_strategy.handle_error(error, context)
                return {
                    'error': error.to_dict(),
                    'strategy': 'fallback',
                    'result': fallback_result,
                    'handling_error': str(handling_error),
                    'timestamp': datetime.now().isoformat()
                }
            except Exception as fallback_error:
                logger.critical(f"Fallback error handling failed: {fallback_error}", exc_info=True)
                return {
                    'error': error.to_dict(),
                    'strategy': 'none',
                    'result': {'handled': False, 'critical_failure': True},
                    'handling_error': str(handling_error),
                    'fallback_error': str(fallback_error),
                    'timestamp': datetime.now().isoformat()
                }


# 全局错误处理器实例
global_error_handler = ErrorHandler()


# ==================== 便捷函数 ====================

async def handle_telegram_command_error(
    error: Exception,
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    operation_name: str = None
) -> bool:
    """处理 Telegram 命令中的错误"""
    try:
        context_data = {
            'operation': operation_name or 'telegram_command',
            'user_id': update.effective_user.id if update.effective_user else None,
            'chat_id': update.effective_chat.id if update.effective_chat else None,
            'command': update.message.text if update.message else None
        }
        
        result = await global_error_handler.handle_error(error, context_data, update)
        return result['result'].get('handled', False)
        
    except Exception as e:
        logger.critical(f"Critical failure in telegram error handling: {e}", exc_info=True)
        
        # 最后的安全网
        try:
            await update.message.reply_text("系统遇到严重错误，请联系管理员。")
        except:
            pass
        
        return False


async def handle_async_operation_error(
    error: Exception,
    operation_name: str,
    operation_data: Dict[str, Any] = None
) -> Dict[str, Any]:
    """处理异步操作中的错误"""
    context_data = {
        'operation': operation_name,
        'data': operation_data,
        'operation_id': f"{operation_name}_{datetime.now().timestamp()}"
    }
    
    return await global_error_handler.handle_error(error, context_data)


def handle_sync_operation_error(
    error: Exception,
    operation_name: str,
    operation_data: Dict[str, Any] = None,
    return_default: Any = None
) -> Any:
    """处理同步操作中的错误"""
    try:
        # 对于同步操作，我们不能使用异步的错误处理
        # 所以这里使用简化的处理逻辑
        
        if not isinstance(error, BaseCustomException):
            error = convert_standard_exception(error)
        
        # 跟踪异常
        exception_tracker.track_exception(error)
        
        # 记录错误
        logger.error(
            f"Error in sync operation '{operation_name}': {error.message}",
            extra=error.to_dict()
        )
        
        # 根据错误类型决定是否返回默认值
        if error.recoverable:
            return return_default
        else:
            raise error
        
    except Exception as handling_error:
        logger.critical(f"Critical failure in sync error handling: {handling_error}", exc_info=True)
        return return_default


# ==================== 装饰器形式的错误处理 ====================

def with_error_handling(
    operation_name: str = None,
    return_default: Any = None,
    notify_user: bool = True
):
    """错误处理装饰器"""
    
    def decorator(func):
        operation = operation_name or func.__name__
        
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    # 尝试从参数中提取 update 对象
                    update = None
                    for arg in args:
                        if isinstance(arg, Update):
                            update = arg
                            break
                    
                    if update and notify_user:
                        await handle_telegram_command_error(e, update, None, operation)
                    else:
                        await handle_async_operation_error(e, operation)
                    
                    return return_default
            
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    return handle_sync_operation_error(e, operation, return_default=return_default)
            
            return sync_wrapper
    
    return decorator


# ==================== 错误统计和监控函数 ====================

def get_error_statistics() -> Dict[str, Any]:
    """获取错误统计信息"""
    return exception_tracker.get_statistics()


def reset_error_statistics():
    """重置错误统计"""
    global exception_tracker
    exception_tracker = ExceptionTracker()


async def error_health_check() -> Dict[str, Any]:
    """错误处理系统健康检查"""
    stats = get_error_statistics()
    
    # 计算健康指标
    total_errors = stats['total_exceptions']
    critical_errors = stats['severities'].get('critical', 0)
    high_errors = stats['severities'].get('high', 0)
    
    # 健康评分 (0-100)
    health_score = 100
    if total_errors > 0:
        error_ratio = (critical_errors * 3 + high_errors * 2) / total_errors
        health_score = max(0, 100 - int(error_ratio * 50))
    
    return {
        'health_score': health_score,
        'status': 'healthy' if health_score >= 80 else 'warning' if health_score >= 60 else 'critical',
        'total_errors': total_errors,
        'critical_errors': critical_errors,
        'high_errors': high_errors,
        'error_categories': stats['categories'],
        'timestamp': datetime.now().isoformat()
    }